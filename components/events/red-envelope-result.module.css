@layer template {
  @layer template {
    .event {
      --avatar-size: calc(var(--event-font-size) * 2);
      --gift-size: calc(var(--event-font-size) * 2);
      --fallback-text: var(--event-gift-text, var(--event-message-text, var(--text-color)));
      --event-border-radius: calc(var(--1px) * 6);

      padding: calc(var(--event-font-size) * 0.5);
      margin: calc(var(--event-font-size) * 0.25) 0;
      font-family: var(--event-font-family);
      font-size: var(--event-font-size);
      line-height: var(--event-line-height-base);
      /* color: var(--fallback-text); */

      border-radius: var(--event-border-radius);
      color: var(--event-gift-text, #fff);
      background: var(--event-red-envelope-result-bg, var(--red-darker));

      &.dark {
        color: var(--event-red-envelope-result-text, #fff);
        background: var(--event-red-envelope-result-bg, var(--red-50));
      }
    }

    .read {
      opacity: 0.5 !important;
      filter: grayscale(0.8) !important;
    }

    .content {
      display: block;
    }

    .message {
      line-height: var(--event-line-height);
      word-break: break-word;
    }

    .detailsName {
      display: flex;
      justify-content: space-between;

      .toggleButton {
        opacity: 0.6;

        &:hover {
          opacity: 1;
        }
      }
    }

    .rewardsItemsMask {
      &.isMasked {
        overflow: hidden;
        mask-image: linear-gradient(to right, black calc(100% - calc(var(--1px) * 40)), transparent);
      }
    }

    .rewardsItemsWrap {
      display: inline-flex;
      flex-wrap: wrap;
      vertical-align: top;
      gap: 0.125em;

      .isMasked & {
        flex-wrap: nowrap;
      }

      .rewardsItemWrap {
        position: relative;
        display: inline-flex;

        .rewardsItemCount {
          position: absolute;
          top: -0.25em;
          right: -0.25em;
          font-size: calc(var(--1px) * 11);
          line-height: 1;
          background-color: var(--yellow-60);
          padding: calc(var(--1px) * 1) calc(var(--1px) * 3);
          border-radius: 0.5em;
        }

        .rewardsItemPictureWrap {
          img {
            width: var(--event-line-height);
            height: var(--event-line-height);
          }
        }
      }
    }
  }
}
