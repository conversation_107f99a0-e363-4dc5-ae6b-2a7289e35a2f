{"name": "@shikijs/engine-oniguruma", "type": "module", "version": "3.3.0", "description": "Engine for Shi<PERSON> using Oniguruma RegExp engine in WebAssembly", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/engine-oniguruma"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki", "shiki-engine", "<PERSON><PERSON><PERSON><PERSON>"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./wasm-inlined": "./dist/wasm-inlined.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@shikijs/vscode-textmate": "^10.0.2", "@shikijs/types": "3.3.0"}, "devDependencies": {"vscode-oniguruma": "1.7.0"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -cw"}}