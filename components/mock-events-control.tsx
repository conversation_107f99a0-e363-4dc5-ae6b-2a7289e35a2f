import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { IconAdjustmentsHorizontal } from '@tabler/icons-react'

import type { ShouldIncludeEventProps } from '@/types'

import type { LaplaceEventTypes } from '@/lib/event-parsers/types'

import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Slider } from '@/components/ui/slider'
import { SwitchWithLabel } from '@/components/ui/switch-with-label'

export interface MockEventOptions extends ShouldIncludeEventProps {
  speed: number
}

export const DEFAULT_MOCK_OPTIONS: MockEventOptions = {
  speed: 300,
  toast: true,
  gift: true,
  superChat: true,
  mvp: true,
  redEnvelope: true,
  system: true,
  interaction: true,
  entryEffect: true,
  danmaku: true,
  superChatDelete: true,
  liveWarning: true,
  liveCutoff: true,
  likeClick: true,
  lottery: true,
  roomNameUpdate: true,
  roomMuteOn: true,
  roomMuteOff: true,
  watchedUpdate: true,
  likesUpdate: true,
  onlineUpdate: true,
  live: true,
  preparing: true,
  notice: true,
  userBlock: true,
}

interface MockEventsConfigProps {
  options: MockEventOptions
  onChange: (options: MockEventOptions) => void
}

// Helper to check if an event type should be included based on options
export function shouldIncludeEvent(type: LaplaceEventTypes, options: MockEventOptions): boolean {
  switch (type) {
    case 'message':
    case 'effect-message':
      return options.danmaku
    case 'gift':
      return options.gift
    case 'superchat':
      return options.superChat
    case 'mvp':
    case 'toast':
      return options.toast
    case 'interaction':
    case 'like-click':
      return options.interaction
    case 'system':
    case 'live':
    case 'preparing':
    case 'live-start':
    case 'live-end':
    case 'live-warning':
    case 'live-cutoff':
    case 'room-mute-on':
    case 'room-mute-off':
    case 'room-name-update':
      return options.system
    case 'entry-effect':
      return options.entryEffect
    case 'red-envelope-start':
    case 'red-envelope-result':
      return options.redEnvelope
    case 'notice':
      return options.notice
    case 'lottery-start':
    case 'lottery-result':
      return options.lottery
    case 'user-block':
      return options.userBlock
    default:
      return true
  }
}

export const MockEventsControls: React.FC<MockEventsConfigProps> = ({ options, onChange }) => {
  const { t } = useTranslation()

  return (
    <Popover>
      <PopoverTrigger className='flex items-center'>
        <IconAdjustmentsHorizontal size='1.25rem' />
      </PopoverTrigger>
      <PopoverContent className='w-96'>
        <div className='space-y-3'>
          <Slider
            value={[options.speed]}
            min={30}
            max={2000}
            step={10}
            onValueChange={values => {
              onChange({ ...options, speed: values[0]! })
            }}
            marks={[
              { value: 60, label: '原神' },
              { value: 300, label: '大主播' },
              { value: 900, label: '中主播' },
              { value: 1800, label: '小主播' },
            ]}
            // defaultValues={[
            //   {
            //     value: 1000,
            //     label: '默认值',
            //   },
            // ]}
          />

          <div className='grid grid-cols-2 gap-2'>
            <SwitchWithLabel
              label={t('Messages')}
              checked={options.danmaku}
              onChange={e => onChange({ ...options, danmaku: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('SuperChats')}
              checked={options.superChat}
              onChange={e => onChange({ ...options, superChat: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('Gifts')}
              checked={options.gift}
              onChange={e => onChange({ ...options, gift: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('Guards')}
              checked={options.toast}
              onChange={e => onChange({ ...options, toast: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('Interactions')}
              checked={options.interaction}
              onChange={e => onChange({ ...options, interaction: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('System Events')}
              checked={options.system}
              onChange={e => onChange({ ...options, system: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('Red Envelopes')}
              checked={options.redEnvelope}
              onChange={e => onChange({ ...options, redEnvelope: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('Lotteries')}
              checked={options.lottery}
              onChange={e => onChange({ ...options, lottery: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('Notices')}
              checked={options.notice}
              onChange={e => onChange({ ...options, notice: e.target.checked })}
            />
            <SwitchWithLabel
              label={t('User Blocks')}
              checked={options.userBlock}
              onChange={e => onChange({ ...options, userBlock: e.target.checked })}
            />
          </div>

          <Button onClick={() => onChange(DEFAULT_MOCK_OPTIONS)}>{t('Reset Options')}</Button>
        </div>
      </PopoverContent>
    </Popover>
  )
}
