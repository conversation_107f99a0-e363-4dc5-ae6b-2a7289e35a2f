import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useOBSAutoConnect, useOBSConnectionSettings } from '@/lib/obs/storage'
import { useOBSStore } from '@/lib/obs/store'
import type { ConnectionSettings } from '@/lib/obs/utils'

import OptionLabel from '@/components/OptionLabel'
import { Alert } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { InputWithLabel } from '@/components/ui/input-with-label'
import { Label } from '@/components/ui/label'

export const OBSConnectionManager: React.FC = () => {
  const [storedSettings, setStoredSettings] = useOBSConnectionSettings()
  const [autoConnect, setAutoConnect] = useOBSAutoConnect()

  const { t } = useTranslation()

  // Get OBS state and actions from the store
  const { isConnected, isConnecting, connectionError, connect, disconnect } = useOBSStore()

  // Connection settings form state
  const [address, setAddress] = useState('localhost')
  const [port, setPort] = useState<number>(4455)
  const [password, setPassword] = useState('')

  // Load saved settings on component render
  useEffect(() => {
    if (storedSettings) {
      setAddress(storedSettings.address)
      setPort(storedSettings.port)
      if (storedSettings.password) {
        setPassword(storedSettings.password)
      }
    }
  }, [storedSettings])

  // Connect to OBS WebSocket server
  const handleConnect = async () => {
    const settings: ConnectionSettings = {
      address,
      port,
      password: password || undefined,
    }

    // Save settings before attempting connection
    setStoredSettings(settings)

    // Wait for settings to be saved to localStorage
    await new Promise(resolve => setTimeout(resolve, 100))

    // Try to connect
    await connect(settings)
  }

  // Handle manual disconnect
  const handleDisconnect = async () => {
    await disconnect()
  }

  return (
    <div className='space-y-3'>
      <div className='grid gap-x-2 gap-y-3'>
        <div className='grid grid-cols-1 items-start gap-x-2 gap-y-4 @sm:grid-cols-3'>
          <div className='space-y-2'>
            <InputWithLabel
              id='address'
              value={address}
              onChange={e => setAddress(e.target.value)}
              placeholder='localhost'
              label={
                <OptionLabel
                  label={t('obs.address.label', 'Server')}
                  desc={t('obs.address.desc', 'Enter the IP address or domain name of the OBS WebSocket')}
                />
              }
              disabled={isConnected || isConnecting}
            />
          </div>

          <div className='space-y-2'>
            <InputWithLabel
              type='number'
              id='port'
              value={port}
              onChange={e => setPort(Number(e.target.value))}
              placeholder='4455'
              label={
                <OptionLabel
                  label={t('obs.port.label', 'Port')}
                  desc={t('obs.port.desc', 'Enter the port of the OBS WebSocket')}
                />
              }
              disabled={isConnected || isConnecting}
              defaultValue={4455}
              min={1}
              max={65535}
            />
          </div>

          <div className='space-y-2'>
            <InputWithLabel
              id='password'
              type='password'
              value={password}
              placeholder={t('obs.password.placeholder', 'Optional')}
              onChange={e => setPassword(e.target.value)}
              label={
                <OptionLabel
                  label={t('obs.password.label', 'Password')}
                  desc={t(
                    'obs.password.desc',
                    'Enter the password of the OBS WebSocket. Leave blank if no password is set'
                  )}
                />
              }
              disabled={isConnected || isConnecting}
            />
          </div>
        </div>
      </div>

      <div className='flex flex-col gap-2 @sm:flex-row @sm:items-center'>
        <div className='flex flex-auto items-center space-x-1'>
          <Checkbox id='auto-connect' checked={autoConnect} onChange={e => setAutoConnect(e.target.checked)} />
          <Label htmlFor='auto-connect'>{t('obs.autoConnect', 'Auto Connect')}</Label>
        </div>

        {!isConnected ? (
          <Button onClick={handleConnect} disabled={isConnecting} tint={'accent'}>
            {isConnecting ? t('obs.connecting', 'Connecting…') : t('obs.connect', 'Connect')}
          </Button>
        ) : (
          <Button onClick={handleDisconnect} tint='rose'>
            {t('obs.disconnect', 'Disconnect')}
          </Button>
        )}

        {/* <div>
          {isConnected ? (
            <Badge variant='dot' tint={'green'}>
              {t('obs.connected', 'Connected')}
            </Badge>
          ) : (
            <Badge variant='dot' tint={'default'}>
              {t('obs.disconnected', 'Disconnected')}
            </Badge>
          )}
        </div> */}
      </div>

      {connectionError && <Alert tint={'warning'}>{connectionError}</Alert>}
    </div>
  )
}
