import { useEffect, useState } from 'react'
import clsx from 'clsx'
import { toast } from 'sonner'
import { IconLanguageHiragana } from '@tabler/icons-react'

import { translateManager } from '@/utils/translateManager'

import styles from './message-translation.module.css'

interface MessageTranslationProps {
  message: string
  className?: string
  eventType: string
  eventId?: string
  roomId: number
}

// Whitelist of event types that should be translated
const TRANSLATE_EVENT_TYPES = ['message', 'superchat', 'effect-message']

export default function MessageTranslation({
  message,
  className,
  eventType,
  eventId,
  roomId,
}: MessageTranslationProps) {
  const [translation, setTranslation] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const options = translateManager.getOptions()

  useEffect(() => {
    // Reset translation state when message changes
    setTranslation(null)

    // Skip translation if disabled, no message, or event type not in whitelist
    if (!options.translateEnabled || !message || !TRANSLATE_EVENT_TYPES.includes(eventType)) {
      return
    }

    let isMounted = true
    setIsLoading(true)

    translateManager
      .translateText(message, roomId, eventId)
      .then(result => {
        if (isMounted) {
          setTranslation(result)
          setIsLoading(false)
        }
      })
      .catch(error => {
        console.error('Translation error:', error)

        if (isMounted) {
          setIsLoading(false)
          toast.error(`Translation failed: ${error.message}`)
        }
      })

    return () => {
      isMounted = false
    }
  }, [message, roomId, eventType, eventId, options.translateEnabled, options.translateTo])

  // Don't render anything if translation is disabled, no translation or event type not in whitelist
  if (!options.translateEnabled || !translation || !TRANSLATE_EVENT_TYPES.includes(eventType)) {
    return null
  }

  // 如果翻译结果与原文相同，则不显示翻译
  if (message === translation) {
    return null
  }

  return (
    <span className={clsx(styles.translation, 'translation', isLoading && styles.loading, className)}>
      <span className={clsx(styles.iconWrap, 'translation-icon-wrap')}>
        <IconLanguageHiragana className={clsx(styles.icon, 'translation-icon')} />
      </span>{' '}
      <span className={clsx(styles.text, 'translation-text')}>{translation}</span>
    </span>
  )
}
