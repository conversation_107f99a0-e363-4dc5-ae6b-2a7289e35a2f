import useSWR, { type Fetcher } from 'swr'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

// Updated fetcher function with TypeScript support for fetching multiple URLs
const fetcher = async (urls: string[]) => {
  const f: Fetcher<BilibiliInternal.HTTPS.Prod.GetInfoByRoom> = (url: string) => fetch(url).then(r => r.json())

  return Promise.allSettled(urls.map(url => f(url))).then(results =>
    results
      .filter(
        (result): result is PromiseFulfilledResult<BilibiliInternal.HTTPS.Prod.GetInfoByRoom> =>
          result.status === 'fulfilled'
      )
      .map(result => result.value)
  )
}

/**
 * Same as `useRoomInfo` but fetch more than one room at a time
 * @param roomIds
 * @returns
 */
function useRoomsInfo(roomIds: number[]) {
  const sanitizedQueries = roomIds.map(roomId => roomId.toString().replace(/[^0-9]/g, ''))
  const sanitizedUrls = sanitizedQueries.map(url => `${Api.Workers}/bilibili/room-info/${url}`)

  const { data, error, isLoading } = useSWR(
    sanitizedQueries && sanitizedQueries.length > 0 ? sanitizedUrls : null,
    fetcher,
    {
      refreshInterval: 60 * 1000,
      // refreshInterval: 0,
      // revalidateOnFocus: false,
    }
  )

  return {
    roomsInfo: data,
    isRoomsInfoLoading: isLoading,
    isRoomsInfoError: error,
  }
}

export default useRoomsInfo
