import { useRef, useState } from 'react'

import { useDebouncedValue } from '@/hooks/useDebouncedValue'

import { Input } from '@/components/ui/input'

interface ColorPickerProps {
  initialColor?: string
  onColorChange?: (color: string) => void
  placeholder?: string
}

const ColorPicker = ({ initialColor, onColorChange, placeholder }: ColorPickerProps) => {
  const [color, setColor] = useState(initialColor)
  const [textInput, setTextInput] = useState(initialColor ?? '')
  const [debouncedColor] = useDebouncedValue(color, 0)
  const colorInputRef = useRef<HTMLInputElement>(null)

  // Call onColorChange only when debounced color changes
  const prevDebouncedColorRef = useRef(debouncedColor)
  if (debouncedColor !== prevDebouncedColorRef.current) {
    prevDebouncedColorRef.current = debouncedColor
    onColorChange?.(debouncedColor || '')
  }

  const normalizeHexColor = (input: string): string => {
    let hex = input.replace('#', '').trim()

    if (/^[0-9A-Fa-f]{3}$/.test(hex)) {
      hex = hex
        .split('')
        .map(char => char + char)
        .join('')
    }

    if (/^[0-9A-Fa-f]{6}$/.test(hex)) {
      return `#${hex}`
    }

    return hex
  }

  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value
    setColor(newColor)
    setTextInput(newColor.replace('#', ''))
  }

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setTextInput(value)

    const normalizedColor = normalizeHexColor(value)
    if (normalizedColor !== color) {
      setColor(normalizedColor)
    }
  }

  const handlePreviewClick = () => {
    colorInputRef.current?.click()
  }

  return (
    <div className='relative flex max-w-sm flex-col gap-4'>
      <input
        ref={colorInputRef}
        type='color'
        value={color ?? '#000000'}
        onChange={handleColorChange}
        className='invisible absolute bottom-0 left-0 size-0'
      />

      <div className='flex items-center gap-2'>
        <div
          onClick={handlePreviewClick}
          className='translucent-grid size-6 cursor-pointer rounded-full border'
          style={{
            backgroundColor: color ?? '#ffffff',
          }}
          role='button'
          aria-label='Choose color'
        />
        <Input
          type='text'
          value={textInput}
          onChange={handleTextChange}
          className='w-28 pl-5 font-mono text-sm'
          placeholder={placeholder || 'fef188'}
          maxLength={7}
          leftSection={<span className='text-fg/60 w-4 select-none'>#</span>}
          leftSectionClassName='pl-2 pointer-events-none'
        />
      </div>
    </div>
  )
}

export { ColorPicker }
