import React from 'react'
import clsx from 'clsx'

import type { Toast } from '@/lib/event-parsers/types/toast'

import { calculateGuardRank, getToastIcon } from '@/utils/eventAssetsUtils'
import { toastCopywriting } from '@/utils/eventCopywriting'
import { nf } from '@/utils/numberFormat'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import type { AsComponentProps, EventModeProps } from '@/components/event'
import UserDropdown from '@/components/UserDropdown'

import styles from './toast.module.css'

export interface ToastProps extends React.HTMLAttributes<HTMLDivElement> {
  data: Toast
  mode: EventModeProps
  as: AsComponentProps
}

/**
 * 大航海事件
 */
export default function ToastItem({ data, mode, as, ...rest }: ToastProps) {
  const options = useOptions()
  const { colorScheme } = options
  const { frameRank, badgeRank } = calculateGuardRank(data.toastTotalCount)
  const { guardIconUrl } = getToastIcon(data.toastType, frameRank, badgeRank)

  const { totalDays, toastActionType } = toastCopywriting(data.message)

  // 盲盒用，本来 unit 里是只包含单位的，大航海盲盒直接把数量写单位里了，闹麻了😅
  const blindToastUnit = data.toastAmountUnit.match(/\d+/)

  return (
    <div
      className={clsx(
        styles.event,
        styles[`tier${data.toastType}`],
        styles[`frameRank${frameRank}`],
        styles[`badgeRank${badgeRank}`],
        styles[`showAs_${as}`],
        colorScheme === 'dark' && [styles.dark, 'dark'],
        data?.mockPrice && 'mock-price',
        data.priceNormalized <= 50 && [styles.specialBlindBox, 'special-blind-box'],
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `event-show-as--${as}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        `guard-level--${data.toastType}`,
        `avatar-frame-rank--${frameRank}`,
        `avatar-badge-rank--${badgeRank}`,
        data.read && [styles.read, 'read']
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      data-toast-price={data.priceNormalized}
      data-toast-type={data.toastType}
      data-toast-amount={data.toastAmount}
      data-frame-rank={frameRank}
      data-badge-rank={badgeRank}
      data-read={data.read}
      {...rest}
    >
      <Avatar uid={data.uid} avatar={data.avatar} guard={data.toastType} perkLevel={frameRank} className='avatar' />
      <Avatar
        uid={data.uid}
        avatar={data.avatar}
        guard={data.toastType}
        perkLevel={frameRank}
        className={`${styles.hidden} avatar-alt-top`}
      />

      <div className={`${styles.content} content`}>
        {as !== 'sticky' && (
          <div className={`${styles.username} username`}>
            {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}

            {mode === 'dashboard' && (
              <UserDropdown event={data}>
                <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
              </UserDropdown>
            )}
          </div>
        )}

        <div className={`${styles.price} price`}>
          {!data?.mockPrice ? (
            <>
              <span className={`${styles.priceCurrency} price-currency`}>CN¥</span>
              <span className={`${styles.priceFigure} price-figure`}>{nf.format(data.priceNormalized)}</span>
            </>
          ) : (
            <>
              <span className={`${styles.priceFigure} price-text`}>{data.toastName}</span>
            </>
          )}
        </div>

        {as !== 'sticky' && (
          <div className={`${styles.message} message`}>
            <span className='toast-details-action'>{toastActionType}</span>
            <span className='toast-details-type'>{data.toastName}</span>
            {data.toastAmount > 1 ? (
              <>
                <span className='toast-details-symbol'>×</span>
                <span className='toast-details-figure'>{data.toastAmount}</span>
                <span className='toast-details-unit'>
                  {data.toastAmountUnit === '月' ? `个${data.toastAmountUnit}` : data.toastAmountUnit}
                </span>
              </>
            ) : null}

            {/* 舰长盲盒 */}
            {data.toastAmountUnit.includes('天') ? (
              <>
                <span className='toast-details-symbol'>×</span>
                <span className='toast-details-figure'>{blindToastUnit ? blindToastUnit[0] : 1}</span>
                <span className='toast-details-unit'>天</span>
              </>
            ) : null}

            {totalDays > 0 ? (
              <>
                <span className='toast-details-total-days-text-before'>，已陪伴主播 </span>
                <b className='toast-details-total-days'>{totalDays}</b>
                <span className='toast-details-total-days-text-after'> 天</span>
              </>
            ) : null}
          </div>
        )}

        <Avatar
          uid={data.uid}
          avatar={data.avatar}
          guard={data.toastType}
          perkLevel={frameRank}
          className={`${styles.hidden} avatar-alt-bottom`}
        />

        {/* Alt gift image on top, hidden by default */}
        <span className={`${styles.hidden} toast-icon-wrap toast-icon-wrap-alt-bottom`}>
          <picture className={`inline-flex`}>
            <img src={guardIconUrl} alt={'大航海'} referrerPolicy='no-referrer' loading='lazy' />
          </picture>
        </span>
      </div>
    </div>
  )
}
