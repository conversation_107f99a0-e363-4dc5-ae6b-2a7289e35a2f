import React from 'react'
import clsx from 'clsx'

import type { System } from '@/lib/event-parsers/types/system'

import styles from './system.module.css'

export interface SystemProps extends React.HTMLAttributes<HTMLDivElement> {
  data: System
}

/**
 * 系统事件，LAPLACE Chat 内部用于展示系统相关信息的事件，并非来自哔哩哔哩
 */
export default function SystemItem({ data, ...rest }: SystemProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <span className='message'>{data.message}</span>
    </div>
  )
}
