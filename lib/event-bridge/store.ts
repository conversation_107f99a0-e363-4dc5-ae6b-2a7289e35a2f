import { toast } from 'sonner'
import { create } from 'zustand'

import type { ConnectionSettings } from '@/lib/event-bridge/utils'
import { getConnectionUrl } from '@/lib/event-bridge/utils'
import type { LaplaceEvent } from '@/lib/event-parsers/types'

interface EventBridgeState {
  // WebSocket instance
  socket: WebSocket | null

  // Connection state
  isConnected: boolean
  isConnecting: boolean
  connectionError: string | null

  // Connection methods
  initialize: () => void
  connect: (settings: ConnectionSettings) => Promise<void>
  disconnect: () => Promise<void>

  // Send event
  sendEvent: (event: LaplaceEvent) => void
}

export const useEventBridgeStore = create<EventBridgeState>()((set, get) => ({
  // Initial state
  socket: null,
  isConnected: false,
  isConnecting: false,
  connectionError: null,

  // Initialize the WebSocket connection
  initialize: () => {
    // No initialization needed for WebSocket like we do with OBS
    // We'll create the socket when connect is called
  },

  // Connect to Event Bridge WebSocket server
  connect: async (settings: ConnectionSettings) => {
    // Disconnect existing connection if any
    await get().disconnect()

    try {
      set({ isConnecting: true, connectionError: null })

      // Get connection URL from settings
      const url = getConnectionUrl(settings)

      // Create new WebSocket connection with a subprotocol indicating role
      // The subprotocol will be sent in the Sec-WebSocket-Protocol header
      const subprotocol = settings.password
        ? ['laplace-event-bridge-role-server', settings.password]
        : ['laplace-event-bridge-role-server']
      const socket = new WebSocket(url, subprotocol)

      // Set up event listeners
      socket.onopen = () => {
        set({ isConnected: true, connectionError: null })

        // Send password if provided
        if (settings.password) {
          socket.send(JSON.stringify({ type: 'auth', password: settings.password }))
        }
      }

      socket.onclose = () => {
        set({
          isConnected: false,
          isConnecting: false,
          socket: null,
        })

        // toast.info('Event Bridge disconnected', {
        //   id: 'event-bridge-disconnected',
        // })
      }

      socket.onerror = error => {
        set({
          isConnecting: false,
          connectionError: `Connection error: ${error}`,
        })

        console.error('Event Bridge error', error)

        toast.info('Event Bridge error', {
          description: `Connection error: ${error}`,
          id: 'event-bridge-error',
        })
      }

      // Store the socket instance
      set({ socket })
    } catch (error) {
      console.warn('Failed to connect to Event Bridge:', error)
      set({
        connectionError: `Failed to connect: ${error}`,
        isConnected: false,
        isConnecting: false,
      })

      toast.info('Failed to connect to Event Bridge', {
        description: String(error),
        id: 'event-bridge-error',
      })
    }
  },

  // Disconnect from Event Bridge WebSocket server
  disconnect: async () => {
    const { socket } = get()

    if (!socket) return

    try {
      socket.close()
      set({
        socket: null,
        isConnected: false,
      })
    } catch (error) {
      console.warn('Failed to disconnect from Event Bridge:', error)

      toast.error('Failed to disconnect from Event Bridge', {
        description: String(error),
      })
    }
  },

  // Send event to Event Bridge WebSocket server
  sendEvent: (event: LaplaceEvent) => {
    const { socket, isConnected } = get()

    if (!socket || !isConnected) return

    try {
      socket.send(JSON.stringify(event))
    } catch (error) {
      console.warn('Failed to send event to Event Bridge:', error)
    }
  },
}))
