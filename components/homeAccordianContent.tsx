import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { LatestVersion } from '@/data/version'
import {
  IconAdjustmentsAlt,
  IconBolt,
  IconBook2,
  IconCircleCheck,
  IconCircleMinus,
  IconCircleX,
  IconCloudDown,
  IconHeart,
  IconHelpSquareRounded,
  IconLanguageHiragana,
  IconLockCheck,
  IconMessage2Question,
  IconMessages,
  IconNotebook,
  IconPlayerPlayFilled,
  IconSettingsCog,
  IconTransfer,
  IconVolume,
} from '@tabler/icons-react'

import { timeFromNow } from '@/utils/timeFromNow'

import BilibiliUser from '@/components/BilibiliUser'
import { AccordionSingleton as Accordion } from '@/components/ui/accordion'
import { Button } from '@/components/ui/button'
import { TooltipOrPopover } from '@/components/ui/tooltip-or-popover'

export function HomeAccordion() {
  const [currentChangelogTimestamp, setCurrentChangelogTimestamp] = useState('…')

  useEffect(() => {
    setCurrentChangelogTimestamp(timeFromNow(+new Date(LatestVersion.timestamp)))
  }, [])

  const DynamicChangelogList = dynamic(() => import('@/components/ChangelogList').then(mod => mod.ChangelogList), {
    loading: () => <p>载入日志…</p>,
    ssr: false,
  })

  // prettier-ignore
  const connectionModeCharts = [
  // 功能 / 浏览器同步 / 匿名直连 / 哔哩哔哩开放平台
  { name: '获取用户 UID', mode0: ['yes', '支持'], mode1: ['no', '不支持'], mode2: ['no', '2024-03 之后不支持'], },
  { name: '获取用户 Open ID', mode0: ['no', '不支持'], mode1: ['no', '不支持'], mode2: ['yes', '支持'], },
  { name: '用户弹幕', mode0: ['yes', '支持'], mode1: ['partial', '打码（明***）'], mode2: ['yes', '支持'], },
  { name: '显示用户头像', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['yes', '支持'], },
  { name: '显示用户头像框', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '显示粉丝勋章', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['partial', '只显示当前主播'], },
  { name: '显示用户 UL 等级', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '显示荣耀等级', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['partial', '仅弹幕支持'], },
  { name: '显示弹幕回复（@用户）', mode0: ['yes', '支持'], mode1: ['partial', '打码（@明***）'], mode2: ['partial', '不显示头像'], },
  { name: '高亮主播弹幕', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '2024-03 之后不支持'], },
  { name: '表情包弹幕', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['partial', '「b豆」以文本显示'], },
  { name: '房管标记', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['yes', '支持'], },
  { name: '排行榜标记（榜1/2/3）', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '礼物事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['yes', '支持'], },
  { name: '大航海事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['partial', '不显示陪伴天数'], },
  { name: '守护圣殿礼物事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '醒目留言事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['yes', '支持'], },
  { name: '醒目留言日文翻译', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '红包事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '天选时刻事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '用户进场事件', mode0: ['yes', '支持'], mode1: ['partial', '打码（明***）'], mode2: ['partial', '无粉丝勋章、大航海标记'], },
  { name: '禁言事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '全局禁言事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '直播间被警告事件', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '用户点赞', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['partial', '无粉丝勋章'], },
  { name: '用户关注', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '用户分享直播间', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '直播间标题变更', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  { name: '控制台实时高能榜', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  // { name: '控制台实时观看人数', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
  // { name: '控制台实时点赞人数', mode0: ['yes', '支持'], mode1: ['yes', '支持'], mode2: ['no', '不支持'], },
]

  const ChartBadge = ({ type }: { type: string }) => {
    if (type === 'yes') {
      return <IconCircleCheck size={'1.25em'} className='text-emerald-500' />
    } else if (type === 'partial') {
      return <IconCircleMinus size={'1.25em'} className='text-amber-500' />
    } else {
      return <IconCircleX size={'1.25em'} className='text-rose-500' />
    }
  }

  const homeAccordionGuideContent = [
    {
      id: 'setup-obs',
      label: '如何使用 OBS 弹幕组件',
      content: (
        <div className='space-y-3'>
          <p>OBS 模式用于直播间弹幕展示，也可用于任意支持网页源的直播推流软件（例如哔哩哔哩官方直播姬）</p>

          <ol className='list-decimal'>
            <li>
              <h5>选择连接方式</h5>
              <div className='text-fg/60'>选择弹幕的连接方式，具体区别请参考下方的「连接方式对比」</div>
            </li>
            <li>
              <h5>设置需要监控的直播间</h5>
              <div className='text-fg/60'>
                在本页面下方将需要监控的直播间链接或房间号粘贴进来。如果您使用的是「开放平台（幻星）」的连接方式，请将「身份码」粘贴进来，您的「身份码」可在{' '}
                <a href='https://play-live.bilibili.com/' target='_blank' rel='noopener noreferrer'>
                  哔哩哔哩幻星
                </a>{' '}
                首页的右下角找到
              </div>
            </li>
            <li>
              <h5>修改配置</h5>
              <div className='text-fg/60'>
                如果对默认的样式和配置不满意，可在本页面修改相应的设置，右边会有测试数据的预览，也可以查看实时直播间预览
              </div>
            </li>
            <li>
              <h5>复制弹幕机链接</h5>
              <div className='text-fg/60'>
                在修改完配置后，请点击页面最下方的复制链接，或者手动选择输入框中的链接进行复制
              </div>
            </li>
            <li>
              <h5>添加到 OBS</h5>
              <div className='text-fg/60'>
                打开 OBS，在 OBS 中添加来源：浏览器。在弹出的窗口中将刚刚复制的 URL 粘贴进输入框。然后调整高宽，建议为
                600 宽 800 高（可根据实际需求自行调整尺寸）。最后如果有自定义
                CSS，将其粘贴至对应的输入框。您也可以使用云端模版功能来自动同步样式
              </div>
            </li>
          </ol>
        </div>
      ),
    },
    {
      id: 'how-to-use-dashboard',
      label: '如何在控制台模式下使用弹幕机',
      content: (
        <div className='space-y-3'>
          <p>控制台模式可用于主播读弹幕、读醒目留言（SuperChat）、以及谢礼物环节</p>
          <p>前期准备：</p>
          <ul className='list-disc'>
            <li>Google Chrome、Edge、或基于 Chromium 内核（建议内核版本大于等于 108）的浏览器，不推荐使用 Firefox</li>
          </ul>

          <ol className='list-decimal'>
            <li>
              <h5>选择连接方式</h5>
              <div className='text-fg/60'>选择弹幕的连接方式，具体区别请参考下方的「连接方式对比」</div>
            </li>
            <li>
              <h5>设置需要监控的直播间</h5>
              <div className='text-fg/60'>
                在本页面下方将需要监控的直播间链接或房间号粘贴进来。如果您使用的是「开放平台（幻星）」的连接方式，请将「身份码」粘贴进来，您的「身份码」可在{' '}
                <a href='https://play-live.bilibili.com/' target='_blank' rel='noopener noreferrer'>
                  哔哩哔哩幻星
                </a>{' '}
                首页的右下角找到
              </div>
            </li>

            <li>
              <h5>修改配置</h5>
              <div className='text-fg/60'>如果对默认的配置不满意，可在本页面修改相应的设置</div>
            </li>

            <li>
              <h5>开始使用</h5>
              <div className='text-fg/60'>点击页面最下方的「打开控制台」即可使用</div>
            </li>
          </ol>
        </div>
      ),
    },
    {
      id: 'obs-advanced-dark-mode',
      label: 'OBS 高级用法：场景标记 —— 自动切换「深色模式」',
      content: (
        <div className='space-y-3'>
          <Image src={'/demo/scene-switching.gif'} alt={'demo'} width={652 / 1.63} height={325 / 1.63} />
          <p>
            如果您的 OBS
            中包含多个场景，例如一个浅色背景的场景，用于杂谈；一个深色背景的场景，用于游戏，那么通常的做法是在 OBS
            中添加两个弹幕机源，一个设为浅色模式、一个深色。但这么做会有两个弊端：
          </p>
          <ul className='list-disc'>
            <li>OBS 中相当于开了两个弹幕机，增加额外的资源占用，如果您有两个以上的场景，那么资源占用更多</li>
            <li>在切换场景时，弹幕机的显示会不连贯、不够顺滑</li>
          </ul>
          <p>
            因此，本项目引入一个原创的特性，可以直接调用 OBS 的
            API，自动判断场景的类型，自动对弹幕机的颜色进行切换，具体使用方法为：
          </p>
          <ol className='list-decimal'>
            <li>
              按上方的使用方法常规添加一个弹幕机，但是在「页面权限」一项中，将权限修改为「用户信息的读取权限（当前场景集合、转场）」
            </li>
            <li>右键点击弹幕机源，选择「复制」</li>
            <li>
              新建场景，或切换到其他场景中，在「来源」空白处右键，选择「粘贴（引用）」，此时两个场景中均会出现一个弹幕机，但是以类似指针的形式，都指向的同一个浏览器源
            </li>
            <li>
              选定需要将弹幕机改为深色的场景，单击右键，然后重命名，在场景名称中添加 <code>[dark]</code>{' '}
              字段。位置任意，推荐放在最后，例如 <code>全屏游戏 [dark]</code>
            </li>
            <li>之后切换场景时将自动根据您添加的标记，自动调整弹幕机的配色</li>
          </ol>
        </div>
      ),
    },
    {
      id: 'obs-advanced-custom-scenes',
      label: 'OBS 高级用法：场景标记 —— 自定义场景',
      content: (
        <div className='space-y-3'>
          <p>与上方的自动切换「深色模式」功能类似，上述方法可以拓展到自定义场景中</p>
          <ol className='list-decimal'>
            <li>保持刚才设置的浏览器源权限</li>
            <li>
              选定需要自定义的场景，单击右键，然后重命名，在场景名称中添加 <code>[scene:name]</code> 字段。
              <code>name</code> 为要添加的场景名，建议使用纯英文，不要带空格等特殊字符，例如{' '}
              <code>全屏游戏 [dark] [scene:gaming]</code>
            </li>
            <li>
              完成设置后，再次切换场景，该场景下的弹幕机则会自动应用上全局的 CSS 样式 <code>.scene-gaming</code>
              ，此时可根据此样式重新定义当前场景下所有属性
            </li>
            <li>如果您你是样式作者，你可以在「样式」标签页中输入「OBS 场景」，用来模拟 OBS 中自定义场景的情况</li>
          </ol>
        </div>
      ),
    },
    {
      id: 'connection-mode-comparison',
      label: '弹幕被打码如何处理？附连接方式对比',
      content: (
        <div className='space-y-3'>
          <p>
            自 2023-09-01
            起，哔哩哔哩开始对匿名弹幕连接进行限制，匿名连接将无法获取弹幕中的用户名，会被官方打码处理。如果弹幕的用户名对您很重要，请在「进阶」标签页下选择浏览器扩展同步，或选择「开放平台（幻星）」的连接方式
          </p>

          <table className='w-full table-auto'>
            <thead>
              <tr>
                <th className='border-b p-2 text-left'>功能</th>
                <th className='border-b p-2 text-left'>浏览器同步</th>
                <th className='border-b p-2 text-left'>匿名直连</th>
                <th className='border-b p-2 text-left'>开放平台</th>
              </tr>
            </thead>
            <tbody>
              {connectionModeCharts.map(element => (
                <tr key={element.name}>
                  <td className='border-fg/10 border-b p-2'>{element.name}</td>
                  <td className='border-fg/10 border-b p-2'>
                    <div className='flex items-center gap-1'>
                      <span className='hidden shrink-0 items-center @lg:block'>
                        <ChartBadge type={element.mode0[0]!} />
                      </span>

                      <span className='@lg:hidden'>
                        <TooltipOrPopover label={element.mode0[1]}>
                          <span className='shrink-0'>
                            <ChartBadge type={element.mode0[0]!} />
                          </span>
                        </TooltipOrPopover>
                      </span>

                      <span className='hidden @lg:block'>{element.mode0[1]}</span>
                    </div>
                  </td>
                  <td className='border-fg/10 border-b p-2'>
                    <div className='flex items-center gap-1'>
                      <span className='hidden shrink-0 items-center @lg:block'>
                        <ChartBadge type={element.mode1[0]!} />
                      </span>

                      <span className='@lg:hidden'>
                        <TooltipOrPopover label={element.mode1[1]}>
                          <span className='shrink-0'>
                            <ChartBadge type={element.mode1[0]!} />
                          </span>
                        </TooltipOrPopover>
                      </span>
                      <span className='hidden @lg:block'>{element.mode1[1]}</span>
                    </div>
                  </td>
                  <td className='border-fg/10 border-b p-2'>
                    <div className='flex items-center gap-1'>
                      <span className='hidden shrink-0 items-center @lg:block'>
                        <ChartBadge type={element.mode2[0]!} />
                      </span>

                      <span className='@lg:hidden'>
                        <TooltipOrPopover label={element.mode2[1]}>
                          <span className='shrink-0'>
                            <ChartBadge type={element.mode2[0]!} />
                          </span>
                        </TooltipOrPopover>
                      </span>
                      <span className='hidden @lg:block'>{element.mode2[1]}</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ),
    },
  ]

  const homeAccordionFaqContent = [
    {
      id: 'q1',
      label: '为何重复造轮子？',
      content: (
        <p>
          目前市面上的弹幕机主要分为两大类，一类基于 web 的 blivechat 及其衍生版本，通常作为 OBS
          组件（widget）出现；一类基于
          Unity、Rust（Tauri）或其他语言实现的客户端版本，通常用于主播读弹幕。这两类都有相似的功能以及各自的优缺点，但为什么没有集成两种形式优点于一身的弹幕机呢？于是这个轮子就出现了
        </p>
      ),
    },
    {
      id: 'q2',
      label: '为什么国内访问有点慢？是否有别的备用节点',
      content: (
        <div className='space-y-3'>
          <p>
            出于部署便捷性以及隐私考虑，本站目前架设在境外，如访问困难可尝试通过代理访问，或通过下方的合作节点尝试访问，目前已知的节点：
          </p>
          {/* prettier-ignore */}
          <ul className='list-disc'>
            <li><a href="https://chat.laplace.live" target='_blank'>chat.laplace.live</a> - 美国 CDN</li>
            <li><a href="https://chat.vrp.moe" target='_blank'>chat.vrp.moe</a> - 美国 Cloudflare CDN</li>
            <li><a href="https://lchat.vupgo.com" target='_blank'>lchat.vupgo.com</a> - 腾讯云 CDN，由「纸片人计划」赞助</li>
            <li><a href="https://chat-dev.laplace.live" target='_blank'>chat-dev.laplace.live</a> - 新加坡机房 + CF CDN（开发节点）</li>
          </ul>
        </div>
      ),
    },
    {
      id: 'q3',
      label: '为什么 OBS 模式下，弹幕不支持显示日期？',
      content: (
        <p>
          对于绝大多数直播间，所呈现的弹幕通常都在一分钟以内，所以给每条弹幕增加时间戳的意义不大。如果想显示时间，请在直播间中展示独立的时钟，可使用{' '}
          <a href='https://clock.laplace.live' target='_blank'>
            LAPLACE Clock
          </a>{' '}
          项目来实现
        </p>
      ),
    },
    {
      id: 'q4',
      label: '吹的这么牛，是否真的性能很好？资源占用低？',
      content: (
        <div className='space-y-3'>
          <ul className='list-disc'>
            <li>
              OBS 模式下：是的。本项目在中、低端机器上都可以流畅运行，稳定 60
              帧，并且没有任何内存泄漏问题，可稳定用于生产环境
            </li>
            <li>
              控制台模式下：看情况。控制台因为包含更多功能，因此会优先保证流畅的前提下占用更多的资源，控制台需要对礼物做额外处理，因此礼物量越大，占用的资源也越多
            </li>
          </ul>
          <p>以下是一场中、大型直播，运行时长 5 小时左右的实测数据：</p>
          <ul className='list-disc'>
            <li>测试机配置：i7-13900K，64 GB 内存</li>
            <li>控制台模式的内存占用会在 300 MB 至 700 MB 徘徊。平均占用在 400 MB 左右，CPU 占用 3%-10% 浮动</li>
            <li>OBS 模式的内存占用会在 40 MB 至 200 MB 徘徊。平均占用在 100 MB 左右，CPU 占用 1%-3% 浮动</li>
          </ul>
          <p>
            在某些特殊情况下，例如某场超大型直播中，单场弹幕数量达到百万或每分钟弹幕数超过 5,000
            的情况下（目前没有真实遇到过，只在模拟实验中进行过测试，参考原神版本更新直播间弹幕量在每分钟 2,000
            左右）。此时会达到浏览器 IndexedDB
            数据库的性能瓶颈（该瓶颈通常与机器性能以及浏览器版本有关），控制台模式下的实时弹幕滚动速度会受限（IDB
            写入性能下降），但不会影响右侧的 SC 和礼物部分。OBS 组件不受任何性能影响
          </p>
        </div>
      ),
    },
    {
      id: 'q5',
      label: '控制台模式下，为什么感觉越用越卡？',
      content: (
        <p>
          因为本地的数据库会越来越大。每位主播的礼物量和需求都不相同，因此本站不会自动清除本地的礼物，如果您使用本站超过一定时间（例如一个月），本地可能存有大量礼物
          ，这时候可以通过控制台右上角的「系统设置 - 管理数据库 - 清空…」来精简数据库
        </p>
      ),
    },
    {
      id: 'q6',
      label: '我是主题作者，我能售卖我创作的主题并从中营利吗？',
      content: (
        <p>可以。您享有全部的主题/模版定价权，并且收益 100% 归您所有，本人不会参与任何基于本项目进行盈利的商业活动</p>
      ),
    },
    {
      id: 'q7',
      label: '隐私相关？',
      content: (
        <ul className='list-disc'>
          <li>
            本站无需登录、无需下载客户端、无需开会员、无需提供手机号、无需上传浏览器 WebGL 指纹、无需前台展示使用者 IP
            属地、无需获取用户 IMEI
            地址、也不需要提供支付方式、特定功能也不需要强制用户下载客户端、不会利用「临时工」直接插入前端脚本以开盒用户、不会通过
            WebRTC 来偷偷利用浏览器当 PCDN、不会私自开启大量 UPnP
            端口、更不需要摄像头扫脸认证以开通使用权限，即可免费的、无限制使用时长的自由使用。所有的选项均保存在浏览器本地，不会被上传至服务器或提供给第三方
          </li>
          <li>
            因为本人的代码和测试写的太好了，生产环境也完全不会遇到未知的错误，因此本网站未使用任何 Sentry、BugSnag
            等错误汇报服务，因此不会统计 IP 地址等 PII 信息
          </li>
          <li>本网站未使用任何 Google Analytics、Cloudflare Analytics 等访问统计服务，不会将访问数据提供给第三方</li>
          <li>
            当您通过「开放平台（幻星）」使用本项目时，本站可以在哔哩哔哩开放平台的用户后台查看到您使用本弹幕机的日期、对应房间号、主播昵称、以及使用次数信息。此信息由哔哩哔哩平台所对应的公司实体收集与保存，与本站无关
          </li>
        </ul>
      ),
    },
    {
      id: 'q8',
      label: '在线可用率？',
      content: (
        <div className='space-y-3'>
          <ul className='list-disc'>
            <li>
              本站基于 Vercel、Cloudflare 等 serverless 平台搭建，由于本站所使用的计划并没有提供
              SLA，因此本站也不承诺任何在线可用率
            </li>
            <li>
              但根据本站历史的在线可用率来看，本站可提供大致 99.95% 的在线可用率，即停机时间不大于：
              <ul className='list-disc'>
                <li>每年：4.38 小时</li>
                <li>每季度：65.7 分钟</li>
                <li>每月：21.92 分钟</li>
                <li>每周：5.04 分钟</li>
                <li>每天（24 小时）：43.20 秒</li>
              </ul>
            </li>
            <li>您可以在本页面最下方查看当前服务器在线状态</li>
            <li>
              因为本站维护、离线等无法访问的原因直接、间接导致您直播产生的经济损失，本站不承担任何责任。具体请查看下方的「免责声明？」
            </li>
          </ul>
        </div>
      ),
    },
    {
      id: 'q9',
      label: '免责声明？',
      content: (
        <div className='space-y-3'>
          <p>本站完全免费使用，不会参与到任何相关的盈利活动中，包括但不限于：</p>
          <ul className='list-disc'>
            <li>售卖样式模板</li>
            <li>有偿提供二次开发</li>
            <li>有偿搭建云端事件服务端</li>
            <li>协助他人黑进哔哩哔哩服务器</li>
            <li>出售弹幕相关数据给第三方公司</li>
            <li>盗取他人哔哩哔哩账号并提取直播收益</li>
            <li>协助成年人进行未成年退款并按比例收取手续费</li>
          </ul>

          <p>
            由于本站完全免费使用，我不会从你那获得任何好处，因此你也不会从我这获得任何技术支持以及可能受到的经济损失的赔偿：包括但不限于：
          </p>

          <ul className='list-disc'>
            <li>
              我用了你的弹幕机，但是直播间里有人发不和谐弹幕，导致直播间被封，我要你负责！
              <ul className='list-disc'>
                <li>正确做法：限制直播间内的用户发言等级；然后去找哔哩哔哩客服</li>
              </ul>
            </li>
            <li>
              我从某人那购买的你这个弹幕机样式，但是现在它突然不工作了，样式不对了，我要找你进行售后！
              <ul className='list-disc'>
                <li>正确做法：找出售给你弹幕机样式的人</li>
              </ul>
            </li>
            <li>
              我正在进行非常关键的周年庆/生日回/联动，弹幕机突然刷不出来，导致直播间互动率变低/没有感谢老板的礼物，我要你赔我个总督！
              <ul className='list-disc'>
                <li>正确做法：先使用其他备用方案</li>
              </ul>
            </li>
          </ul>
        </div>
      ),
    },
    {
      id: 'q10',
      label: '备用方案？',
      content: (
        <div className='space-y-3'>
          <p>如果因为任何原因导致您无法正常使用本站，您可以尝试下列备选方案：</p>
          <ul className='list-disc'>
            <li>
              <a href='https://github.com/xfgryujk/blivechat' target='_blank'>
                xfgryujk/blivechat
              </a>{' '}
              - 原版 blivechat，开源/免费
            </li>
            <li>
              <a href='https://www.miebo.cn' target='_blank'>
                咩播
              </a>{' '}
              - 咩播直播插件，闭源/商业/freemium
            </li>
            <li>
              <a href='https://github.com/Tsuk1ko/bilibili-live-chat' target='_blank'>
                Tsuk1ko/bilibili-live-chat
              </a>{' '}
              - 开源/免费
            </li>
          </ul>
          <p>如果您有重要的直播活动，请务必选择一个 standby 方案可供随时切换</p>
        </div>
      ),
    },
    {
      id: 'q98',
      label: '100% 的代码由 LLM 生成是真的吗？',
      content: (
        <div className='space-y-3'>
          <p>是真的，本站目前所有的代码均由 LLM 生成，并由本人进行审核完成</p>
        </div>
      ),
    },
    {
      id: 'q99',
      label: '项目是否开源？技术栈？',
      content: (
        <div className='space-y-3'>
          <p>目前不开源，如果后续无法继续稳定维护，会考虑开源给社区</p>
          <ul className='list-disc'>
            <li>
              前端
              <ul className='list-disc'>
                <li>框架：React、Next.js</li>
                <li>数据库：IndexedDB (Dexie.js)</li>
                <li>状态管理：Zustand</li>
                <li>UI 库：Radix Primitives、Tailwind + custom-made OKLCH color palettes、Kladewind</li>
              </ul>
            </li>
            <li>
              后端
              <ul className='list-disc'>
                <li>框架：Hono、ElysiaJS</li>
                <li>数据库：PostgreSQL (Drizzle ORM)、Valkey、Redis</li>
                <li>运行时：Bun</li>
                <li>Serverless 平台：Vercel Edge Functions、Cloudflare Workers</li>
              </ul>
            </li>
            <li>
              同构弹幕库：
              <a href='https://github.com/simon300000/bilibili-live-ws' target='_blank'>
                bilibili-live-ws
              </a>
            </li>
            <li>
              浏览器扩展：
              <a href='https://github.com/laplace-live/login-sync' target='_blank'>
                laplace-login-sync
              </a>
            </li>
          </ul>
        </div>
      ),
    },
    {
      id: 'q100',
      label: '为什么不叫弹幕姬？',
      content: <p>为什么不叫弹幕先生/弹幕先辈/弹幕君/弹幕鸡/弹幕姬/弹幕黑妹/弹幕昆/弹幕基/弹麻先生/弹麻先辈/弹麻君/弹麻鸡/弹麻姬/弹麻黑妹/弹麻昆/弹麻基/大母鸡/大母基/大母姬/大母先生/大母先辈/大母君/大母黑妹/大母昆？</p>,
    },
    {
      id: 'q1000',
      label: '仍有疑问？',
      content: (
        <p>
          疑问、意见建议、或有限的技术支持请加入{' '}
          <a href='https://discord.gg/hDc6jfmr7c' target='_blank'>
            Discord
          </a>
        </p>
      ),
    },
  ]

  const homeAccordionContent = [
    {
      id: 'guide',
      label: (
        <div className='flex items-center gap-2'>
          <IconHelpSquareRounded className='text-teal-500' />
          项目介绍以及使用方法
        </div>
      ),
      content: (
        <div className='space-y-3'>
          <div className='grid grid-cols-1 gap-2.5 @xl:grid-cols-2 @3xl:grid-cols-3'>
            <div className='flex gap-2 rounded-md border border-lime-500 bg-lime-500/10 p-3'>
              <IconTransfer className='shrink-0 text-lime-500' />
              <div className='text-balance text-lime-900 dark:text-lime-500'>
                一个程序两种模式，纯 web 技术实现。可用于 OBS 组件，也可用于读弹幕谢礼物。无需下载客户端，自动更新
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-emerald-500 bg-emerald-500/10 p-3'>
              <IconSettingsCog className='shrink-0 text-emerald-500' />
              <div className='text-balance text-emerald-900 dark:text-emerald-500'>
                {/* 基于 web 的特性可以实现很多客户实现起来很困难的功能：比如弹幕复制、弹幕搜索、视频链接解析、查成分 */}
                支持与 OBS、VTube Studio 等第三方服务集成，在控制台中即可切换、管理场景、切换模型、触发表情等操作
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-blue-500 bg-blue-500/10 p-3'>
              <IconCloudDown className='shrink-0 text-blue-500' />
              <div className='text-balance text-blue-900 dark:text-blue-500'>
                云端事件同步：该功能可在未打开弹幕机时持续监控直播间，周期将事件同步至本地，真正做到不错过任何礼物
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-indigo-500 bg-indigo-500/10 p-3'>
              <IconBolt className='shrink-0 text-indigo-500' />
              <div className='text-balance text-indigo-900 dark:text-indigo-500'>
                超高性能，低资源占用。测试通过三倍原神直播间的弹幕量、运行数天不卡顿、无内存泄漏，界面操作相应迅速
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-purple-500 bg-purple-500/10 p-3'>
              <IconMessages className='shrink-0 text-purple-500' />
              <div className='text-balance text-purple-900 dark:text-purple-500'>
                支持弹幕联动：可将多个直播间的弹幕事件聚合，联动时显示在一个弹幕列表中，同时支持弹幕来源按样式区分
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-rose-500 bg-rose-500/10 p-3'>
              <IconAdjustmentsAlt className='shrink-0 text-rose-500' />
              <div className='text-balance text-rose-900 dark:text-rose-500'>
                高可定制性，所有的元素均提供语义化命名，内置完整的色盘以及 VS Code 同款编辑器，方便设计师定制样式
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-orange-500 bg-orange-500/10 p-3'>
              <IconVolume className='shrink-0 text-orange-500' />
              <div className='text-balance text-orange-900 dark:text-orange-500'>
                支持 TTS，弹幕自动朗读，支持系统内置的免费服务与第三方的付费服务，是全宇宙集成 TTS 服务最多的弹幕机
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-amber-500 bg-amber-500/10 p-3'>
              <IconLanguageHiragana className='shrink-0 text-amber-500' />
              <div className='text-balance text-amber-900 dark:text-amber-500'>
                支持弹幕翻译，内置 AI 大模型翻译可将弹幕与醒目留言翻译成其他语言，且效果优于市面上绝大多数的机器翻译
              </div>
            </div>
            <div className='flex gap-2 rounded-md border border-yellow-500 bg-yellow-500/10 p-3'>
              <IconLockCheck className='shrink-0 text-yellow-500' />
              <div className='text-balance text-yellow-900 dark:text-yellow-500'>
                隐私友好，所有的数据保存在本地浏览器，支持数据的导入导出，方便运营数据分析；无任何访问统计与 PII 收集
              </div>
            </div>
          </div>

          <div className='my-4 flex flex-wrap items-center gap-2'>
            <Button asChild leftSection={<IconPlayerPlayFilled size='1em' />} className='text-wrap'>
              <a href={`https://www.bilibili.com/video/BV1Hx4y1y7gR`} target='_blank' rel='noopener noreferrer'>
                弹幕内容显示不全如何处理 by ハイルラプラス
              </a>
            </Button>

            <Button asChild leftSection={<IconPlayerPlayFilled size='1em' />} className='text-wrap'>
              <a href={`https://www.bilibili.com/video/BV1Jk4y1p7HX`} target='_blank' rel='noopener noreferrer'>
                查看视频介绍 by 橙橙子君
              </a>
            </Button>

            <Button asChild leftSection={<IconBook2 size='1em' />} className='text-wrap'>
              <a href={`https://www.bilibili.com/video/BV1t8411U7re`} target='_blank' rel='noopener noreferrer'>
                进阶教程/样式设计 by 橙橙子君
              </a>
            </Button>

            <Button asChild leftSection={<IconBook2 size='1em' />} className='text-wrap'>
              <a
                href={`https://orangeeee.notion.site/orangeeee/2b721e066437463baff750c9120b9c31`}
                target='_blank'
                rel='noopener noreferrer'
              >
                使用指南@Notion by 橙橙子君
              </a>
            </Button>

            <Button asChild leftSection={<IconPlayerPlayFilled size='1em' />} className='text-wrap'>
              <a href={`https://www.bilibili.com/video/BV1BN4y1q7Xf`} target='_blank' rel='noopener noreferrer'>
                OBS 高级用法：场景自动切换 by ハイルラプラス
              </a>
            </Button>
          </div>

          <Accordion items={homeAccordionGuideContent} />
        </div>
      ),
    },
    {
      id: 'faq',
      label: (
        <div className='flex items-center gap-2'>
          <IconMessage2Question className='text-purple-500' />
          FAQ 常见问题解答
        </div>
      ),
      content: <Accordion items={homeAccordionFaqContent} />,
    },
    {
      id: 'changelog',
      label: (
        <div className='flex items-center gap-2'>
          <IconNotebook className='text-blue-500' />
          更新日志：{currentChangelogTimestamp}
        </div>
      ),
      content: <DynamicChangelogList />,
    },
    {
      id: 'special-thanks',
      label: (
        <div className='flex items-center gap-2'>
          <IconHeart className='text-rose-500' />
          特别感谢
        </div>
      ),
      content: (
        <div className='space-y-3'>
          <p>按时间排序</p>
          <div className='grid gap-1'>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={2132180406} name='明前奶绿' /> - 项目初衷及建议提供
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={12236936} name='只熊KUMA' /> - 推广及建议提供
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={4261481} name='橙橙子君' /> - 视频宣传、建议提供以及测试协助
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={3802666} name='Ask是Asuka' /> - 功能建议提供
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={526225523} name='费可' /> - 功能建议提供
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={1954091502} name='岁己SUI' /> - 主题及功能建议提供
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={21935620} name='纸片人计划' /> - 功能建议及镜像站支援
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={3102384} name='猫裙少年泽远喵' /> - 对本项目的恶意造谣与抹黑
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={3493124712172000} name='魈格Yezpi' /> - 幻星平台测试协助
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={3494358269561570} name='池乐还是有点饿' /> - 幻星平台测试协助
            </div>
            <div className='flex items-center gap-1'>
              <BilibiliUser uid={1640282} name='moe糊糊' /> - 捉虫及测试协助
            </div>
          </div>
          <p>…以及所有其他对项目做出了贡献的人</p>
        </div>
      ),
    },
  ]

  return <Accordion variant='separated' items={homeAccordionContent} />
}
