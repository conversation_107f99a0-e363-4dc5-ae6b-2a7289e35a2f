// hooks/useDynamicStyles.ts
import { useEffect, useId, useRef } from 'react'

/**
 * Insert dynamic styles into the document head
 *
 * @param styleGenerator - A function that returns the styles to be inserted
 * @param deps - An array of dependencies that will trigger a re-render
 * @param customId - Optional custom ID for the style element
 */
export function useDynamicStyles(styleGenerator: () => string, deps: any[] = [], customId?: string) {
  const generatedId = useId()
  const id = customId || generatedId
  const styleElementRef = useRef<HTMLStyleElement | null>(null)

  useEffect(() => {
    // Generate the styles based on the provided function
    const styles = styleGenerator()

    // Create or find the style element
    if (!styleElementRef.current) {
      const styleElement = document.createElement('style')
      styleElement.id = `dynamic-${id}`
      document.head.appendChild(styleElement)
      styleElementRef.current = styleElement
    }

    // Set the styles
    if (styleElementRef.current) {
      styleElementRef.current.textContent = styles // textContent is faster than innerHTML
    }

    // Cleanup
    return () => {
      if (styleElementRef.current) {
        document.head.removeChild(styleElementRef.current)
        styleElementRef.current = null
      }
    }
  }, deps)
}
