@layer template {
  .event {
    --avatar-size: calc(var(--event-font-size) * 2);
    --gift-size: calc(var(--event-font-size) * 3.75);
    --fallback-text: var(--event-gift-text, var(--event-message-text, var(--text-color)));
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    /* color: var(--fallback-text); */

    border-radius: var(--event-border-radius);
    color: var(--event-gift-text, #fff);
    background: var(--event-lottery-start-bg, var(--indigo-darker));

    &.dark {
      color: var(--event-lottery-start-text, #fff);
      background: var(--event-lottery-start-bg, var(--indigo-50));
    }
  }

  .read {
    opacity: 0.5 !important;
    filter: grayscale(0.8) !important;
  }

  .eventSizeHighlight {
    --gift-size: calc(var(--event-font-size) * 3.75);

    font-size: calc(var(--event-font-size) * 1.125);

    @container (max-width: 320px) {
      --gift-size: calc(var(--event-font-size) * 2);
    }

    .message {
      line-height: var(--event-line-height);
      word-break: break-word;
    }

    .top {
      display: inline-flex;
      align-items: center;
      gap: calc(var(--event-font-size) * 0.375);
      line-height: var(--event-line-height);
      font-weight: bold;
    }

    .content {
      position: relative;
      display: grid;
      padding-right: calc(var(--avatar-size) * 1.25);
      gap: calc(var(--event-font-size) * 0.25);
    }

    .giftIconWrap {
      position: absolute;
      top: 0;
      right: 0;

      display: inline-block;
      /* This help avoid inline image height issues */
      max-height: var(--gift-size);
      pointer-events: none;

      img {
        width: var(--gift-size);
        height: var(--gift-size);
      }
    }
  }

  .priceCurrency {
    opacity: 0.8;
    font-weight: normal;
  }

  .priceFigure {
    font-weight: bold;
  }

  .event.showAs_sticky {
    --avatar-size: var(--event-line-height);
    --gift-size: calc(var(--event-line-height) * 1.25);
    --event-border-radius: calc(var(--1px) * 100);

    font-size: var(--event-font-size);
    padding: calc(var(--event-font-size) * 0.25);
    padding-right: calc(var(--event-font-size) * 0.5);

    .giftIconWrap {
      top: calc(var(--1px) * -3);
    }
  }

  .hidden {
    display: none;
  }
}
