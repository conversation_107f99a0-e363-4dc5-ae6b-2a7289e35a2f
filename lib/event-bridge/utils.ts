import { isLocalNetwork } from '@/utils/isLocalNetwork'

export type ConnectionSettings = {
  address: string
  port: number
  password?: string
}

// Create a helper function to get connection URL
export function getConnectionUrl(settings: ConnectionSettings): string {
  const isLocal = isLocalNetwork(settings.address)
  const protocol = isLocal ? 'ws' : 'wss'
  return `${protocol}://${settings.address}:${settings.port}`
}
