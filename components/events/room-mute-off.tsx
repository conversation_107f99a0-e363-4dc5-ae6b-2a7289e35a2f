import React from 'react'
import clsx from 'clsx'

import type { RoomMuteOff } from '@/lib/event-parsers/types/room-mute-off'

import type { EventModeProps } from '@/components/event'

import styles from './room-mute-off.module.css'

export interface RoomMuteOffProps extends React.HTMLAttributes<HTMLDivElement> {
  data: RoomMuteOff
  mode: EventModeProps
}

export default function RoomMuteOffItem({ data, mode, ...rest }: RoomMuteOffProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <div className='message'>{data.message}</div>
    </div>
  )
}
