import React from 'react'
import clsx from 'clsx'

import type { Notice } from '@/lib/event-parsers/types/notice'

import { useOptions } from '@/hooks/useOptions'

import type { EventModeProps } from '@/components/event'
import { parseNoticeNode } from '@/components/events/notice-node'

import styles from './notice.module.css'

export interface NoticeProps extends React.HTMLAttributes<HTMLDivElement> {
  data: Notice
  mode: EventModeProps
}

export default function NoticeItem({ data, mode, ...rest }: NoticeProps) {
  const options = useOptions()
  const { colorScheme } = options

  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <span className='message'>
        {data?.messageRaw && data?.messageRaw.length
          ? data.messageRaw.map((seg, idx) => {
              return (
                <span className='notice-seg' data-type={seg.type} key={idx}>
                  {parseNoticeNode(seg, colorScheme)}
                </span>
              )
            })
          : data.message}
      </span>
    </div>
  )
}
