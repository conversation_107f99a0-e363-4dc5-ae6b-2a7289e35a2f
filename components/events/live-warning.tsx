import React from 'react'
import clsx from 'clsx'

import type { LiveWarning } from '@/lib/event-parsers/types/live-warning'

import type { EventModeProps } from '@/components/event'

import styles from './live-warning.module.css'

export interface LiveWarningProps extends React.HTMLAttributes<HTMLDivElement> {
  data: LiveWarning
  mode: EventModeProps
}

export default function LiveWarningItem({ data, mode, ...rest }: LiveWarningProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <span className='message'>
        直播间 {data.uid} 被警告：{data.message}
      </span>
    </div>
  )
}
