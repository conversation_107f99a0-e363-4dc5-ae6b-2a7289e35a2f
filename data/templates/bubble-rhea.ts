const TemplateRhea = /* css */ `/* 紫色气泡 × 瑞娅Rhea by LAPLACE Chat */
/* 引入 Google Fonts 字体，理论上国内可以直接访问 */
@import url('https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,400;0,600;1,400;1,600&display=swap');

@layer template {
  /* 全局背景透明，可保证 OBS 载入弹幕机的过程中背景全程透明 */
  body { background-color: rgba(0, 0, 0, 0); }

  /* 全局事件变量 */
  .event {
    /* 应用全局自定义字体 */
    /* --font-sans 为 LAPLACE Chat 内置变量，可调用非衬线字体 */
    --event-font-family: 'Jost', var(--font-sans);
  }

  /* 强制隐藏粉丝勋章 */
  .fans-medal {
    display: none;
  }

  /* 弹幕自定义样式 */
  .event--message {
    --text: rgb(110, 62, 87);
    --bg: rgb(244, 222, 232);
  }

  /* 总督变量 */
  .event--message.guard-level--1 {
    --text: rgb(61, 24, 23);
    --bg: rgb(253, 213, 186);
  }

  /* 提督变量 */
  .event--message.guard-level--2 {
    --text: rgb(75, 29, 95);
    --bg: rgb(217, 204, 240);
  }

  /* 舰长变量 */
  .event--message.guard-level--3 {
    --text: rgb(26, 24, 85);
    --bg: rgb(200, 221, 252);
  }

  /* 主播变量 */
  .event--message.user-type--streamer {
    --text: rgb(255, 255, 255);
    --bg: rgb(119, 110, 242);
  }

  /* 为事件增加一个全局的渐变，可在调用 OBS API 时增加颜色渐变效果，使场景切换时看上去没那么突兀 */
  .event {
    transition: .4s color ease, .4s background-color ease;
  }

  /* 当主题为暗色时，为用户名增加一个阴影，以保证在大部分深色背景的游戏场景遇到白色背景时依然可以看清用户名 */
  .scheme-dark .event--system,
  .scheme-dark .event--superchat,
  .scheme-dark .event--user-block,
  .scheme-dark .event--live-cutoff,
  .scheme-dark .event--toast .content,
  .scheme-dark .event--gift .content,
  .scheme-dark .event--like-click .username,
  .scheme-dark .event--interaction .username,
  .scheme-dark .event--message .username {
    text-shadow: 0 0 2px var(--bg-color), 0 0 2px var(--bg-color), 0 0 2px var(--bg-color);
  }

  /* 防止深色模式下礼物事件中的粉丝勋章出现阴影 */
  .fans-medal {
    text-shadow: none;
  }

  /* 弹幕正文改为聊天泡泡样式 */
  .event--message .message {
    --margin-top: 2;
    --margin-left: 28;
    position: relative;
    display: block;
    margin-top: calc(var(--1px) * var(--margin-top));
    margin-left: calc(var(--1px) * var(--margin-left));
    padding: calc(var(--1px) * 8) calc(var(--1px) * 14);
    color: var(--text);
    background-color: var(--bg);
    width: fit-content;
    min-width: 3em;
    border-radius: calc(var(--1px) * 18);
    filter: drop-shadow(0 0 1px currentColor);
    font-weight: bold;
  }

  /* 去掉大表情消息边框 */
  .event--message.has-emotes .message {
    background-color: transparent;
    padding: 0;
    filter: none;
  }

  /* 为聊天泡泡增加尾巴，半透明会导致箭头颜色叠加，因此不要应用于 [scene:blue] */
  .events-list-wrap:not(.scene-blue) .event--message:not(.has-emotes) .message::before {
    content: "";
    display: inline-block;
    position: absolute;
    top: calc(var(--1px) * -5);
    left: calc(var(--1px) * -12);
    border: calc(var(--1px) * 10) solid transparent;
    border-right: calc(var(--1px) * 18) solid var(--bg);
    transform: rotate(40deg);
  }

  /* 增大圆角 */
  .event--toast,
  .event--gift,
  .event--mvp,
  .event--superchat {
    --event-border-radius: calc(var(--1px) * 10);
  }

  /* 放大表情尺寸 */
  .event--message .emote {
    max-height: calc(var(--event-line-height) * 2);
  }

  /* 为自定义大表情固定高度，防止 OBS 模式滚动时显示不全 */
  .event--message .emote.emote--large {
    min-height: calc(var(--event-line-height) * 2);
  }

  /* 一个简易的渐变淡出效果，可以让组件边缘看起来没那么突兀 */
  .event-list {
    --offset: 30px;
    -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 1) var(--offset), rgba(0, 0, 0, 1) calc(100% - var(--offset)), rgba(0, 0, 0, 0));
            mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 1) var(--offset), rgba(0, 0, 0, 1) calc(100% - var(--offset)), rgba(0, 0, 0, 0));
  }
}
`

export default TemplateRhea
