import React from 'react'
import { clsx } from 'clsx'
import { IconThumbUpFilled } from '@tabler/icons-react'

import type { LikeClick } from '@/lib/event-parsers/types/like-click'
import useGlobalStore from '@/lib/store'

import { getPerkLevel } from '@/utils/eventAssetsUtils'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import Medal from '@/components/chat/medal'
import type { EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'
import UserDropdown from '@/components/UserDropdown'

import styles from './like-click.module.css'

export interface LikeClickProps extends React.HTMLAttributes<HTMLDivElement> {
  data: LikeClick
  mode: EventModeProps
}

export default function LikeClickItem({ data, mode, ...rest }: LikeClickProps) {
  const options = useOptions()
  const { showMedal, useCst } = options

  const dashboardLiveGuards = useGlobalStore(state => state.dashboardLiveGuards)

  return (
    <div
      className={clsx(
        styles.event,
        styles[`guardLevel${data?.medal?.guardType || 0}`],
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <TimeDisplay date={data.timestampNormalized} localTime={!useCst} />

      <div className={`${styles.meta} meta`}>
        <Avatar
          uid={data.uid}
          avatar={data.avatar}
          guard={data.medal.guardType}
          perkLevel={data?.medal?.resolvedPerkLevel || getPerkLevel(dashboardLiveGuards, { uid: data.medal.uid })}
        />

        {/* Fans medal badge */}
        {(showMedal || mode === 'dashboard') && (
          <Medal
            data={data.medal}
            mode={mode}
            perkLevel={
              data?.medal?.resolvedPerkLevel ||
              getPerkLevel(dashboardLiveGuards, {
                uid: data.medal.uid,
              })
            }
          />
        )}

        <div className={`${styles.username} username`}>
          {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}
          {mode === 'dashboard' && (
            <UserDropdown event={data}>
              <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
            </UserDropdown>
          )}
        </div>
      </div>
      <span className={`${styles.message} message`}>
        {`${data.message}`}
        <IconThumbUpFilled size={'1em'} />
      </span>
    </div>
  )
}
