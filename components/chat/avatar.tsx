import { useMemo, useState } from 'react'
import clsx from 'clsx'

import { Api } from '@/lib/const'

import { useOptions } from '@/hooks/useOptions'

import styles from './avatar.module.css'

export interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  uid: number
  /**
   * 默认 160px，会自动 x2 适配高分辨率，通常情况下不需要指定，建议通过父级组件的 CSS 变量传入
   * 如果传入此值，会影响具体头像 img 的尺寸，CSS 变量只会调整对应 img 的 width 和 height，而不是图片的实际尺寸
   * 因此如果通过父级组件传入的 CSS 变量尺寸很大，例如 64px，那么同样建议设置此值为 64，避免图片看起来很糊
   */
  size?: number
  /**
   * showing `guardType` (总督、提督、舰长)
   */
  guard?: number
  /**
   * force avatar, avatar from `uid` will be ignored when specified
   */
  avatar?: string
  /**
   * force avatar frame. frame from `guard` will be ignored when frame URL specified
   */
  frame?: string
  /**
   * 千舰、万舰的舰长框、舰长图标略有不同，传入该参数可进行区分。由于并不是所有事件都可以获取到舰长总数，因此需手动指定
   * 但是这个参数通常不需要手动传入，正常应该从父组件通过 CSS 变量传入
   * @deprecated 被 `perkLevel` 代替
   */
  frameRank?: number
  /**
   * 舰长头像框的等级，1-3
   * - 1 或者不传：普通
   * - 2：千舰
   * - 3：万舰
   */
  perkLevel?: number
  /**
   * 是否为舰队指挥官
   */
  guardLeader?: boolean
  /**
   * 是否正在直播
   */
  live?: boolean
  /**
   * 传入的 uid 是否为房间号，当为房间号时，则通过房间 API 获取头像
   */
  isRoom?: boolean
  className?: string
}

function Avatar({
  uid,
  size = 160,
  guard = 0,
  avatar,
  frame,
  frameRank = 1,
  perkLevel = 1,
  guardLeader = false,
  live = false,
  isRoom,
  className,
  ...props
}: AvatarProps & React.ComponentProps<'div'>) {
  const [avatarFailed, setAvatarFailed] = useState(false)
  const [avatarFrameFailed, setAvatarFrameFailed] = useState(false)
  const options = useOptions()
  const { showAvatarFrame, baseFontSize } = options

  // const apiBase = customAvatarApi || `https://edge-fetcher.xn--7dvy22i.com/bilibili/avatar/`
  // const apiBase = customAvatarApi || `https://edge-fetcher.vrp.moe/bilibili/avatar/`
  const apiBase = `${Api.Workers}/bilibili/avatar/`
  const avatarSize = useMemo(() => size * 2 * (baseFontSize / 20), [size, baseFontSize])
  const avatarUrl = avatar ? avatar : `${apiBase}${uid}?size=${avatarSize}${isRoom ? '&isRoom=1' : ''}`

  return (
    <div
      data-slot='avatar'
      className={clsx(
        styles.wrap,
        'avatar-wrap',
        styles[`guardLevel${guard}`],
        styles[`frameRank${frameRank}`],
        styles[`perkLevel${perkLevel}`],
        styles[`guardLeader${guardLeader ? '1' : '0'}`],
        `guard-level--${guard}`,
        `perk-level--${perkLevel}`,
        `guard-leader--${guardLeader ? '1' : '0'}`,
        live && `${styles.isLive} is-live`,
        className
      )}
      data-uid={uid}
      {...props}
    >
      {showAvatarFrame !== false &&
        (frame ? (
          <picture className={'avatar-frame-wrap'}>
            <img
              src={avatarFrameFailed ? `/transparent.png` : frame}
              alt={`头像框`}
              referrerPolicy='no-referrer'
              className={clsx(styles.avatarFrame, 'avatar-frame', avatarFrameFailed && 'avatar-frame-fallback')}
              onError={() => setAvatarFrameFailed(true)}
              loading='lazy'
            />
          </picture>
        ) : (
          <div className={`${styles.avatarFrame} avatar-frame`}></div>
        ))}
      <picture className={`${styles.avatarImgWrap} avatar-img-wrap`}>
        <img
          src={avatarFailed ? `/no-avatar.jpg` : avatarUrl}
          alt={`头像`}
          referrerPolicy='no-referrer'
          className={clsx(styles.avatar, 'avatar', avatarFailed && 'avatar-fallback')}
          onError={() => setAvatarFailed(true)}
          loading='lazy'
        />
      </picture>
    </div>
  )
}

export default Avatar
