// dashboard-metrics-panel.tsx
'use client'

import Image from 'next/image'
import clsx from 'clsx'
import { IconPhoto } from '@tabler/icons-react'

import type { DashboardRealtimeInfo } from '@/types'

import { useLocalStorage } from '@/hooks/useLocalStorage'

import Avatar from '@/components/chat/avatar'
import { UserMetaInfo } from '@/components/DashboardUserMetaInfo'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tooltip } from '@/components/ui/tooltip'

import styles from './EventListDashboard.module.css'

export function DashboardMetricsPanel({ data }: { data: DashboardRealtimeInfo[] }) {
  const [localStorageOptions] = useLocalStorage()
  const { dashboardShowMetrics } = localStorageOptions

  return (
    <div className={'group/basicInfo flex items-center gap-1'}>
      {data.length > 0 &&
        data[0] &&
        (() => {
          // Only get the first item to display
          const firstRoomData = data[0]

          if (!firstRoomData.uid) {
            return '基础信息载入错误'
          }

          return (
            <Popover>
              <PopoverTrigger className='focus-ring rounded-full focus-visible:ring-4'>
                {/* <IconSettings size='1.25rem' className='cursor-pointer' /> */}
                <div className={'relative z-1 flex items-center [--avatar-size:28px]'}>
                  <Avatar
                    uid={firstRoomData.uid}
                    live={firstRoomData.liveStatus === 1}
                    frame={firstRoomData.frameImage || ''}
                  />
                </div>
              </PopoverTrigger>
              <PopoverContent className='max-w-[500px] py-1'>
                <div>
                  {data.map((roomInfo, idx) => {
                    return (
                      <div
                        key={idx}
                        className={clsx(
                          '-mx-3 flex items-center gap-2 px-3 py-1',
                          styles.dropdownItem,
                          roomInfo.liveStatus !== 1 && 'opacity-50'
                        )}
                      >
                        <a
                          href={`https://live.bilibili.com/${roomInfo.room}`}
                          target='_blank'
                          rel='noreferrer noopener'
                          // https://github.com/radix-ui/primitives/issues/2248
                          onFocusCapture={e => {
                            e.stopPropagation()
                          }}
                        >
                          <Tooltip label={roomInfo.frameName || '打开直播间'}>
                            <div className={'flex items-center gap-1 [--avatar-size:36px]'}>
                              <Avatar
                                uid={roomInfo.uid}
                                live={roomInfo.liveStatus === 1}
                                frame={roomInfo.frameImage || ''}
                              />
                            </div>
                          </Tooltip>
                        </a>
                        <div>
                          <div className={'my-0.5 flex items-center gap-1'}>
                            <a
                              href={`https://space.bilibili.com/${roomInfo.uid}`}
                              className='font-semibold'
                              target='_blank'
                              rel='noreferrer noopener'
                            >
                              {`${roomInfo.name}`}
                            </a>
                            {roomInfo.keyframeImage ? (
                              <Tooltip
                                label={
                                  <div className={styles.liveKeyframeWrap}>
                                    <Image
                                      className={styles.liveKeyframe}
                                      src={roomInfo.keyframeImage}
                                      alt='keyframe'
                                      referrerPolicy='no-referrer'
                                      loading='lazy'
                                      fill
                                      unoptimized
                                    />
                                  </div>
                                }
                              >
                                <a
                                  href={roomInfo.keyframeImage}
                                  target='_blank'
                                  rel='noopener noreferrer'
                                  className='flex'
                                >
                                  <IconPhoto size={'1em'} />
                                </a>
                              </Tooltip>
                            ) : null}
                          </div>
                          <div>
                            {`${roomInfo.title}`} - {roomInfo.area}
                          </div>
                          <UserMetaInfo data={roomInfo} dashboardShowMetrics={dashboardShowMetrics} extra />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </PopoverContent>
            </Popover>
          )
        })()}

      <div className={'hidden sm:block'}>
        {data.length > 0 &&
          (() => {
            const key = Number(Object.keys(data)[0])
            const roomInfo = data[key]!
            return <UserMetaInfo data={roomInfo} dashboardShowMetrics={dashboardShowMetrics} />
          })()}
      </div>
    </div>
  )
}
