import type { BilibiliInternal } from '@laplace.live/internal'

import type { ColorScheme } from '@/lib/const'

import styles from './notice.module.css'

export function parseNoticeNode(
  data: BilibiliInternal.WebSocket.Prod.NoticeSegment,
  colorScheme: ColorScheme
): React.ReactNode[] {
  // text 可以能为空😅
  if (data.type === 1 && data.text) {
    const regex = /<%(.+?)%>/g
    const result: React.ReactNode[] = []
    let lastIndex = 0
    let match: RegExpExecArray | null

    // Base styles for normal text
    const resolvedStyles: React.CSSProperties = {}
    if (data.font_bold) {
      resolvedStyles.fontWeight = 'bold'
    }
    if (data.font_color) {
      if (
        data.font_color.toLowerCase() === '#cccccc' ||
        data.font_color.toLowerCase() === '#ffffff' ||
        data.font_color.toLowerCase() === '#f9f9f9'
      ) {
        resolvedStyles.color = '#555'
      } else {
        resolvedStyles.color = data.font_color
      }
    }
    if (data.font_color_dark && colorScheme === 'dark') {
      resolvedStyles.color = data.font_color_dark
    }
    if (data.background_color) {
      resolvedStyles.backgroundColor = Array.isArray(data.background_color)
        ? data.background_color[0]
        : data.background_color
    }
    if (data.background_color_dark && colorScheme === 'dark') {
      resolvedStyles.backgroundColor = Array.isArray(data.background_color_dark)
        ? data.background_color_dark[0]
        : data.background_color_dark
    }

    // Styles for highlighted text
    const resolvedHighlightStyles: React.CSSProperties = {
      ...resolvedStyles,
    }
    if (data.highlight_font_color) {
      resolvedHighlightStyles.color = data.highlight_font_color
    }
    if (data.highlight_font_color_dark && colorScheme === 'dark') {
      resolvedHighlightStyles.color = data.highlight_font_color_dark
    }

    // Using while loop with RegExp.exec for better performance than match/split
    while ((match = regex.exec(data.text)) !== null) {
      // Add the text before the match if it exists
      if (match.index > lastIndex) {
        // plain text
        result.push(
          <span key={`text-${lastIndex}`} className='notice-text' style={resolvedStyles}>
            {data.text.slice(lastIndex, match.index)}
          </span>
        )
      }

      // Add the highlighted text
      result.push(
        <span key={`highlight-${match.index}`} className={'notice-highlight'} style={resolvedHighlightStyles}>
          {match[1]}
        </span>
      )

      lastIndex = regex.lastIndex
    }

    // Add any remaining text after the last match
    if (lastIndex < data.text.length) {
      result.push(
        <span key={`text-${lastIndex}`} className='notice-text' style={resolvedStyles}>
          {data.text.slice(lastIndex)}
        </span>
      )
    }

    return result
  }

  if (data.type === 2 && data.img_url) {
    return [
      <span key={`image-${data.img_url}`} className={`${styles.noticeIconWrap} notice-icon-wrap`}>
        <picture className={`inline-flex`}>
          <img
            // className={clsx(loadIconFailed && 'gift-icon-fallback')}
            src={data.img_url}
            alt={'礼物'}
            referrerPolicy='no-referrer'
            loading='lazy'
          />
        </picture>
      </span>,
    ]
  }

  if (data.type === 3 && data.uri) {
    return [
      <a key={`link-${data.uri}`} className='notice-link' href={data.uri} target='_blank' rel='noopener noreferrer'>
        {data.text}
      </a>,
    ]
  }

  return []
}
