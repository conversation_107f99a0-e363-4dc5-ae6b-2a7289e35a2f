import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'

import { LOCAL_STORAGE_KEY } from '@/lib/const'

import { Alert } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Dropzone } from '@/components/ui/dropzone'
import { Label } from '@/components/ui/label'
import { SwitchWithLabel } from '@/components/ui/switch-with-label'

interface SettingsBackupRestoreProps {
  onRestoreComplete?: () => void
}

// Sensitive keys that should be marked for caution during export
const SENSITIVE_KEYS = ['loginSyncToken', 'laplaceLoginSyncToken', 'translateLoginSyncToken']

// Helper to mask sensitive data recursively
function maskSensitiveData(obj: unknown, includeSensitive: boolean): unknown {
  if (typeof obj !== 'object' || obj === null) {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(item => maskSensitiveData(item, includeSensitive))
  }

  const masked: Record<string, unknown> = {}
  for (const [key, value] of Object.entries(obj)) {
    // Mask sensitive values if not explicitly included
    if (SENSITIVE_KEYS.includes(key) && !includeSensitive) {
      // Set to empty string instead of asterisks
      masked[key] = ''
      continue
    }

    // If this is an object, recursively mask its values
    if (typeof value === 'object' && value !== null) {
      masked[key] = maskSensitiveData(value, includeSensitive)
    } else {
      masked[key] = value
    }
  }

  return masked
}

interface SettingItem {
  key: string
  size: number
  isSensitive: boolean
}

export function SettingsBackupRestore({ onRestoreComplete }: SettingsBackupRestoreProps) {
  const { t } = useTranslation()

  // Backup states
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [isBackupDialogOpen, setIsBackupDialogOpen] = useState(false)
  const [includeSensitiveData, setIncludeSensitiveData] = useState(false)
  const [availableSettings, setAvailableSettings] = useState<SettingItem[]>([])
  const [selectedSettings, setSelectedSettings] = useState<Record<string, boolean>>({})

  // Restore states
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [parsedSettings, setParsedSettings] = useState<Record<string, unknown>>({})
  const [selectedRestoreSettings, setSelectedRestoreSettings] = useState<Record<string, boolean>>({})

  // Load available settings when backup dialog opens
  useEffect(() => {
    if (isBackupDialogOpen) {
      loadAvailableSettings()
    }
  }, [isBackupDialogOpen])

  // Reset states when dialogs close
  useEffect(() => {
    if (!isBackupDialogOpen) {
      setIncludeSensitiveData(false)
    }
    if (!isRestoreDialogOpen) {
      setSelectedFiles([])
      setParsedSettings({})
      setSelectedRestoreSettings({})
    }
  }, [isBackupDialogOpen, isRestoreDialogOpen])

  // Load available settings from localStorage
  const loadAvailableSettings = () => {
    const settings: SettingItem[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        const item = localStorage.getItem(key) || ''
        settings.push({
          key,
          size: new Blob([item]).size,
          isSensitive: SENSITIVE_KEYS.includes(key),
        })
      }
    }

    setAvailableSettings(settings)

    // Initialize all settings as selected
    const initialSelection = settings.reduce<Record<string, boolean>>((acc, setting) => {
      acc[setting.key] = true
      return acc
    }, {})

    setSelectedSettings(initialSelection)
  }

  const handleSelectAllSettings = (selected: boolean) => {
    const newSelection = { ...selectedSettings }
    Object.keys(newSelection).forEach(key => {
      newSelection[key] = selected
    })
    setSelectedSettings(newSelection)
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) {
      setSelectedFiles([])
      setParsedSettings({})
      setSelectedRestoreSettings({})
      return
    }

    setSelectedFiles(files)

    try {
      const file = files[0]!
      const text = await file.text()
      const settings = JSON.parse(text)

      // Validate the restored data
      if (typeof settings !== 'object' || settings === null) {
        throw new Error('Invalid settings format')
      }

      setParsedSettings(settings)

      // Initialize all settings as selected for restore
      const initialRestoreSelection = Object.keys(settings).reduce<Record<string, boolean>>((acc, key) => {
        // Allow selecting all settings by default, regardless of value
        acc[key] = true
        return acc
      }, {})

      setSelectedRestoreSettings(initialRestoreSelection)
    } catch (error) {
      console.error('Parse error:', error)
      toast.error(t('Failed to restore settings'), {
        description: error instanceof Error ? error.message : 'Unknown error',
      })
      setSelectedFiles([])
    }
  }

  const handleBackup = () => {
    try {
      setIsBackingUp(true)

      // Get selected localStorage items
      const settings: Record<string, unknown> = {}

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && selectedSettings[key]) {
          try {
            settings[key] = JSON.parse(localStorage.getItem(key) || '')
          } catch {
            settings[key] = localStorage.getItem(key)
          }
        }
      }

      // Mask sensitive information recursively if needed
      const processedSettings = maskSensitiveData(settings, includeSensitiveData)

      // Create and download file
      const blob = new Blob([JSON.stringify(processedSettings, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      const withSensitiveData = includeSensitiveData ? '-token' : ''
      a.href = url
      a.download = `laplace-chat-settings-${new Date().toISOString().split('T')[0]}${withSensitiveData}.lcs`
      a.click()
      URL.revokeObjectURL(url)

      toast.success(t('Settings backed up successfully'))
      setIsBackupDialogOpen(false)
    } catch (error) {
      console.error('Backup error:', error)
      toast.error(t('Failed to backup settings'))
    } finally {
      setIsBackingUp(false)
    }
  }

  const handleRestore = async () => {
    if (selectedFiles.length === 0 || Object.keys(selectedRestoreSettings).length === 0) return

    try {
      setIsRestoring(true)

      // Store each selected setting in localStorage
      Object.entries(parsedSettings).forEach(([key, value]) => {
        // Skip if not selected
        if (!selectedRestoreSettings[key]) {
          return
        }

        try {
          localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value))
        } catch (error) {
          console.error(`Failed to restore setting ${key}:`, error)
        }
      })

      toast.success(t('Settings restored successfully'))
      onRestoreComplete?.()
      setIsRestoreDialogOpen(false)
    } catch (error) {
      console.error('Restore error:', error)
      toast.error(t('Failed to restore settings'), {
        description: error instanceof Error ? error.message : 'Unknown error',
      })
    } finally {
      setIsRestoring(false)
      setSelectedFiles([])
    }
  }

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
  }

  return (
    <div className='flex flex-wrap items-center gap-2'>
      {/* Backup Dialog */}
      <Dialog open={isBackupDialogOpen} onOpenChange={setIsBackupDialogOpen}>
        <DialogTrigger asChild>
          <Button>{t('Backup Settings…')}</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('Backup Settings')}</DialogTitle>
          </DialogHeader>
          <div className='space-y-4'>
            <SwitchWithLabel
              label={t('Select All')}
              checked={Object.values(selectedSettings).every(v => v)}
              onChange={e => handleSelectAllSettings(e.target.checked)}
            />

            <div className='-mx-4 max-h-60 space-y-2 overflow-y-auto border-y px-4 py-3'>
              {availableSettings.map(setting => (
                <div className='flex items-center' key={setting.key}>
                  <Checkbox
                    data-slot='checkbox-with-label'
                    id={setting.key}
                    checked={selectedSettings[setting.key] || false}
                    onChange={e => setSelectedSettings(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                  />
                  <Label htmlFor={setting.key} className='w-full pl-1'>
                    <div className='flex w-full items-center gap-1'>
                      <span className='flex-auto font-mono break-all'>{setting.key}</span>
                      <span className='text-fg/60 shrink-0 font-normal'>{formatBytes(setting.size)}</span>
                      {setting.isSensitive && <span className='text-warning ml-1 text-xs'>⚠️</span>}
                    </div>
                  </Label>
                </div>
              ))}
            </div>

            <SwitchWithLabel
              label={
                <span className='flex items-center gap-1'>
                  <span>包含同步登录信息</span>
                </span>
              }
              checked={includeSensitiveData}
              onChange={e => setIncludeSensitiveData(e.target.checked)}
              // disable this option if laplaceChatOptions_v4 is not selected
              disabled={!selectedSettings[LOCAL_STORAGE_KEY]}
            />

            {includeSensitiveData && <Alert tint='warning'>仅用于个人备份。请勿将备份文件分享给他人</Alert>}
          </div>
          <DialogFooter>
            <Button variant='link' disabled={isBackingUp} onClick={() => setIsBackupDialogOpen(false)}>
              {t('Cancel')}
            </Button>
            <Button
              onClick={handleBackup}
              disabled={isBackingUp || Object.values(selectedSettings).every(v => !v)}
              loading={isBackingUp}
              tint='accent'
            >
              {isBackingUp ? t('Restoring…') : t('Backup Settings')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Restore Dialog */}
      <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
        <DialogTrigger asChild>
          <Button>{t('Restore Settings…')}</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('Restore Settings')}</DialogTitle>
          </DialogHeader>
          <DialogDescription hidden>
            Select a previously backed up settings file (.lcs) to restore your settings.
          </DialogDescription>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='configFileInput'>{t('Select a LAPLACE Chat Settings (.lcs) to restore settings.')}</Label>
              <Dropzone
                id='configFileInput'
                accept='.lcs'
                onChange={handleFileSelect}
                disabled={isRestoring}
                className='w-full'
                multiple={false}
              />
            </div>

            {Object.keys(parsedSettings).length > 0 && (
              <>
                <SwitchWithLabel
                  label={t('Select All')}
                  checked={Object.values(selectedRestoreSettings).every(v => v)}
                  onChange={e => {
                    const newValue = e.target.checked
                    setSelectedRestoreSettings(prev =>
                      Object.fromEntries(Object.keys(prev).map(key => [key, newValue]))
                    )
                  }}
                />

                <div className='-mx-4 max-h-60 space-y-2 overflow-y-auto border-y px-4 py-3'>
                  {Object.entries(parsedSettings).map(([key, value]) => (
                    <div className='flex items-center' key={key}>
                      <Checkbox
                        data-slot='checkbox-with-label'
                        id={key}
                        checked={selectedRestoreSettings[key] || false}
                        onChange={e => setSelectedRestoreSettings(prev => ({ ...prev, [key]: e.target.checked }))}
                      />
                      <Label htmlFor={key} className='w-full pl-1'>
                        <div className='flex w-full items-center gap-1'>
                          <span className='flex-auto font-mono break-all'>{key}</span>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>

                <Alert tint='info'>
                  {t('Selected file will override your current settings. This action cannot be undone.')}
                </Alert>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant='link' disabled={isRestoring} onClick={() => setIsRestoreDialogOpen(false)}>
              {t('Cancel')}
            </Button>
            <Button
              tint='accent'
              onClick={handleRestore}
              disabled={
                isRestoring || selectedFiles.length === 0 || !Object.values(selectedRestoreSettings).some(v => v)
              }
              loading={isRestoring}
            >
              {isRestoring ? t('Restoring…') : t('Restore')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
