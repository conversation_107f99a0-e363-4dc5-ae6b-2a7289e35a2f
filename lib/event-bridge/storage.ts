import useLocalStorageState from 'use-local-storage-state'

import type { ConnectionSettings } from '@/lib/event-bridge/utils'

export const LEB_BRIDGE_SETTINGS_KEY = 'event_bridge_settings'
export const LEB_BRIDGE_AUTO_CONNECT_KEY = 'event_bridge_auto_connect'

/**
 * React hook to manage Event Bridge settings in localStorage
 * Returns a tuple with the current value and a setter function
 */
export function useEventBridgeSettings() {
  return useLocalStorageState<ConnectionSettings | null>(LEB_BRIDGE_SETTINGS_KEY, {
    defaultValue: null,
  })
}

/**
 * React hook to manage auto-connect setting in localStorage
 * Returns a tuple with the current value and a setter function
 */
export function useEventBridgeAutoConnect() {
  return useLocalStorageState<boolean>(LEB_BRIDGE_AUTO_CONNECT_KEY, {
    defaultValue: true,
  })
}
