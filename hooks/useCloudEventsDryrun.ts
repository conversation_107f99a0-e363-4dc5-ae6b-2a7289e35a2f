import useSWR, { type Fetcher } from 'swr'

import type { EventFetcherResp } from '@/types'

import { filterEmptyParams } from '@/utils/filterEmptyParams'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<EventFetcherResp> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching cloud events')
  }
  return response.json()
}

function useCloudEventsDryrun(roomId: number, dryRunUrl: string, dryRunAuth?: string) {
  const params = {
    full: 1,
    auth: dryRunAuth,
  }

  const urlParams = filterEmptyParams(params)

  const { data, error, isLoading, mutate, isValidating } = useSWR(
    dryRunUrl && roomId ? `${dryRunUrl}/events/${roomId}?${urlParams}` : null,
    fetcher,
    {
      refreshInterval: 0,
      revalidateOnFocus: false,
    }
  )

  return {
    eventsFromCloudDryrun: data,
    isCloudEventsDryrunLoading: isLoading,
    isCloudEventsDryrunError: error,
    isCloudEventsDryrunValidating: isValidating,
    cloudEventsDryrunMutate: mutate,
  }
}

export default useCloudEventsDryrun
