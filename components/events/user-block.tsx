import React from 'react'
import clsx from 'clsx'

import type { UserBlock } from '@/lib/event-parsers/types/user-block'

import type { EventModeProps } from '@/components/event'
import UserDropdown from '@/components/UserDropdown'

import styles from './user-block.module.css'

export interface UserBlockProps extends React.HTMLAttributes<HTMLDivElement> {
  data: UserBlock
  mode: EventModeProps
}

export default function UserBlockItem({ data, mode, ...rest }: UserBlockProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <span className='username'>
        {mode === 'obs' && <span className='username-text'>{data.username}</span>}

        {mode === 'dashboard' && (
          <UserDropdown event={data}>
            <span className='username-text cursor-pointer'>{data.username}</span>
          </UserDropdown>
        )}
      </span>

      <span className='message'>
        被 <span className='block-operator'>{data.operator === 1 ? `房管` : `主播`}</span> 禁言
        {data?.vaildPeriod ? (
          <>
            ，有效期 <span className='block-duration'>{data.vaildPeriod}</span>
          </>
        ) : (
          ''
        )}
      </span>
    </div>
  )
}
