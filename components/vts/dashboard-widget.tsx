import { useTranslation } from 'react-i18next'
import { IconInfoCircle } from '@tabler/icons-react'

import { cn } from '@/lib/cn'
import { useVTSStore } from '@/lib/vts/store'

import { nf } from '@/utils/numberFormat'

import { Divider } from '@/components/ui/divider'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { VTSExpressions } from '@/components/vts/expressions'

import { Button } from '../ui/button'
import { VTSHotkeys } from './hotkeys'
import { VTSModels } from './models'

export const VTSDashboard: React.FC = () => {
  const { isConnected, currentModel, stats } = useVTSStore()
  const { t } = useTranslation()

  if (!isConnected) {
    return null
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className='cursor-pointer px-1'>
          <StatCard
            title={
              <div className='flex items-center gap-x-0.5'>
                VTS
                {isConnected ? (
                  <div className='size-2 animate-pulse rounded-full bg-emerald-500' />
                ) : (
                  <div className='bg-fg/40 size-2 rounded-full' />
                )}
              </div>
            }
            value={currentModel?.modelName || 'Not loaded'}
            color={currentModel ? 'green' : 'default'}
            size='sm'
          />
        </div>
      </PopoverTrigger>

      <PopoverContent className='max-w-[calc(100dvw-10px)]'>
        <div className='space-y-3'>
          {currentModel && (
            <>
              <div className='grid grid-cols-2 gap-x-2 gap-y-3 sm:grid-cols-3'>
                <StatCard
                  title={t('vts.model', 'Model')}
                  value={
                    <div className='flex items-center gap-x-1'>
                      <div className='truncate'>{currentModel.modelName}</div>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant='link' size='sm' className='rounded-full p-0'>
                            <IconInfoCircle className='size-4' />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className='w-60'>
                          <div className='space-y-2'>
                            <div className='grid grid-cols-2 gap-1 text-xs'>
                              <span className='text-fg/60'>Load Time:</span>
                              <span>{(currentModel.modelLoadTime || 0) / 1000}s</span>

                              <span className='text-fg/60'>Parameters:</span>
                              <span>{currentModel.numberOfLive2DParameters || 'N/A'}</span>

                              <span className='text-fg/60'>Art Meshes:</span>
                              <span>{currentModel.numberOfLive2DArtmeshes || 'N/A'}</span>

                              <span className='text-fg/60'>Textures:</span>
                              <span>{currentModel.numberOfTextures || 'N/A'}</span>

                              <span className='text-fg/60'>Resolution:</span>
                              <span>
                                {currentModel.textureResolution ? `${currentModel.textureResolution}px` : 'N/A'}
                              </span>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  }
                  color='default'
                />

                {/* {stats && <StatCard title='Version' value={stats.version} color='default' />} */}

                {currentModel.numberOfLive2DParameters !== undefined && (
                  <StatCard
                    title={t('vts.parameters', 'Parameters')}
                    value={nf.format(currentModel.numberOfLive2DParameters)}
                    color='default'
                  />
                )}

                {currentModel.numberOfLive2DArtmeshes !== undefined && (
                  <StatCard
                    title={t('vts.artmeshes', 'Artmeshes')}
                    value={nf.format(currentModel.numberOfLive2DArtmeshes)}
                    color='default'
                  />
                )}
              </div>

              <Divider className='-mx-3 my-3 before:w-1.5' extended label={t('vts.expressions', 'Expressions')} />
              <VTSExpressions />

              <Divider className='-mx-3 my-3 before:w-1.5' extended label={t('vts.hotkeys', 'Hotkeys')} />
              <VTSHotkeys />

              <Divider className='-mx-3 my-3 before:w-1.5' extended label={t('vts.models', 'Models')} />
              <VTSModels />
            </>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Stat card component (copied from OBS dashboard for consistency)
interface StatCardProps {
  title: React.ReactNode
  value: React.ReactNode
  color: 'red' | 'yellow' | 'green' | 'default'
  size?: 'sm' | 'md'
}

const StatCard: React.FC<StatCardProps> = ({ title, value, color, size = 'md' }) => {
  const getColorClass = () => {
    switch (color) {
      case 'red':
        return 'bg-rose-500'
      case 'yellow':
        return 'bg-yellow-500'
      case 'green':
        return 'bg-emerald-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className={cn('grid items-center', size === 'sm' ? 'text-sm leading-4' : 'text-[18px]')}>
      <span className='text-fg/60 flex text-[11px] leading-none'>{title}</span>
      <div className='flex max-w-30 items-center gap-x-1'>
        <span className={'font-logo truncate font-semibold'}>{value}</span>
      </div>
    </div>
  )
}
