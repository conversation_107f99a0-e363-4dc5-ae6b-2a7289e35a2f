import React, { useState } from 'react'
import clsx from 'clsx'

import type { Mvp } from '@/lib/event-parsers/types/mvp'

import { getGiftRank } from '@/utils/getGiftRank'
import { nf } from '@/utils/numberFormat'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import type { AsComponentProps, EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'
import UserDropdown from '@/components/UserDropdown'

import styles from './mvp.module.css'

export interface MvpProps extends React.HTMLAttributes<HTMLDivElement> {
  data: Mvp
  mode: EventModeProps
  as: AsComponentProps
}

export default function MvpItem({ data, mode, as, ...rest }: MvpProps) {
  const [giftIconFailed, setGiftIconFailed] = useState(false)

  const options = useOptions()
  const { colorScheme, showGiftHighlightAbove, baseFontSize, useCst } = options

  const price = data.priceNormalized
  // const avatarSize = baseFontSize * 2 * 2
  // const isHighlighted = price > showGiftHighlightAbove
  // const imageUrlBg = `https://imageprx.openbayes.net/cdn-cgi/image/format=auto/${data.mvpIcon}`
  const priceDisplay = (
    <>
      <span className={`${styles.priceCurrency} price-currency`}>CN¥</span>
      <span className={`${styles.priceFigure} price-figure`}>{nf.format(price)}</span>
    </>
  )

  const giftRank = getGiftRank(price)

  return (
    <div
      className={clsx(
        styles.event,
        styles.eventSizeHighlight,
        styles[`mvpRank${giftRank}`],
        styles[`showAs_${as}`],
        colorScheme === 'dark' && [styles.dark, 'dark'],
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `event-show-as--${as}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        `mvp-rank--${giftRank}`,
        data.read && [styles.read, 'read']
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      data-mvp-price={price}
      data-mvp-amount={data.mvpAmount}
      data-mvp-rank={giftRank}
      data-read={data.read}
      {...rest}
    >
      <div className={`${styles.content} content`}>
        {as !== 'sticky' && <TimeDisplay date={data.timestampNormalized} localTime={!useCst} />}

        <div className={`${styles.top} top`}>
          <Avatar uid={data.uid} guard={data.guardType} />
          <Avatar uid={data.uid} guard={data.guardType} className={`${styles.hidden} avatar-alt-top`} />

          {as !== 'sticky' && (
            <div className={`${styles.username} username`}>
              {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}

              {mode === 'dashboard' && (
                <UserDropdown event={data}>
                  <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
                </UserDropdown>
              )}
            </div>
          )}

          <div className={`price`}>{priceDisplay}</div>

          {/* Main gift image, always show */}
          <span className={`${styles.giftIconWrap} mvp-icon-wrap`}>
            <picture>
              <img
                className={clsx(giftIconFailed && `gift-icon-fallback`)}
                src={giftIconFailed ? `/gift-fallback.png` : data.mvpIcon}
                alt={`礼物`}
                referrerPolicy='no-referrer'
                onError={() => setGiftIconFailed(true)}
                loading='lazy'
              />
            </picture>
          </span>

          {/* Alt gift image on top, hidden by default */}
          <span className={`${styles.giftIconWrap} ${styles.hidden} mvp-icon-wrap mvp-icon-wrap-alt-top`}>
            <picture>
              <img
                className={clsx(giftIconFailed && `gift-icon-fallback`)}
                src={giftIconFailed ? `/gift-fallback.png` : data.mvpIcon}
                alt={`礼物`}
                referrerPolicy='no-referrer'
                onError={() => setGiftIconFailed(true)}
                loading='lazy'
              />
            </picture>
          </span>
        </div>

        <div className={`${styles.hidden} price-alt`}>{priceDisplay}</div>

        {as !== 'sticky' && (
          <div className={`${styles.message} message`}>
            <span className='mvp-details-action'>{data.action}</span>
            <b className='mvp-details-message'>{data.message}</b>

            {/* Alt avatar image on bottom, hidden by default */}
            <Avatar uid={data.uid} guard={data.guardType} className={`${styles.hidden} avatar-alt-bottom`} />

            {/* Alt gift image on bottom, hidden by default */}
            <span className={`${styles.giftIconWrap} ${styles.hidden} mvp-icon-wrap mvp-icon-wrap-alt-bottom`}>
              <picture>
                <img
                  className={clsx(giftIconFailed && `gift-icon-fallback`)}
                  src={giftIconFailed ? `/gift-fallback.png` : data.mvpIcon}
                  alt={`礼物`}
                  referrerPolicy='no-referrer'
                  onError={() => setGiftIconFailed(true)}
                  loading='lazy'
                />
              </picture>
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
