'use client'

import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { IconLink, IconTrash, IconX } from '@tabler/icons-react'

import type { TemplateMetadata } from '@/types'

import { cn } from '@/lib/cn'

import { loadRemoteTemplate } from '@/utils/loadRemoteTemplate'

import { useDebouncedValue } from '@/hooks/useDebouncedValue'
import { useLocalStorage } from '@/hooks/useLocalStorage'

import { TemplateItem } from '@/components/editor-template-item'
import OptionLabel from '@/components/OptionLabel'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import Loading from '@/components/ui/loading'
import { ScrollArea } from '@/components/ui/scroll-area'

import { Skeleton } from './ui/skeleton'

// Built-in remote templates - just URLs
const BUILT_IN_REMOTE_TEMPLATES = [
  'https://pub-d4d706263dfc409fb4c0991a6f6ba4f1.r2.dev/gradient_chat/normal.css',
  'https://rsrc.laplace.cn/assets/bubble-snow.css',
]

interface RemoteTemplateSelectProps {
  onLoadTemplate?: (css: string) => void
}

export function RemoteTemplateSelect({ onLoadTemplate }: RemoteTemplateSelectProps) {
  const [typingRemoteUrl, setTypingRemoteUrl] = useState('')
  const [isRemoteTemplateLoading, setIsRemoteTemplateLoading] = useState(false)
  const [remoteTemplateError, setRemoteTemplateError] = useState<string | null>(null)
  const [isRemotePopoverOpen, setIsRemotePopoverOpen] = useState(false)

  // State for built-in template metadata
  const [builtInTemplateMetadata, setBuiltInTemplateMetadata] = useState<
    Array<{ url: string; metadata: TemplateMetadata | null; loading: boolean }>
  >(BUILT_IN_REMOTE_TEMPLATES.map(url => ({ url, metadata: null, loading: false })))

  // For remote template history tracking
  const [debouncedRemoteUrl] = useDebouncedValue(typingRemoteUrl, 800)

  // Remote template history
  const [remoteTemplateHistory, setRemoteTemplateHistory] = useState<
    Array<{ url: string; metadata: TemplateMetadata | null }>
  >([])

  const [localStorageOptions, setLocalStorageOptions] = useLocalStorage()
  const { remoteTheme } = localStorageOptions

  // Load remote template function
  const handleLoadRemoteTemplate = async (url: string) => {
    if (!url) {
      setRemoteTemplateError(null)
      return
    }

    try {
      setIsRemoteTemplateLoading(true)
      setRemoteTemplateError(null)

      const template = await loadRemoteTemplate(url)

      if (!template.success) {
        setRemoteTemplateError(template.error || '无法加载远程模板')
        return
      }

      // Update the actul theme we are using in localStorage
      setLocalStorageOptions({
        ...localStorageOptions,
        remoteTheme: url,
      })

      // Add to history and show success toast
      updateRemoteTemplateHistory(url, template.metadata || null)

      // Show appropriate success message
      if (template.metadata?.title) {
        toast.success(`远程模版已加载`, {
          description: template.metadata.title,
          id: `remote-template-loaded-${url}`,
        })
      } else {
        toast.success('远程模版已加载', {
          id: `remote-template-loaded-${url}`,
        })
      }

      setIsRemotePopoverOpen(false)

      // Apply CSS if requested - COMMENTED OUT: No longer applying CSS directly to editor
      // if (template.css) {
      //   alertDialog.open({
      //     title: '是否应用远程模版 CSS？',
      //     children: (
      //       <div>
      //         {template.metadata ? (
      //           <div className='space-y-2'>
      //             <p>已成功加载远程模版:</p>
      //             <div className='space-y-1 rounded border p-3'>
      //               <p>
      //                 <strong>标题:</strong> {template.metadata.title}
      //               </p>
      //               {template.metadata.description && (
      //                 <p>
      //                   <strong>描述:</strong> {template.metadata.description}
      //                 </p>
      //               )}
      //               <p>
      //                 <strong>作者:</strong> {template.metadata.author}
      //               </p>
      //               {template.metadata.version && (
      //                 <p>
      //                   <strong>版本:</strong> {template.metadata.version}
      //                 </p>
      //               )}
      //             </div>
      //             <p>是否要将这个模版应用到编辑器中？</p>
      //           </div>
      //         ) : (
      //           <div>已成功加载远程模版，是否将这个模版应用到编辑器中？</div>
      //         )}
      //       </div>
      //     ),
      //     labels: { confirm: '应用', cancel: '仅保存链接' },
      //     onCancel: () => {
      //       toast.success('远程模版链接已保存')
      //       // Add to history
      //       updateRemoteTemplateHistory(url, template.metadata || null)
      //     },
      //     onConfirm: () => {
      //       setEditorContent(template.css)
      //       setCustomCss(template.css)
      //       toast.success('远程模版已应用到编辑器')
      //       // Add to history
      //       updateRemoteTemplateHistory(url, template.metadata || null)
      //     },
      //   })
      // } else {
      //   toast.error('远程模版不包含有效的 CSS')
      // }

      // Apply CSS if requested and callback exists
      if (template.css && onLoadTemplate) {
        onLoadTemplate(template.css)
      }
    } catch (error) {
      setRemoteTemplateError(error instanceof Error ? error.message : '无法加载远程模板')
    } finally {
      setIsRemoteTemplateLoading(false)
    }
  }

  // Function to fetch metadata for built-in templates
  const loadBuiltInTemplateMetadata = async () => {
    // Mark all templates as loading
    setBuiltInTemplateMetadata(prev => prev.map(item => (item.metadata === null ? { ...item, loading: true } : item)))

    // Create promises for each template
    const metadataPromises = builtInTemplateMetadata.map(async item => {
      // Skip if already loaded
      if (item.metadata !== null) {
        return { url: item.url, metadata: item.metadata, loading: false }
      }

      try {
        // Add timeout to prevent indefinite loading
        const template = await Promise.race<ReturnType<typeof loadRemoteTemplate>>([
          loadRemoteTemplate(item.url),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Request timeout')), 8000)) as Promise<never>,
        ])

        if (template.success) {
          return { url: item.url, metadata: template.metadata || null, loading: false }
        } else {
          console.warn(`Failed to load metadata for template: ${item.url}`, template.error)
          return { url: item.url, metadata: null, loading: false }
        }
      } catch (error) {
        console.warn(`Failed to load metadata for template: ${item.url}`, error)
        return { url: item.url, metadata: null, loading: false }
      }
    })

    // Wait for all promises and update state once
    try {
      const results = await Promise.all(metadataPromises)
      setBuiltInTemplateMetadata(results)
    } catch (error) {
      console.warn('Error loading template metadata:', error)
      // Ensure loading states are cleared even on error
      setBuiltInTemplateMetadata(prev => prev.map(item => ({ ...item, loading: false })))
    }
  }

  // Add remote template to history
  const updateRemoteTemplateHistory = (url: string, metadata: TemplateMetadata | null) => {
    setRemoteTemplateHistory(prev => {
      // Remove if already exists
      const filtered = prev.filter(item => item.url !== url)
      // Add to the beginning
      return [{ url, metadata }, ...filtered].slice(0, 100) // Keep only the 100 most recent
    })
  }

  // Load from URL when debounced URL changes
  useEffect(() => {
    if (debouncedRemoteUrl && debouncedRemoteUrl !== remoteTheme) {
      handleLoadRemoteTemplate(debouncedRemoteUrl)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedRemoteUrl])

  // Initialize typing remote URL from localStorage
  useEffect(() => {
    if (remoteTheme) {
      setTypingRemoteUrl(remoteTheme)
    }
  }, [remoteTheme])

  // Load remote template history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('remoteTemplateHistory')
    if (history) {
      try {
        setRemoteTemplateHistory(JSON.parse(history))
      } catch (e) {
        console.error('Failed to parse remote template history')
      }
    }
  }, [])

  // Save remote template history to localStorage
  useEffect(() => {
    if (remoteTemplateHistory.length > 0) {
      localStorage.setItem('remoteTemplateHistory', JSON.stringify(remoteTemplateHistory))
    }
  }, [remoteTemplateHistory])

  // Load metadata for built-in templates when dropdown is opened
  useEffect(() => {
    if (isRemotePopoverOpen) {
      loadBuiltInTemplateMetadata()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRemotePopoverOpen])

  const clearRemoteTemplate = () => {
    setLocalStorageOptions({
      ...localStorageOptions,
      remoteTheme: '',
    })
    setTypingRemoteUrl('')
  }

  return (
    <div className='flex-auto space-y-2'>
      <Label htmlFor='remote-template-input' onClick={() => setIsRemotePopoverOpen(true)}>
        <OptionLabel label={'远程模版'} desc={'输入远程模版的 URL 地址，详细用法请访问文档'} />
      </Label>
      <DropdownMenu open={isRemotePopoverOpen} onOpenChange={setIsRemotePopoverOpen}>
        <DropdownMenuTrigger asChild>
          <div
            tabIndex={0}
            className='focus-ring data-[state=open]:border-ac mt-1 flex cursor-pointer flex-wrap gap-1 rounded-md border p-2 text-lg'
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault()
                e.currentTarget.click()
              }
            }}
          >
            {remoteTheme ? (
              <div className='flex w-full min-w-0 items-center gap-2 rounded-sm'>
                <TemplateItem item={remoteTemplateHistory.find(item => item.url === remoteTheme)?.metadata} />
              </div>
            ) : (
              <div className='flex-auto space-y-0.5'>
                <div className='text-fg/40'>远程模版</div>
                <div className='text-fg/40 text-sm'>在此输入一个远程模版，可从样式作者、开发者等第三方渠道获取</div>
              </div>
            )}
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className='max-h-[min(var(--radix-dropdown-menu-content-available-height),450px)] w-[var(--radix-dropdown-menu-trigger-width)] p-0'>
          <div className='shadow-border-b relative z-50 px-3'>
            <Input
              type='url'
              placeholder='https://example.com/theme.css'
              value={typingRemoteUrl}
              onChange={event => {
                setTypingRemoteUrl(event.currentTarget.value)
              }}
              className={cn(
                'border-none py-2 pl-6 text-base shadow-none inset-shadow-none focus-visible:ring-0',
                isRemoteTemplateLoading && 'pr-14'
              )}
              leftSection={<IconLink className='size-4 shrink-0 opacity-50' />}
              leftSectionClassName='pointer-events-none'
              rightSection={
                <div className='flex items-center'>
                  {isRemoteTemplateLoading ? <Loading /> : null}
                  {typingRemoteUrl ? (
                    <Button
                      className='-mr-2 p-2'
                      variant='link'
                      onClick={e => {
                        clearRemoteTemplate()
                      }}
                      aria-label='移除'
                    >
                      <IconX size='1.25rem' />
                    </Button>
                  ) : null}
                </div>
              }
            />
          </div>

          {remoteTemplateError && (
            <div className='bg-rose-500/10 px-3 py-2 font-mono text-sm text-rose-500'>{remoteTemplateError}</div>
          )}

          <ScrollArea
            className='z-40'
            viewportProps={{
              className: 'max-h-[calc(min(var(--radix-dropdown-menu-content-available-height),450px)-40px-5px)]',
            }}
          >
            {remoteTemplateHistory.length > 0 && (
              <div className='@container'>
                <DropdownMenuLabel>最近使用的模版</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {remoteTemplateHistory.map((item, index) => (
                  <div
                    key={`${item.url}-${index}`}
                    tabIndex={0}
                    className='focus-ring hover:bg-ac/10 flex cursor-pointer items-center justify-between gap-2 px-3 py-2'
                    aria-label={item.metadata?.title || item.url}
                    onClick={() => {
                      setTypingRemoteUrl(item.url)
                      handleLoadRemoteTemplate(item.url)
                      setIsRemotePopoverOpen(false)
                    }}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        e.currentTarget.click()
                      }
                    }}
                  >
                    <TemplateItem item={item.metadata} url={item.url} withDetails />

                    <Button
                      onClick={e => {
                        e.stopPropagation()
                        setRemoteTemplateHistory(prev => prev.filter(template => template.url !== item.url))
                      }}
                      className='p-1.5'
                      size='sm'
                      tint='rose'
                      aria-label='移除'
                    >
                      <IconTrash className='size-4' />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {!remoteTemplateHistory.length && typingRemoteUrl === '' && (
              <div className='text-fg/60 p-3 text-sm'>暂无记录。输入远程模版 URL 并加载后将会显示在这里。</div>
            )}

            {/* Built-in Remote Templates */}
            <div className='@container'>
              <DropdownMenuLabel>内置远程模版</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {builtInTemplateMetadata.map((template, index) => (
                <div
                  key={`built-in-${index}`}
                  tabIndex={0}
                  className='focus-ring hover:bg-ac/10 flex cursor-pointer items-center justify-between gap-2 px-3 py-2'
                  onClick={() => {
                    setTypingRemoteUrl(template.url)
                    handleLoadRemoteTemplate(template.url)
                    setIsRemotePopoverOpen(false)
                  }}
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      e.currentTarget.click()
                    }
                  }}
                >
                  {template.loading ? (
                    <div className='flex w-full flex-col items-start gap-2 @sm:flex-row'>
                      <Skeleton className='h-24 w-30 shrink-0' />
                      <div className='min-w-0 flex-auto space-y-1'>
                        <Skeleton className='h-6 w-24' />
                        <Skeleton className='h-5 w-48' />
                        <Skeleton className='h-5 w-48' />
                        <Skeleton className='h-4 w-full' />
                      </div>
                    </div>
                  ) : (
                    <TemplateItem item={template.metadata} url={template.url} withDetails />
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </DropdownMenuContent>
      </DropdownMenu>
      <div className='text-fg/60 text-sm'>请勿添加未知来源的链接，否则可能导致弹幕机样式异常</div>
    </div>
  )
}
