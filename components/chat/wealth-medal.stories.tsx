import type { <PERSON>a, StoryObj } from '@storybook/react'

import WealthMedal from '@/components/chat/wealth-medal'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Components/Wealth Medal',
  component: WealthMedal,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof WealthMedal>

export default meta
type Story = StoryObj<typeof meta>

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    data: 50,
  },
}

export const AllLevels: Story = {
  args: {
    data: 1,
  },
  render: () => {
    return (
      <div className='flex w-[560px] flex-wrap gap-2'>
        {Array.from({ length: 101 }, (_, idx) => (
          <WealthMedal key={idx} data={idx} />
        ))}
      </div>
    )
  },
}
