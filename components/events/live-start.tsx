import React from 'react'
import clsx from 'clsx'

import type { LiveStart } from '@/lib/event-parsers/types/live-start'

import type { EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'

import styles from './live-start.module.css'

export interface LiveStartProps extends React.HTMLAttributes<HTMLDivElement> {
  data: LiveStart
  mode: EventModeProps
}

export default function LiveStartItem({ data, mode, ...rest }: LiveStartProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <span className='message'>
        {data.message} <TimeDisplay date={data.timestampNormalized} localTime={false} forceDisplay />
      </span>
    </div>
  )
}
