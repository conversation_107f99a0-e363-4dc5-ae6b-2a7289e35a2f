import { cn } from '@/lib/cn'

import Avatar from '@/components/chat/avatar'
import { Skeleton } from '@/components/ui/skeleton'

export function BiliibliUserWithRoom({
  uid,
  username,
  roomId,
  className,
  ...props
}: {
  uid: number | string
  username: React.ReactNode
  roomId: React.ReactNode
  className?: string
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn('flex items-center gap-2 [--avatar-size:40px]', className)} {...props}>
      <Avatar uid={Number(uid)} />
      <div>
        <div className='text-base'>{username}</div>
        <div className='text-sm opacity-60'>{roomId}</div>
      </div>
    </div>
  )
}

export function BiliibliUserWithRoomSkeleton() {
  return (
    <div className='flex items-center gap-2'>
      {/* <Loading size='40px' width='5px' /> */}
      <Skeleton className='size-[40px] rounded-full' />
      <div className='space-y-2 py-1.5'>
        <Skeleton className='h-3 w-24' />
        <Skeleton className='h-3 w-16' />
      </div>
    </div>
  )
}
