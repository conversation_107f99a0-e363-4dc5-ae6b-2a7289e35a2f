import { useTranslation } from 'react-i18next'

import { cn } from '@/lib/cn'
import { useOBSStore } from '@/lib/obs/store'

import { nf } from '@/utils/numberFormat'

import { OBSControls } from '@/components/obs/controls'
import { SceneName } from '@/components/obs/scene-name'
import { OBSScenes } from '@/components/obs/scenes'
import { Divider } from '@/components/ui/divider'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

export const OBSDashboard: React.FC = () => {
  const { isConnected, stats, streamStatus, currentScene } = useOBSStore()
  const { t } = useTranslation()

  // Format a value with unit
  const formatValue = (value: number, unit: string, precision = 2): string => {
    return `${value.toFixed(precision)} ${unit}`
  }

  // Format percentage
  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`
  }

  // Format bitrate
  const formatBitrate = (bytes: number): string => {
    const num = parseInt((bytes * 8).toFixed(0))
    return `${nf.format(num)} kbps`
  }

  if (!isConnected) {
    return null
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className='cursor-pointer px-1'>
          <StatCard
            title={
              <div className='flex items-center gap-x-0.5'>
                OBS
                {streamStatus?.outputActive ? (
                  <div className='size-2 animate-pulse rounded-full bg-rose-500' />
                ) : (
                  <div className='bg-fg/40 size-2 rounded-full' />
                )}
              </div>
            }
            value={currentScene ? <SceneName sceneName={currentScene} /> : 'N/A'}
            color={streamStatus?.outputActive ? 'green' : 'default'}
            size='sm'
          />
        </div>
      </PopoverTrigger>

      <PopoverContent className='max-w-[calc(100dvw-10px)]'>
        <div className='space-y-3'>
          <OBSScenes />

          <Divider className='-mx-3 my-3 before:w-1.5' extended label={t('obs.stats')} />

          <div className='grid grid-cols-2 gap-x-2 gap-y-3 sm:grid-cols-3'>
            {streamStatus && streamStatus.outputActive && (
              <>
                <StatCard
                  title={t('obs.bitrate')}
                  value={formatBitrate(
                    streamStatus.outputDuration ? streamStatus.outputBytes / streamStatus.outputDuration : 0
                  )}
                  color='default'
                />

                <StatCard
                  title={t('obs.droppedFrames')}
                  value={`${nf.format(streamStatus.outputSkippedFrames)} / ${nf.format(streamStatus.outputTotalFrames)} (${formatPercentage((streamStatus.outputSkippedFrames / Math.max(1, streamStatus.outputTotalFrames)) * 100)})`}
                  color={streamStatus.outputSkippedFrames > 0 ? 'yellow' : 'green'}
                />

                <StatCard
                  title={t('obs.congestion')}
                  value={formatPercentage(streamStatus.outputCongestion * 100)}
                  color={
                    streamStatus.outputCongestion > 0.5
                      ? 'red'
                      : streamStatus.outputCongestion > 0.1
                        ? 'yellow'
                        : 'green'
                  }
                />
              </>
            )}

            {stats && (
              <>
                <StatCard
                  title={t('obs.fps')}
                  value={formatValue(stats.activeFps, 'fps', 1)}
                  color={stats.activeFps < 30 ? 'yellow' : 'green'}
                />
                <StatCard
                  title={t('obs.cpu')}
                  value={formatPercentage(stats.cpuUsage)}
                  color={stats.cpuUsage > 80 ? 'red' : stats.cpuUsage > 50 ? 'yellow' : 'green'}
                />

                <StatCard
                  title={t('obs.memory')}
                  value={`${nf.format(stats.memoryUsage, { maximumFractionDigits: 0 })} MB`}
                  color={stats.memoryUsage > 80 ? 'red' : stats.memoryUsage > 50 ? 'yellow' : 'green'}
                />

                <StatCard
                  title={t('obs.skippedFramesOutput')}
                  value={stats.outputSkippedFrames.toString()}
                  color={stats.outputSkippedFrames > 0 ? 'yellow' : 'green'}
                />

                <StatCard
                  title={t('obs.skippedFramesRender')}
                  value={stats.renderSkippedFrames.toString()}
                  color={stats.renderSkippedFrames > 0 ? 'yellow' : 'green'}
                />

                <StatCard
                  title={t('obs.frameRenderTime')}
                  value={formatValue(stats.averageFrameRenderTime, 'ms')}
                  color={stats.averageFrameRenderTime > 10 ? 'yellow' : 'green'}
                />
              </>
            )}
          </div>

          <Divider className='-mx-3 my-3 before:w-1.5' extended label={t('obs.controls')} />

          <OBSControls />
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Stat card component
interface StatCardProps {
  title: React.ReactNode
  value: React.ReactNode
  color: 'red' | 'yellow' | 'green' | 'default'
  size?: 'sm' | 'md'
}

const StatCard: React.FC<StatCardProps> = ({ title, value, color, size = 'md' }) => {
  const getColorClass = () => {
    switch (color) {
      case 'red':
        return 'bg-rose-500'
      case 'yellow':
        return 'bg-yellow-500'
      case 'green':
        return 'bg-emerald-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className={cn('grid items-center', size === 'sm' ? 'text-[14px] leading-4' : 'text-[18px]')}>
      <span className='text-fg/60 flex text-[11px] leading-none'>{title}</span>
      <div className='flex max-w-30 items-center gap-x-1'>
        {/* <div className={`w-1 h-4 rounded-md ${getColorClass()}`} /> */}
        <span className={'font-logo truncate font-semibold'}>{value}</span>
      </div>
    </div>
  )
}
