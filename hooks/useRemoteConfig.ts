import useSWR, { type Fetcher } from 'swr'
import type { LaplaceInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<LaplaceInternal.HTTPS.Workers.RemoteConfig> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching remote config')
  }
  return response.json()
}

function useRemoteConfig(domain: string) {
  const { data, error, isLoading } = useSWR(
    domain ? `${Api.Workers}/laplace/remote-config?domain=${domain}` : null,
    fetcher,
    {
      refreshInterval: 0,
      revalidateOnFocus: false,
    }
  )

  return {
    remoteConfig: data,
    isRemoteConfigLoading: isLoading,
    isRemoteConfigError: error,
  }
}

export default useRemoteConfig
