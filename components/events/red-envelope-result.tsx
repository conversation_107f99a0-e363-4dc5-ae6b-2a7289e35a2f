import React, { useState } from 'react'
import clsx from 'clsx'

import type { RedEnvelopeResult } from '@/lib/event-parsers/types/red-envelope-result'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import type { EventModeProps } from '@/components/event'
import { Tooltip } from '@/components/ui/tooltip'
import UserDropdown from '@/components/UserDropdown'

import styles from './red-envelope-result.module.css'

export interface RedEnvelopeResultProps extends React.HTMLAttributes<HTMLDivElement> {
  data: RedEnvelopeResult
  mode: EventModeProps
}

const INITIAL_DISPLAY_COUNT = 12

export default function RedEnvelopeResultItem({ data, mode, ...rest }: RedEnvelopeResultProps) {
  const [showAll, setShowAll] = useState(false)
  const options = useOptions()
  const { colorScheme } = options

  const totalUsers = data.list.length
  const shouldShowToggle = mode === 'dashboard' && totalUsers > INITIAL_DISPLAY_COUNT

  // Determine which users to display
  const visibleUsers = (() => {
    if (mode === 'obs') {
      return data.list.slice(0, INITIAL_DISPLAY_COUNT)
    }
    if (shouldShowToggle && !showAll) {
      return data.list.slice(0, INITIAL_DISPLAY_COUNT)
    }
    return data.list
  })()

  return (
    <div
      className={clsx(
        styles.event,
        colorScheme === 'dark' && [styles.dark, 'dark'],
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        data.list.length === 0 && 'no-rewards',
        data.read && [styles.read, 'read']
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      data-read={data.read}
      {...rest}
    >
      <div className={`${styles.content} content`}>
        <div className={`${styles.message} message`}>
          <div className={`${styles.detailsName} red-envelope-result-details-name`}>
            {data.message}
            {totalUsers > 0 && `（共 ${totalUsers} 人）`}

            {/* Show toggle button for dashboard mode */}
            {shouldShowToggle && (
              <button
                className={`${styles.toggleButton} red-envelope-result-toggle`}
                onClick={() => setShowAll(!showAll)}
              >
                {showAll ? '收起' : `展开`}
              </button>
            )}
          </div>

          <div
            className={clsx(
              styles.rewardsItemsMask,
              (mode === 'obs' || (shouldShowToggle && !showAll)) && [styles.isMasked, 'is-masked'],
              'red-envelope-result-mask'
            )}
          >
            <div className={`${styles.rewardsItemsWrap} red-envelope-result-list`}>
              {data.list && data.list.length > 0 ? (
                visibleUsers.map(item => {
                  return (
                    <div key={item[0]} className={`${styles.rewardsItemWrap} list-item-wrap`}>
                      {mode === 'obs' && <Avatar uid={item[0]} />}

                      {mode === 'dashboard' && (
                        <UserDropdown
                          event={{
                            ...data,
                            uid: item[0] || 0,
                            username: item[1],
                          }}
                        >
                          <div className='cursor-pointer'>
                            <Tooltip label={item[1]}>
                              <Avatar uid={item[0]} />
                            </Tooltip>
                          </div>
                        </UserDropdown>
                      )}
                    </div>
                  )
                })
              ) : (
                <div className='list-item-wrap'>无人中奖</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
