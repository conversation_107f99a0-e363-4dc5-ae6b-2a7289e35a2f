import { useEffect, useState } from 'react'

import { useEventBridgeAutoConnect, useEventBridgeSettings } from '@/lib/event-bridge/storage'
import { useEventBridgeStore } from '@/lib/event-bridge/store'

/**
 * A component that automatically initializes and manages Event Bridge connection
 * without rendering any UI elements. It should be included high in the
 * component tree to ensure Event Bridge connection is initialized early.
 */
export const EventBridgeConnectionInitializer: React.FC = () => {
  const [storedSettings] = useEventBridgeSettings()
  const [autoConnect] = useEventBridgeAutoConnect()
  const [hasAttemptedConnect, setHasAttemptedConnect] = useState(false)

  // Get Event Bridge state and actions from the store
  const { isConnected, isConnecting, connect } = useEventBridgeStore()

  // Auto-connect when component mounts if auto connect is enabled and we have stored settings
  useEffect(() => {
    const attemptConnect = async () => {
      if (
        storedSettings &&
        !isConnected &&
        !isConnecting &&
        !hasAttemptedConnect &&
        autoConnect // Only connect if "auto connect" is enabled
      ) {
        setHasAttemptedConnect(true)
        await connect(storedSettings)
      }
    }

    attemptConnect()
  }, [storedSettings, isConnected, isConnecting, hasAttemptedConnect, autoConnect, connect])

  // This component doesn't render anything visible
  return null
}
