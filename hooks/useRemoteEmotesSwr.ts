import useSWR, { type Fetcher } from 'swr'
import type { LaplaceInternal } from '@laplace.live/internal'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<LaplaceInternal.HTTPS.Workers.PspRemoteEmotesProps> = async (
  ...args: Parameters<typeof fetch>
) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching remote emotes')
  }
  return response.json()
}

function useRemoteEmotes(roomId: string) {
  const sanitizedQuery = roomId.replace(/[^0-9]/g, '')

  const { data, error, isLoading } = useSWR(
    sanitizedQuery ? `https://api-work.psplive.com/ChatEmoji/getEmojiInfo?brid=${sanitizedQuery}` : null,
    fetcher,
    {
      refreshInterval: 0,
      revalidateOnFocus: false,
    }
  )

  return {
    remoteStickers: data,
    isRemoteStickersLoading: isLoading,
    isRemoteStickersError: error,
  }
}

export default useRemoteEmotes
