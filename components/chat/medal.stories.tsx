import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'

import { DanmakuMock } from '@/utils/mock'

import Medal from '@/components/chat/medal'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Components/Medal',
  component: Medal,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof Medal>

export default meta
type Story = StoryObj<typeof meta>

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    data: DanmakuMock.medal,
  },
}

export const CustomPerkLevel: Story = {
  args: {
    data: {
      lightened: 1,
      level: 29,
      name: '大奶绿',
      room: 25034104,
      guardType: 0,
    },
    perkLevel: 2,
  },
  render: ({ data }) => {
    return (
      <div className='flex gap-2'>
        <Medal data={{ ...data, guardType: 3 }} perkLevel={2} />
        <Medal data={{ ...data, guardType: 2 }} perkLevel={2} />
        <Medal data={{ ...data, guardType: 1 }} perkLevel={2} />
      </div>
    )
  },
}

// Bucket Level
export const MedalBuckets: Story = {
  args: {
    data: {
      lightened: 1,
      level: 1,
      name: '小奶绿',
      room: 25034104,
      guardType: 0,
    },
  },
  render: ({ data }) => {
    const levelIncrements = 4 // How many levels to skip each row (initially 4, adjusted later)
    const totalRows = 10 // Total number of rows
    const guardOrder = [0, 3, 2, 1] // Custom order for guard types

    return (
      <div className='grid gap-2'>
        {Array.from({ length: totalRows }, (_, idx) => {
          const baseLevel = 1 + idx * levelIncrements // Adjusting level based on row index
          return (
            <div className='flex gap-2' key={idx}>
              {guardOrder.map((guardType, colIdx) => (
                <Medal
                  key={colIdx}
                  data={{
                    ...data,
                    level: baseLevel,
                    guardType: guardType,
                  }}
                />
              ))}
            </div>
          )
        })}
      </div>
    )
  },
}

export const GroupMedal: Story = {
  args: {
    data: {
      lightened: 1,
      level: 0,
      name: '巨奶绿',
      room: 25034104,
      guardType: 0,
      type: 1,
    },
    perkLevel: 2,
  },
  render: ({ data }) => {
    return (
      <div className='flex gap-2'>
        <Medal data={data} />
      </div>
    )
  },
}
