@layer template {
  .event {
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.25) calc(var(--event-font-size) * 0.5);
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--event-message-text, var(--text-color));
    background: var(--event-like-click-bg, transparent);
  }

  .meta {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--event-font-size) * 0.25);
    margin-right: calc(var(--event-font-size) * 0.5);
  }

  .message {
    color: var(--event-like-click-text, var(--pink));
    line-height: var(--event-line-height);
    vertical-align: top;
    word-break: break-word;
  }

  .username {
    font-weight: bold;
    word-break: break-word;
  }

  /* Copied from interaction.module.css */
  .guardLevel0 {
    background-color: transparent;
    --fallback-text: var(--text-color);

    .username {
      color: var(--event-username-text-0, var(--event-username-color-t0, var(--fallback-text)));
    }
  }

  .guardLevel1 {
    /* background-color: var(--orange-20); */
    --fallback-text: var(--orange-dark);

    .username {
      color: var(--event-username-text-1, var(--event-username-color-t1, var(--fallback-text)));
    }

    &.dark {
      --fallback-text: var(--orange-light);
    }
  }

  .guardLevel2 {
    /* background-color: var(--purple-20); */
    --fallback-text: var(--purple-dark);

    .username {
      color: var(--event-username-text-1, var(--event-username-color-t2, var(--fallback-text)));
    }

    &.dark {
      --fallback-text: var(--purple-light);
    }
  }

  .guardLevel3 {
    /* background-color: var(--blue-20); */
    --fallback-text: var(--blue-dark);

    .username {
      color: var(--event-username-text-1, var(--event-username-color-t3, var(--fallback-text)));
    }

    &.dark {
      --fallback-text: var(--blue-light);
    }
  }
}
