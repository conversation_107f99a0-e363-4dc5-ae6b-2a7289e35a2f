import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import useS<PERSON> from 'swr'

import useGlobalStore from '@/lib/store'

interface VersionInfo {
  hash: string
  buildTime: string
}

// Fetcher function for SWR
const fetcher = async (url: string): Promise<VersionInfo> => {
  const res = await fetch(url)
  if (!res.ok) {
    throw new Error(`Failed to fetch version: ${res.status}`)
  }
  return res.json()
}

/**
 * A hook that checks for version updates by comparing the current version with the one in version.json.
 * Uses SWR for efficient data fetching and revalidation.
 */
export default function useVersionCheck(refreshInterval = 300000) {
  const { t } = useTranslation()
  const [currentVersion, setCurrentVersion] = useState<string | null>(null)
  const [newVersionAvailable, setNewVersionAvailable] = useState(false)
  const pendingUpdate = useGlobalStore(state => state.pendingUpdate)
  const updatePendingUpdate = useGlobalStore.getState().updatePendingUpdate

  const { data, error, isLoading } = useSWR(
    // Only fetch if not paused
    pendingUpdate ? null : '/version.json',
    fetcher,
    {
      refreshInterval,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      onSuccess: data => {
        if (!currentVersion) {
          // First load - set initial version
          setCurrentVersion(data.hash)
        } else if (data.hash !== currentVersion) {
          // Version changed
          setNewVersionAvailable(true)
          toast.info(t('version.newVersionAvailable'), {
            id: 'version-check',
            description: t('version.refreshDescription'),
            duration: Infinity,
            onDismiss: () => {
              updatePendingUpdate(true)
            },
            closeButton: true,
            action: {
              label: t('version.refresh'),
              onClick: () => window.location.reload(),
            },
            position: 'top-right',
          })
        }
      },
    }
  )

  // Function to resume checking
  const resumeChecking = () => {
    updatePendingUpdate(false)
  }

  return {
    currentVersion,
    newVersionAvailable,
    isLoading,
    error,
    versionData: data,
    pendingUpdate,
    resumeChecking,
  }
}
