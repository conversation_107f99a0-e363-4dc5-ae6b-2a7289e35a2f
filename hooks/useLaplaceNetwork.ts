import useSWR, { type Fetcher } from 'swr'

import { Api } from '@/lib/const'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<{ status: string }> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching network status')
  }
  return response.json()
}

function useLaplaceNetwork() {
  const { data, error, isLoading } = useSWR(
    // Use fixed domain for status check
    `https://edge-workers.laplace.cn/network/laplace?scope=chat-frontend,chat-workers,chat-backend`,
    fetcher,
    {
      refreshInterval: 120 * 1000,
    }
  )

  return {
    laplaceNetwork: data,
    isLaplaceNetworkLoading: isLoading,
    isLaplaceNetworkError: error,
  }
}

export default useLaplaceNetwork
