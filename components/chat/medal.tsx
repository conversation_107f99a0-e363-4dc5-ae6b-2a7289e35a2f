import { useRef, useState } from 'react'
import clsx from 'clsx'
import type { BilibiliInternal, FansMedal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

import { nf } from '@/utils/numberFormat'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import type { EventModeProps } from '@/components/event'
import { Loading } from '@/components/ui/loading'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import styles from './medal.module.css'

/**
 * 粉丝勋章
 * @project laplace-chat
 * @link https://link.bilibili.com/p/help/index#/audience-fans-medal
 */
export default function Medal({
  data,
  mode,
  perkLevel,
}: {
  data: FansMedal
  mode?: EventModeProps
  perkLevel?: number
}) {
  const [anchorInfo, setAnchorInfo] = useState<BilibiliInternal.HTTPS.Prod.GetDanmuMedalAnchorInfo['data'] | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const abortRef = useRef<AbortController | null>(null)

  const options = useOptions()
  const { showMedalLightenedOnly } = options

  const handlePopoverOpenChange = (open: boolean) => {
    // Abort previous request if it exists
    if (!open && abortRef.current) {
      abortRef.current.abort()
      abortRef.current = null
      setIsLoading(false)
    }

    // Fetch new data if opening
    if (open && data.uid) {
      fetchAnchorInfo(data.uid)
    }
  }

  const fetchAnchorInfo = async (uid: number) => {
    if (!uid) return

    // Create new abort controller for this request
    abortRef.current = new AbortController()

    setIsLoading(true)
    setError(null)
    try {
      const resp = await fetch(`${Api.Workers}/bilibili/live-medal-anchor-info/${uid}`, {
        signal: abortRef.current.signal,
      })
      if (!resp.ok) {
        throw new Error('用户信息获取错误')
      }
      const data: BilibiliInternal.HTTPS.Prod.GetDanmuMedalAnchorInfo = await resp.json()
      setAnchorInfo(data.data)
    } catch (err) {
      // Don't set error if request was aborted
      if (err instanceof Error && err.name === 'AbortError') {
        return
      }
      setError(err instanceof Error ? err.message : '未知错误')
    } finally {
      if (abortRef.current) {
        setIsLoading(false)
        abortRef.current = null
      }
    }
  }

  if (data && (data.name === '' || data.name === null)) {
    return <></>
  }

  const shouldShow = !showMedalLightenedOnly || data.lightened
  if (!shouldShow) return <></>

  // https://link.bilibili.com/p/help/index#/audience-fans-medal
  const fansMedalTier = Math.ceil(data.level / 4)

  // 大航海 或 大航海套票都要显示图标
  const hasGuardIcon = (data?.guardType && data.guardType > 0) || data.type === 1

  const medalContent = (
    <div
      className={clsx(
        styles.fansMedal,
        styles[`fansMedalTier${fansMedalTier}`],
        styles[`fansMedalType${data?.type || 0}`],
        !data?.lightened && styles.fansMedalDimmed,
        'fans-medal',
        `fans-medal-lvl--${data.level}`,
        `fans-medal-tier--${fansMedalTier}`,
        `fans-medal-name--${data.name}`,
        `fans-medal-lightened--${data?.lightened !== undefined ? data.lightened : 'unknown'}`,
        `fans-medal-type--${data?.type || 0}`,
        hasGuardIcon && [
          styles.fansMedalHasGuard,
          styles[`fansMedalHasGuard${data.guardType}`],
          'fans-medal-has-guard',
          `fans-medal-has-guard--${data.guardType}`,
        ]
      )}
    >
      <div className={`${styles.fansMedalContent} fans-medal-content`}>
        {hasGuardIcon ? (
          <div
            className={clsx(
              styles.guardBadge,
              styles[`guardBadge${data.guardType}`],
              perkLevel && styles[`perkLevel${perkLevel}`],
              'guard-badge-in-fans-medal',
              'guard-badge',
              `guard-badge--${data.guardType}`,
              perkLevel && `perk-level-${perkLevel}`
            )}
          ></div>
        ) : null}
        {data.name}
      </div>

      {/* 粉丝勋章套票等级为 0，因此需要额外判断 */}
      {data?.level ? <div className={`${styles.fansMedalLevel} fans-medal-level`}>{data.level}</div> : null}
    </div>
  )

  // Only wrap in Popover if in dashboard mode
  if (mode === 'dashboard' && data.uid && data?.type !== 1) {
    return (
      <Popover onOpenChange={handlePopoverOpenChange}>
        <PopoverTrigger className='cursor-pointer' asChild>
          {medalContent}
        </PopoverTrigger>
        <PopoverContent className='w-auto p-2' side='top'>
          {isLoading ? (
            <div className='flex items-center justify-center'>
              <Loading />
            </div>
          ) : error ? (
            <div className='text-rose-500'>{error}</div>
          ) : anchorInfo ? (
            <div className='flex items-center gap-2 pr-1'>
              <a
                href={`https://live.bilibili.com/${anchorInfo.anchor_roomid}`}
                target='_blank'
                rel='noreferrer noopener'
              >
                <Avatar
                  className='[--avatar-size:2.25rem]'
                  live={anchorInfo.live_stream_status === 1}
                  uid={anchorInfo.ruid}
                  avatar={anchorInfo.rface}
                />
              </a>
              <div>
                <div>
                  <a
                    href={`https://space.bilibili.com/${anchorInfo.ruid}`}
                    className='font-semibold'
                    target='_blank'
                    rel='noreferrer noopener'
                  >
                    {anchorInfo.runame}
                  </a>
                </div>
                <div className='text-sm opacity-60'>{nf.format(anchorInfo.fans_club_count)} 粉丝团</div>
              </div>
            </div>
          ) : null}
        </PopoverContent>
      </Popover>
    )
  }

  // 部分事件，例如 interaction，会传入 medal: null，因此此处需要额外判断是否
  // 但此处不能用 data.uid 判断，因为历史遗留问题，部分醒目留言在解析时并没有提供 uid
  if (data.name) {
    return medalContent
  }

  return <></>
}
