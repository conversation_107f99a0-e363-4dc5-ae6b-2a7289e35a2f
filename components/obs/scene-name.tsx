import { IconMoonFilled, IconSunHighFilled } from '@tabler/icons-react'

import { cn } from '@/lib/cn'
import { parseSceneName } from '@/lib/obs/utils'

import { Badge, badgeVariantsConfig } from '@/components/ui/badge'

interface SceneNameProps {
  sceneName: string
  className?: string
}

type TintType = keyof typeof badgeVariantsConfig.tint

// Type guard to check if a string is a valid badge tint
function isValidTint(value: string): value is TintType {
  return Object.keys(badgeVariantsConfig.tint).includes(value)
}

export const SceneName: React.FC<SceneNameProps> = ({ sceneName, className }) => {
  const { name, colorScheme, scene } = parseSceneName(sceneName)

  // Get tint value for badge
  const tint = scene && isValidTint(scene) ? scene : 'default'

  return (
    <div className={cn('flex items-center gap-x-1', className)}>
      <div className='truncate text-sm leading-4'>{name}</div>

      {colorScheme === 'dark' && (
        <div className='flex size-3 shrink-0 items-center justify-center rounded-full bg-sky-900 text-yellow-200'>
          <IconMoonFilled className='size-2' />
        </div>
      )}
      {colorScheme === 'light' && (
        <div className='flex size-3 shrink-0 items-center justify-center rounded-full bg-amber-500 text-yellow-50'>
          <IconSunHighFilled className='size-2.5' />
        </div>
      )}

      {scene && (
        <Badge tint={tint} size='sm'>
          {scene}
        </Badge>
      )}
    </div>
  )
}
