import Image from 'next/image'
import { useTranslation } from 'react-i18next'

import { cn } from '@/lib/cn'
import { Api } from '@/lib/const'
import { useEventBridgeStore } from '@/lib/event-bridge/store'

import { Divider } from '@/components/ui/divider'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

export const EventBridgeDashboardWidget: React.FC = () => {
  const { isConnected } = useEventBridgeStore()
  const { t } = useTranslation()

  if (!isConnected) {
    return null
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className='cursor-pointer px-1'>
          <StatCard
            title={
              <div className='flex items-center gap-x-0.5'>
                LEB
                {isConnected ? (
                  <div className='size-2 animate-pulse rounded-full bg-emerald-500' />
                ) : (
                  <div className='bg-fg/40 size-2 rounded-full' />
                )}
              </div>
            }
            value={isConnected ? 'Active' : 'N/A'}
            color={isConnected ? 'green' : 'default'}
            size='sm'
          />
        </div>
      </PopoverTrigger>

      <PopoverContent className='max-w-90 overflow-hidden'>
        <div className='space-y-3'>
          {/* <div className='grid grid-cols-2 gap-x-2 gap-y-3 sm:grid-cols-3'>
            <div className='flex flex-col rounded-md bg-transparent p-2 shadow-sm dark:bg-black'>
              <span className='text-fg/60 text-xs font-light'>Status</span>
              <span className='flex items-center gap-x-1 text-sm font-medium'>
                <div className='h-2 w-2 rounded-full bg-emerald-500 shadow-sm shadow-emerald-500/50' />
                Connected
              </span>
            </div>
          </div> */}

          {/* <Divider className='-mx-3 my-3 before:w-1.5' extended label='Information' /> */}

          <p className='pr-[calc(98px-0.75rem)]'>
            {t(
              'eventBridge.dashboard.description',
              'Event Bridge is active. All events are being sent to the connected LEB server.'
            )}
          </p>
        </div>
        <picture className='absolute -right-[1px] -bottom-[1px] flex size-[98px] justify-end'>
          <Image src={`${Api.Assets}/assets/stickers/uotn/30.png`} alt='Mascot' loading='lazy' width={98} height={98} />
        </picture>
      </PopoverContent>
    </Popover>
  )
}

// Stat card component
interface StatCardProps {
  title: React.ReactNode
  value: React.ReactNode
  color: 'red' | 'yellow' | 'green' | 'default'
  size?: 'sm' | 'md'
}

const StatCard: React.FC<StatCardProps> = ({ title, value, color, size = 'md' }) => {
  const getColorClass = () => {
    switch (color) {
      case 'red':
        return 'bg-rose-500'
      case 'yellow':
        return 'bg-yellow-500'
      case 'green':
        return 'bg-emerald-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className={cn('grid items-center', size === 'sm' ? 'text-[14px] leading-4' : 'text-[18px]')}>
      <span className='text-fg/60 flex text-[11px] leading-none'>{title}</span>
      <div className='flex max-w-30 items-center gap-x-1'>
        {/* <div className={`w-1 h-4 rounded-md ${getColorClass()}`} /> */}
        <span className={'font-logo truncate font-semibold'}>{value}</span>
      </div>
    </div>
  )
}
