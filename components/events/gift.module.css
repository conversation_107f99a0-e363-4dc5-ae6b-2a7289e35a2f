@layer template {
  .event {
    --avatar-size: calc(var(--event-font-size) * 2);
    --gift-size: calc(var(--event-font-size) * 2);
    --fallback-text: var(--event-gift-text, var(--event-message-text, var(--text-color)));
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--fallback-text);
  }

  .read {
    opacity: 0.5 !important;
    filter: grayscale(0.8) !important;
  }

  .eventSizeNormal {
    --avatar-size: var(--event-line-height);
    --gift-size: var(--event-line-height);

    padding-top: calc(var(--event-font-size) * 0.125);
    padding-bottom: calc(var(--event-font-size) * 0.125);
    /* TODO: a temp `display: block` padding fix, this method causes non-even padding */
    /* padding-bottom: 0; */

    /* prettier-ignore-start */
    &.guardLevel0 {
      --username-text: var(--text-color);
    }
    &.guardLevel1 {
      --username-text: var(--orange-dark);
    }
    &.guardLevel2 {
      --username-text: var(--purple-dark);
    }
    &.guardLevel3 {
      --username-text: var(--blue-dark);
    }

    &.guardLevel1.dark {
      --username-text: var(--orange-light);
    }
    &.guardLevel2.dark {
      --username-text: var(--purple-light);
    }
    &.guardLevel3.dark {
      --username-text: var(--blue-light);
    }

    &.guardLevel0 .username {
      color: var(--event-username-text-0, var(--event-username-color-t0, var(--username-text)));
    }
    &.guardLevel1 .username {
      color: var(--event-username-text-1, var(--event-username-color-t1, var(--username-text)));
    }
    &.guardLevel2 .username {
      color: var(--event-username-text-2, var(--event-username-color-t2, var(--username-text)));
    }
    &.guardLevel3 .username {
      color: var(--event-username-text-3, var(--event-username-color-t3, var(--username-text)));
    }

    &.giftRank1 .message {
      color: var(--event-gift-normal-text-1, var(--blue-darker));
      background: var(--event-gift-normal-bg-1, transparent);
    }
    &.giftRank2 .message {
      color: var(--event-gift-normal-text-2, var(--green-darker));
      background: var(--event-gift-normal-bg-2, transparent);
    }
    &.giftRank3 .message {
      color: var(--event-gift-normal-text-3, var(--amber-darker));
      background: var(--event-gift-normal-bg-3, transparent);
    }
    &.giftRank4 .message {
      color: var(--event-gift-normal-text-4, var(--orange-darker));
      background: var(--event-gift-normal-bg-4, transparent);
    }
    &.giftRank5 .message {
      color: var(--event-gift-normal-text-5, var(--red-dark));
      background: var(--event-gift-normal-bg-5, transparent);
    }
    &.giftRank6 .message {
      color: var(--event-gift-normal-text-6, var(--red-darkest));
      background: var(--event-gift-normal-bg-6, transparent);
    }

    &.dark.giftRank1 .message {
      color: var(--event-gift-normal-text-1, var(--blue));
      background: var(--event-gift-normal-bg-1, transparent);
    }
    &.dark.giftRank2 .message {
      color: var(--event-gift-normal-text-2, var(--green));
      background: var(--event-gift-normal-bg-2, transparent);
    }
    &.dark.giftRank3 .message {
      color: var(--event-gift-normal-text-3, var(--amber));
      background: var(--event-gift-normal-bg-3, transparent);
    }
    &.dark.giftRank4 .message {
      color: var(--event-gift-normal-text-4, var(--orange));
      background: var(--event-gift-normal-bg-4, transparent);
    }
    &.dark.giftRank5 .message {
      color: var(--event-gift-normal-text-5, var(--red));
      background: var(--event-gift-normal-bg-5, transparent);
    }
    &.dark.giftRank6 .message {
      color: var(--event-gift-normal-text-6, var(--red));
      background: var(--event-gift-normal-bg-6, transparent);
    }
    /* prettier-ignore-end */

    .content {
      display: block;
    }

    .message {
      display: inline;
      margin-left: calc(var(--event-font-size) * 0.125);
      vertical-align: top;
    }

    .price {
      display: none;
    }
  }

  .eventSizeHighlight {
    --gift-size: calc(var(--event-font-size) * 3.75);

    font-size: calc(var(--event-font-size) * 1.125);
    border-radius: var(--event-border-radius);
    color: var(--event-gift-text, #fff);

    @container (max-width: 320px) {
      --gift-size: calc(var(--event-font-size) * 2);
    }

    &.giftRank1 {
      color: var(--event-gift-text-1, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-1, var(--blue-dark));
    }
    &.giftRank2 {
      color: var(--event-gift-text-2, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-2, var(--green-darker));
    }
    &.giftRank3 {
      color: var(--event-gift-text-3, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-3, var(--amber-darker));
    }
    &.giftRank4 {
      color: var(--event-gift-text-4, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-4, var(--orange-dark));
    }
    &.giftRank5 {
      color: var(--event-gift-text-5, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-5, var(--red-dark));
    }
    &.giftRank6 {
      color: var(--event-gift-text-6, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-6, var(--red-darkest));
    }

    &.dark.giftRank1 {
      color: var(--event-gift-text-1, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-1, var(--blue-40));
    }
    &.dark.giftRank2 {
      color: var(--event-gift-text-2, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-2, var(--green-40));
    }
    &.dark.giftRank3 {
      color: var(--event-gift-text-3, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-3, var(--amber-40));
    }
    &.dark.giftRank4 {
      color: var(--event-gift-text-4, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-4, var(--orange-40));
    }
    &.dark.giftRank5 {
      color: var(--event-gift-text-5, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-5, var(--red-40));
    }
    &.dark.giftRank6 {
      color: var(--event-gift-text-6, var(--event-gift-text, #fff));
      background: var(--event-gift-bg-6, var(--red-60));
    }

    .content {
      position: relative;
      display: grid;
      padding-right: calc(var(--avatar-size) * 1.25);
      gap: calc(var(--event-font-size) * 0.25);
    }

    .giftDetailsPrice {
      display: none;
    }

    .top {
      gap: 0 calc(var(--event-font-size) * 0.375);
    }

    .giftIconWrap {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .showAs_sticky {
    --avatar-size: var(--event-line-height);
    --gift-size: calc(var(--event-line-height) * 1.25);

    &.eventSizeHighlight {
      --event-border-radius: calc(var(--1px) * 100);
      font-size: var(--event-font-size);
      padding: calc(var(--event-font-size) * 0.25);
      padding-right: calc(var(--event-font-size) * 0.5);

      .giftIconWrap {
        top: calc(var(--1px) * -3);
      }
    }
  }

  .username {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    word-break: break-word;

    a {
      color: inherit;
    }
  }

  .price {
    /* 防止盲盒礼物这种价格比较长的显示不完全 */
    flex-shrink: 0;
  }

  .priceActual {
    text-decoration: line-through;
    opacity: 0.8;
  }

  .priceCurrency {
    opacity: 0.8;
    font-weight: normal;
  }

  .priceFigure {
    font-weight: bold;
  }

  .priceBlindResult {
    display: inline-flex;
    vertical-align: top;

    svg {
      height: var(--event-line-height);
    }
  }

  .message {
    line-height: var(--event-line-height);
    word-break: break-word;
  }

  .top {
    display: inline-flex;
    align-items: center;
    gap: 0 calc(var(--event-font-size) * 0.25);
    line-height: var(--event-line-height);
    font-weight: bold;
  }

  .giftDetailsSymbol {
    opacity: 0.6;
  }

  .giftDetailsFigure {
    opacity: 0.6;
  }

  .giftIconWrap {
    display: inline-block;
    /* This help avoid inline image height issues */
    max-height: var(--gift-size);
    /* 为了控制台礼物可点击触发特效，不再无法点击 */
    /* pointer-events: none; */
    flex-shrink: 0;

    img {
      width: var(--gift-size);
      height: var(--gift-size);
    }
  }

  .giftReceiver {
    --avatar-size: 1.2em;
    display: inline-flex;
    align-items: center;
  }

  .hidden {
    display: none;
  }
}
