import { useState } from 'react'
import { clsx } from 'clsx'

import { Api } from '@/lib/const'

import styles from './wealth-medal.module.css'

/**
 * 荣耀勋章
 */
export default function WealthMedal({ data }: { data: number }) {
  const [loadImageFailed, setLoadImageFailed] = useState(false)

  const imageUrl = `${Api.Workers}/bilibili/wealth-config?level=${data}`

  if (!data) {
    return <></>
  } else {
    return (
      <div
        className={clsx(
          styles.wealthMedal,
          'wealth-medal',
          `wealth-medal-lvl--${data}`,
          loadImageFailed && [styles.wealthMedalImageFailed, 'wealth-medal-image-failed']
        )}
        data-medal-lvl={data}
      >
        <span className={clsx(styles.wealthMedalText, 'wealth-medal-text')}>{data}</span>

        <picture>
          <img
            className={clsx(loadImageFailed && `wealth-medal-image-fallback`)}
            src={loadImageFailed ? `/transparent.png` : imageUrl}
            alt={`荣耀勋章`}
            referrerPolicy='no-referrer'
            onError={() => setLoadImageFailed(true)}
            loading='lazy'
          />
        </picture>
        {loadImageFailed && data}
      </div>
    )
  }
}
