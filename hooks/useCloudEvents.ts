import { useEffect, useState } from 'react'
import useSWR, { type Fetcher } from 'swr'

import type { EventFetcherResp } from '@/types'

import { Api } from '@/lib/const'

import { filterEmptyParams } from '@/utils/filterEmptyParams'

import { useOptions } from '@/hooks/useOptions'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<EventFetcherResp> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching cloud events')
  }
  return response.json()
}

// const FETCH_INTERVAL = 5000 // 5 seconds, for debugging
const FETCH_INTERVAL = 60 * 1000 * 5 // 5 minutes
const FULL_FETCH_INTERVAL = 60 * 1000 * 60 // 1 hour

function useCloudEvents(roomId: string, forceFullLoad?: boolean) {
  const sanitizedQuery = roomId.replace(/[^0-9]/g, '')
  const options = useOptions()
  const { danmakuFetcherApi, eventFetcherAuth } = options

  // Track last full fetch time
  const [lastFullFetchTime, setLastFullFetchTime] = useState<number>(0)
  const [shouldFetchFull, setShouldFetchFull] = useState(forceFullLoad ?? true)

  // Check if we should do a full fetch based on time elapsed
  useEffect(() => {
    const checkFullFetchNeeded = () => {
      const now = Date.now()
      const timeSinceLastFull = now - lastFullFetchTime

      // If forceFullLoad is explicitly set, use that
      if (forceFullLoad !== undefined) {
        setShouldFetchFull(forceFullLoad)
        return
      }

      // If we haven't done a full fetch yet, or it's been more than FULL_FETCH_INTERVAL
      if (lastFullFetchTime === 0 || timeSinceLastFull >= FULL_FETCH_INTERVAL) {
        setShouldFetchFull(true)
      } else {
        setShouldFetchFull(false)
      }
    }

    // Check immediately
    checkFullFetchNeeded()

    // Check every minute to see if we need a full fetch
    const interval = setInterval(checkFullFetchNeeded, 60 * 1000 * 1)

    return () => clearInterval(interval)
  }, [forceFullLoad, lastFullFetchTime])

  const params = {
    full: shouldFetchFull ? 1 : undefined,
    // Only apply auth key if custom server defined
    auth: danmakuFetcherApi ? eventFetcherAuth : undefined,
  }

  const urlParams = filterEmptyParams(params)

  // const defaultApiBase = `http://localhost:8080/events`
  // const defaultApiBase = `https://event-fetcher.laplace.live`
  const defaultApiBase = Api.EventFetcher

  const apiBase = danmakuFetcherApi ? `${danmakuFetcherApi}` : defaultApiBase

  const { data, error, isLoading, mutate, isValidating } = useSWR(
    // sanitizedQuery ? `https://event-fetcher.vrp.moe/events/${sanitizedQuery}` : null,
    sanitizedQuery ? `${apiBase}/events/${sanitizedQuery}?${urlParams}` : null,
    fetcher,
    {
      refreshInterval: FETCH_INTERVAL,
      onSuccess: data => {
        // Update last full fetch time when we successfully complete a full fetch
        if (shouldFetchFull && data?.status === 200) {
          setLastFullFetchTime(Date.now())
          // After a successful full fetch, switch back to incremental
          setShouldFetchFull(false)
        }
      },
    }
  )

  return {
    eventsFromCloud: data,
    isCloudEventsLoading: isLoading,
    isCloudEventsValidating: isValidating,
    isCloudEventsError: error,
    mutate,
  }
}

export default useCloudEvents
