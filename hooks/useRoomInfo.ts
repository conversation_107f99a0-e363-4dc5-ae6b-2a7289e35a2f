import useSWR, { type Fetcher } from 'swr'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<BilibiliInternal.HTTPS.Prod.GetInfoByRoom> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching the data')
  }
  return response.json()
}

function useRoomInfo(roomId: string) {
  const sanitizedQuery = roomId.replace(/[^0-9]/g, '')

  const { data, error, isLoading } = useSWR(
    sanitizedQuery ? `${Api.Workers}/bilibili/room-info/${sanitizedQuery}` : null,
    fetcher,
    {
      refreshInterval: 60 * 1000,
    }
  )

  return {
    roomInfo: data,
    isRoomInfoLoading: isLoading,
    isRoomInfoError: error,
  }
}

export default useRoomInfo
