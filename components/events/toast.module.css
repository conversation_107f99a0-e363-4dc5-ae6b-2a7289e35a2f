@layer template {
  .event {
    --avatar-size: calc(var(--1px) * 40);
    --badge-size: calc(var(--event-font-size) * 3.75);
    --fallback-text: var(--event-toast-text, #fff);
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    font-family: var(--event-font-family);
    font-size: calc(var(--event-font-size) * 1.125);
    line-height: var(--event-line-height-base);
    color: var(--fallback-text);
    border-radius: var(--event-border-radius);
    display: flex;
    gap: calc(var(--event-font-size) * 0.5);
    align-items: center;
    background-size: var(--badge-size) var(--badge-size);
    background-repeat: no-repeat;
    background-position: center right calc(var(--event-font-size) * 0.5);

    @container (max-width: 320px) {
      --badge-size: calc(var(--event-font-size) * 2);
    }
  }

  .read {
    opacity: 0.5 !important;
    filter: grayscale(0.8) !important;
  }

  .event.showAs_sticky {
    --avatar-size: var(--event-line-height);
    --event-border-radius: calc(var(--1px) * 100);

    background-position: center right calc(var(--event-font-size) * -1.5);
    padding: calc(var(--event-font-size) * 0.25);
    padding-right: calc(var(--event-font-size) * 2);
  }

  /* 总督 */
  .tier1 {
    color: var(--event-toast-text-1, var(--fallback-text));
    background-color: var(--event-toast-bg-1, var(--red));

    &.badgeRank1 {
      background-image: url(https://rsrc.laplace.cn/assets/guard-icons/icon-1-animate.png);
    }
    &.badgeRank2 {
      background-image: url(https://rsrc.laplace.cn/assets/guard-icons/icon-1-1k.png);
    }

    &.frameRank1 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-1.png);
    }

    &.frameRank2 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-1-1k.png);
    }

    &.frameRank3 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-1-10k.png);
    }
  }

  /* 提督 */
  .tier2 {
    color: var(--event-toast-text-2, var(--fallback-text));
    background-color: var(--event-toast-bg-2, var(--purple));

    &.badgeRank1 {
      background-image: url(https://rsrc.laplace.cn/assets/guard-icons/icon-2-animate.png);
    }
    &.badgeRank2 {
      background-image: url(https://rsrc.laplace.cn/assets/guard-icons/icon-2-1k.png);
    }

    &.frameRank1 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-2.png);
    }

    &.frameRank2 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-2-1k.png);
    }

    &.frameRank3 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-2-10k.png);
    }
  }

  /* 舰长 */
  .tier3 {
    color: var(--event-toast-text-3, var(--fallback-text));
    background-color: var(--event-toast-bg-3, var(--blue));

    &.badgeRank1 {
      background-image: url(https://rsrc.laplace.cn/assets/guard-icons/icon-3-animate.png);
    }

    &.badgeRank2 {
      background-image: url(https://rsrc.laplace.cn/assets/guard-icons/icon-3-1k.png);
    }

    &.frameRank1 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-3.png);
    }

    &.frameRank2 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-3-1k.png);
    }

    &.frameRank3 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-3-10k.png);
    }
  }

  .dark.tier1 {
    background-color: var(--event-toast-bg-1, var(--red-40));
  }
  .dark.tier2 {
    background-color: var(--event-toast-bg-2, var(--purple-40));
  }
  .dark.tier3 {
    background-color: var(--event-toast-bg-3, var(--blue-40));
  }

  .content {
    display: grid;
    gap: calc(var(--event-font-size) * 0.25);
  }

  .avatar {
    width: var(--avatar-size);
    height: var(--avatar-size);
    border-radius: 50%;
  }

  .username {
    font-weight: bold;
    width: fit-content;
    word-break: break-word;
  }

  .message {
    padding-right: calc(var(--badge-size) + calc(var(--1px) * 4));
  }

  .price {
    font-weight: bold;
  }

  .priceCurrency {
    opacity: 0.8;
    font-weight: normal;
  }

  .priceFigure {
    font-weight: bold;
  }

  /* 大航海盲盒 */
  .specialBlindBox {
    opacity: 0.6;
  }

  .hidden {
    display: none;
  }
}
