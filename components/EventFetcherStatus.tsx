import React from 'react'
import { IconCloudCode, IconCloudQuestion, IconCloudStar, IconCloudX } from '@tabler/icons-react'

import type { EventFetcherResp } from '@/types'

import { EventFetcherInput } from '@/components/EventFetcherInput'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import { Alert } from './ui/alert'

/**
 * @deprecated 请使用 EventFetcherInput 组件
 */
const EventFetcherStatus = ({
  room,
  danmakuFetcherApi,
  eventsFromCloud,
  builtInEventFetcherWhitelist,
}: {
  room: number
  danmakuFetcherApi: string
  eventsFromCloud: EventFetcherResp | undefined
  builtInEventFetcherWhitelist: number[]
}) => {
  const getCloudStatus = () => {
    if (danmakuFetcherApi) {
      if (eventsFromCloud?.status === 200) {
        return {
          icon: IconCloudCode,
          color: 'text-emerald-500',
          message: <Alert tint={'success'}>当前直播间已开启云端同步，关闭本控制台依然可以正常统计礼物</Alert>,
        }
      }
      return {
        icon: IconCloudQuestion,
        color: 'text-fg',
        message: (
          <Alert tint={'warning'}>
            当前自定义 API 未包含本直播间的云端事件，控制台关闭时的礼物将无法统计，请保持本页面开启
          </Alert>
        ),
      }
    }

    if (builtInEventFetcherWhitelist.includes(room)) {
      return {
        icon: IconCloudStar,
        color: 'text-purple-500',
        message: (
          <Alert tint={'info'}>当前直播间为本站 VIP，已开启内置云端同步，关闭本控制台依然可以正常统计礼物</Alert>
        ),
      }
    }

    return {
      icon: IconCloudX,
      color: 'text-fg',
      message: (
        <>
          <Alert tint={'warning'}>当前直播间未开启云端同步，控制台关闭时的礼物将无法统计，请保持本页面开启</Alert>
          <div>
            访问{' '}
            <a href='https://subspace.institute/docs/laplace-chat/event-fetcher' target='_blank'>
              亚空间研究所
            </a>{' '}
            了解如何通过 laplace-event-fetcher 自行搭建云端事件
          </div>
        </>
      ),
    }
  }

  const status = getCloudStatus()
  const Icon = status.icon

  return (
    <Popover>
      <PopoverTrigger className='focus-ring flex rounded-md p-1'>
        <Icon size='1.25rem' className={status.color} />
      </PopoverTrigger>
      <PopoverContent className='w-[320px] max-w-[calc(100dvw-10px)]'>
        <div className='mb-4 space-y-2'>{status.message}</div>
        <EventFetcherInput
          room={room}
          eventsFromCloud={eventsFromCloud}
          builtInEventFetcherWhitelist={builtInEventFetcherWhitelist}
        />
      </PopoverContent>
    </Popover>
  )
}

export { EventFetcherStatus }
