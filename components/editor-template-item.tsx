import type { BuiltInTemplateItemProps, TemplateMetadata } from '@/types'

import { templateFeaturesMap } from '@/utils/map'
import { timeFromNow } from '@/utils/timeFromNow'

import { Badge } from '@/components/ui/badge'
import { Tooltip } from '@/components/ui/tooltip'

function TemplateItem({
  item,
  withDetails = false,
  url,
}: {
  item: BuiltInTemplateItemProps | TemplateMetadata | null | undefined
  url?: string
  withDetails?: boolean
}) {
  return (
    <div data-slot='template-item' className='min-w-0 flex-auto'>
      <div className='flex flex-col items-start gap-2 @sm:flex-row'>
        {/* <Avatar src={image} /> */}
        {item?.thumbnail && (
          <picture className='flex shrink-0 overflow-hidden rounded-sm'>
            <img
              className={`${withDetails ? 'h-[96px] w-[120px]' : 'h-[50px] w-[62.5px]'} object-cover object-[center_left]`}
              alt={'预览图'}
              src={item.thumbnail}
              loading='lazy'
            />
          </picture>
        )}

        <div className={'min-w-0 flex-auto space-y-0.5'}>
          <div className='truncate font-medium'>{item?.title || '未命名模版'}</div>
          <div className='flex flex-wrap items-center gap-x-2 text-sm'>
            {item?.updated && (
              <div>
                <span className='opacity-60'>更新</span> {timeFromNow(+new Date(item.updated))}
              </div>
            )}
            {item?.version && (
              <div>
                <span className='opacity-60'>版本</span> {item.version}
              </div>
            )}
            <div>
              <span className='opacity-60'>作者</span> {item?.author || '未知'}
            </div>
          </div>
          {withDetails && item?.features && Array.isArray(item.features) && item.features.length > 0 && (
            <div className='my-1.5 flex flex-wrap gap-1'>
              {item.features.map((feature, idx) => {
                const data = templateFeaturesMap[feature]!

                return (
                  <Tooltip label={data.desc} key={idx}>
                    <div>
                      <Badge size='sm' variant={data.badgeType} tint={data.badgeColor}>
                        {data.name}
                      </Badge>
                    </div>
                  </Tooltip>
                )
              })}
            </div>
          )}
          {withDetails && item?.description && <div className='text-sm opacity-60'>{item.description}</div>}

          {url && <div className='text-fg/60 truncate text-xs'>{url}</div>}
        </div>
      </div>
    </div>
  )
}

export { TemplateItem }
