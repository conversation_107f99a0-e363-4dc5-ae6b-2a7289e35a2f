import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useOBSStore } from '@/lib/obs/store'

import { Alert } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'

export const OBSControls: React.FC = () => {
  const { isConnected, streamStatus, recordStatus, startStreaming, stopStreaming, startRecording, stopRecording } =
    useOBSStore()

  const { t } = useTranslation()

  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  // Handle start streaming with error handling
  const handleStartStreaming = async () => {
    try {
      setLoading(true)
      setError(null)
      await startStreaming()
    } catch (error) {
      console.error('Failed to start streaming:', error)
      setError('Failed to start streaming: ' + (error || 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  // Handle stop streaming with error handling
  const handleStopStreaming = async () => {
    try {
      setLoading(true)
      setError(null)
      await stopStreaming()
    } catch (error) {
      console.error('Failed to stop streaming:', error)
      setError('Failed to stop streaming: ' + (error || 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  // Handle start recording with error handling
  const handleStartRecording = async () => {
    try {
      setLoading(true)
      setError(null)
      await startRecording()
    } catch (error) {
      console.error('Failed to start recording:', error)
      setError('Failed to start recording: ' + (error || 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  // Handle stop recording with error handling
  const handleStopRecording = async () => {
    try {
      setLoading(true)
      setError(null)
      await stopRecording()
    } catch (error) {
      console.error('Failed to stop recording:', error)
      setError('Failed to stop recording: ' + (error || 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  if (!isConnected) {
    return null
  }

  return (
    <div>
      {error && <Alert className='mb-4'>{error}</Alert>}

      <div className='flex items-center gap-2'>
        {streamStatus?.outputActive ? (
          <Button variant='default' disabled={loading} onClick={handleStopStreaming}>
            <span className='inline-block h-3 w-3 animate-pulse rounded-full bg-rose-500'></span>
            {t('obs.stopStreaming')}
          </Button>
        ) : (
          <Button variant='default' disabled={loading} onClick={handleStartStreaming}>
            <span className='bg-fg/40 inline-block h-3 w-3 rounded-full'></span>
            {t('obs.startStreaming')}
          </Button>
        )}
        {/* {recordStatus?.outputActive ? (
          <Button variant='default' disabled={loading} onClick={handleStopRecording}>
            <span className='inline-block w-3 h-3 rounded-full bg-rose-500 animate-pulse'></span>
            Stop Recording
          </Button>
        ) : (
          <Button variant='default' disabled={loading} onClick={handleStartRecording}>
            <span className='inline-block w-3 h-3 rounded-full bg-gray-400'></span>
            Start Recording
          </Button>
        )} */}
      </div>
    </div>
  )
}
