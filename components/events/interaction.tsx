import React from 'react'
import clsx from 'clsx'

import type { Interaction } from '@/lib/event-parsers/types/interaction'
import useGlobalStore from '@/lib/store'

import { getPerkLevel } from '@/utils/eventAssetsUtils'
import { interactionCopywriting } from '@/utils/eventCopywriting'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import Medal from '@/components/chat/medal'
import WealthMedal from '@/components/chat/wealth-medal'
import type { EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'
import UserDropdown from '@/components/UserDropdown'

import styles from './interaction.module.css'

export interface InteractionProps extends React.HTMLAttributes<HTMLDivElement> {
  data: Interaction
  mode: EventModeProps
}

export default function InteractionItem({ data, mode, ...rest }: InteractionProps) {
  const eventType = interactionCopywriting(data.action)
  const dashboardLiveGuards = useGlobalStore(state => state.dashboardLiveGuards)

  const options = useOptions()
  const { showMedal, showWealthMedal, useCst } = options

  return (
    <div
      className={clsx(
        styles.event,
        styles[`guardLevel${data.guardType}`],
        styles[`action_${eventType.action}`],
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        `guard-level--${data.guardType}`,
        `event-action--${eventType.action}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <TimeDisplay date={data.timestampNormalized} localTime={!useCst} />
      <div className={`${styles.meta} meta`}>
        <Avatar
          avatar={data.avatar}
          uid={data.uid}
          guard={data.guardType}
          perkLevel={data?.medal?.resolvedPerkLevel || getPerkLevel(dashboardLiveGuards, { uid: data.medal.uid })}
        />

        {/* Wealth medal */}
        {showWealthMedal && !!data?.wealthMedalLevel && <WealthMedal data={data.wealthMedalLevel} />}

        {/* Fans medal badge */}
        {(showMedal || mode === 'dashboard') && (
          <Medal
            data={data.medal}
            mode={mode}
            perkLevel={
              data?.medal?.resolvedPerkLevel ||
              getPerkLevel(dashboardLiveGuards, {
                uid: data.medal.uid,
              })
            }
          />
        )}

        <div className={`${styles.username} username`}>
          {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}
          {mode === 'dashboard' && (
            <UserDropdown event={data}>
              <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
            </UserDropdown>
          )}
        </div>
      </div>
      {/* Content text */}
      <span className={`${styles.message} message`}>{`${eventType.name}直播间`}</span>{' '}
      {/* Relation icon and text in dashboard mode */}
      {mode === 'dashboard' && data?.relation?.type && data?.relation?.type > 0 && data?.relation?.icon ? (
        <span
          className={clsx(styles.relationWrap, 'relation-wrap', `relation-type--${data.relation.type}`)}
          title={data.relation.text}
        >
          <picture className={clsx(styles.relationIconWrap, 'relation-icon-wrap')}>
            <img
              className={clsx(styles.relationIcon, 'relation-icon')}
              src={data.relation.icon}
              alt={data.relation.text}
              referrerPolicy='no-referrer'
              loading='lazy'
            />
          </picture>
        </span>
      ) : null}
    </div>
  )
}
