'use client'

import { useState } from 'react'

import type { BuiltInTemplateItemProps } from '@/types'

import { useLocalStorage } from '@/hooks/useLocalStorage'

import { TemplateItem } from '@/components/editor-template-item'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown'
import { Label } from '@/components/ui/label'

interface TemplateSelectProps {
  placeholder?: string
  data: BuiltInTemplateItemProps[]
  onChange: (value: string) => void
}

type GroupedTemplate = [string, BuiltInTemplateItemProps[]]

const BuiltinTemplateSelect = ({
  placeholder = '在此选择一个内置模版…',
  data: availableTemplates,
  onChange,
}: TemplateSelectProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const [localStorageOptions] = useLocalStorage()
  const { cloudTheme } = localStorageOptions

  const handleSelect = (value: string) => {
    setIsOpen(false)
    onChange(value)
  }

  const selectedItem = availableTemplates.find(template => template.value === cloudTheme)

  // Get unique groups while preserving order
  const groupOrder = Array.from(new Set(availableTemplates.map(template => template.group || '')))

  // Group templates while maintaining original order
  const groups: GroupedTemplate[] = groupOrder.map(group => [
    group,
    availableTemplates.filter(template => (template.group || '') === group),
  ])

  return (
    <div className='group/template-select-wrap relative space-y-2'>
      <Label htmlFor='template-select' onClick={() => setIsOpen(true)}>
        内置模版
      </Label>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger id='template-select' asChild>
          <div
            tabIndex={0}
            className='focus-ring data-[state=open]:border-ac mt-1 flex cursor-pointer flex-wrap gap-1 rounded-md border p-2 text-lg'
          >
            {selectedItem ? (
              <div className='flex w-full min-w-0 items-center gap-2 rounded-sm'>
                <TemplateItem item={selectedItem} />
              </div>
            ) : (
              <div className='flex-auto space-y-0.5'>
                <div className='text-fg/40'>内置模版</div>
                <div className='text-fg/40 text-sm'>{placeholder}</div>
              </div>
            )}
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className='group/template-select-content @container max-h-[min(var(--radix-dropdown-menu-content-available-height),450px)] w-[var(--radix-popper-anchor-width)] overflow-y-auto'>
          <DropdownMenuItem className='w-full' onSelect={() => handleSelect('empty')}>
            不使用内置模版
          </DropdownMenuItem>
          {groups.map(([group, templates]) => (
            <DropdownMenuGroup key={group || 'ungrouped'}>
              {group && (
                <>
                  <DropdownMenuLabel>{group}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                </>
              )}
              {templates.map(template => (
                <DropdownMenuItem key={template.value} className='w-full' onSelect={() => handleSelect(template.value)}>
                  <TemplateItem item={template} withDetails />
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export { BuiltinTemplateSelect }
