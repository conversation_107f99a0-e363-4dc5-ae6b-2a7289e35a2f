import useSWR, { type Fetcher } from 'swr'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<BilibiliInternal.HTTPS.OpenPlatform.Heartbeat> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching the data')
  }
  return response.json()
}

/**
 * 用于开放平台保活，暂未使用，请使用 useBilibiliOpenPlatformHeartbeats 批量版本
 * @param gameId
 * @returns
 */
function useBilibiliOpenPlatformHeartbeat(gameId: string) {
  const { data, error, isLoading } = useSWR(
    gameId ? `${Api.Heartbeater}/bilibili-open/heartbeat/${gameId}` : null,
    fetcher,
    {
      refreshInterval: 5 * 1000, // 5 seconds
      refreshWhenHidden: true,
      revalidateIfStale: true,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
    }
  )

  return {
    heartbeat: data,
    isHeartbeatLoading: isLoading,
    isHeartbeatError: error,
  }
}

export default useBilibiliOpenPlatformHeartbeat
