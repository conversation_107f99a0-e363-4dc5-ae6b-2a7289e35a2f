import React, { useState } from 'react'
import clsx from 'clsx'

import { Api } from '@/lib/const'
import type { Gift } from '@/lib/event-parsers/types/gift'
import { useGiftEffectStore } from '@/lib/gift-effects/store'
import useGlobalStore from '@/lib/store'

import { getPerkLevel } from '@/utils/eventAssetsUtils'
import { getGiftRank } from '@/utils/getGiftRank'
import { nf } from '@/utils/numberFormat'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import Medal from '@/components/chat/medal'
import WealthMedal from '@/components/chat/wealth-medal'
import type { AsComponentProps, EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'
import { IconDown, IconUp } from '@/components/ui/icons'
import UserDropdown from '@/components/UserDropdown'

import styles from './gift.module.css'

export interface GiftProps extends React.HTMLAttributes<HTMLDivElement> {
  data: Gift
  mode: EventModeProps
  as: AsComponentProps
}

/**
 * 礼物事件
 */
export default function GiftItem({ data, mode, as, ...rest }: GiftProps) {
  const [loadIconFailed, setLoadIconFailed] = useState(false)

  const options = useOptions()
  const dashboardLiveGuards = useGlobalStore(state => state.dashboardLiveGuards)
  const addGiftEffectQueue = useGiftEffectStore(state => state.addGiftEffectQueue)

  const { colorScheme, showMedal, showGiftHighlightAbove, baseFontSize, showWealthMedal, useCst } = options

  const isBlindGift = data?.blindGift !== null
  const priceOrBlindPrice = data?.blindGift
    ? (data.blindGift.gift_tip_price * data.giftAmount) / 1000
    : data.priceNormalized
  const avatarSize = 320 * (baseFontSize / 20)
  const avatarUrl = `${data.avatar}@${avatarSize}w_${avatarSize}h`
  const isHighlighted = priceOrBlindPrice > showGiftHighlightAbove
  const isPaidiGift = data.coinType === 'gold'
  const imageUrl =
    data?.giftIcon ||
    `${Api.Workers}/bilibili/room-gift-config/${data.origin}?gift=${data.giftId}&size=${isHighlighted ? avatarSize * 3 : avatarSize}`
  // const imageUrlBg = `https://imageprx.openbayes.net/cdn-cgi/image/format=auto/${imageUrl}`
  const priceDisplay = isPaidiGift ? (
    <>
      {isBlindGift && priceOrBlindPrice !== data.priceNormalized && (
        <span className={`${styles.priceActual} price-actual`}>
          <span className={`${styles.priceCurrency} price-currency`}>¥</span>
          <span className={`${styles.priceFigure} price-figure`}>{nf.format(data.priceNormalized)}</span>
        </span>
      )}

      {isBlindGift && priceOrBlindPrice > data.priceNormalized ? (
        <span className={`${styles.priceBlindResult} price-blind-result price-blind-result-up`}>
          <IconUp className='size-4' />
        </span>
      ) : null}

      {isBlindGift && priceOrBlindPrice < data.priceNormalized ? (
        <span className={`${styles.priceBlindResult} price-blind-result price-blind-result-down`}>
          <IconDown className='size-4' />
        </span>
      ) : null}

      <span className={`${styles.priceCurrency} price-currency`}>{as === 'sticky' ? 'CN¥' : '¥'}</span>
      <span className={`${styles.priceFigure} price-figure`}>{nf.format(priceOrBlindPrice)}</span>
    </>
  ) : (
    `${nf.format(priceOrBlindPrice)} 银瓜子`
  )

  const giftRank = getGiftRank(priceOrBlindPrice)

  return (
    <div
      className={clsx(
        styles.event,
        styles[`guardLevel${data.guardType}`],
        (isHighlighted && isPaidiGift) || as === 'sticky' ? styles.eventSizeHighlight : styles.eventSizeNormal,
        styles[`giftRank${giftRank}`],
        styles[`showAs_${as}`],
        colorScheme === 'dark' && [styles.dark, 'dark'],
        'event',
        `event-size--${(isHighlighted && isPaidiGift) || as === 'sticky' ? `highlight` : `normal`}`,
        `event--${data.type}`,
        `event-type--${data.type}`,
        `event-show-as--${as}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        `guard-level--${data.guardType}`,
        `event-gift-id--${data.giftId}`,
        // `event-gift-type--${data.giftType}`,
        `event-gift-type--${isPaidiGift ? 'paid' : 'free'}`,
        `event-gift-rank--${giftRank}`,
        `event-price-rank--${giftRank}`,
        data.read && [styles.read, 'read']
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      data-gift-price={data.priceNormalized}
      data-gift-id={data.giftId}
      data-gift-type={data.giftType}
      data-gift-amount={data.giftAmount}
      data-gift-rank={giftRank}
      data-read={data.read}
      {...rest}
    >
      <div className={`${styles.content} content`}>
        {as !== 'sticky' && <TimeDisplay date={data.timestampNormalized} localTime={!useCst} />}

        <div className={`${styles.top} top`}>
          <Avatar
            uid={data.uid}
            avatar={avatarUrl}
            guard={data.guardType}
            perkLevel={getPerkLevel(dashboardLiveGuards, {
              roomId: data.origin,
            })}
            className='sender-avatar avatar--sender'
          />

          <Avatar
            uid={data.uid}
            avatar={avatarUrl}
            guard={data.guardType}
            perkLevel={getPerkLevel(dashboardLiveGuards, {
              roomId: data.origin,
            })}
            className={`${styles.hidden} avatar--sender avatar-alt-top`}
          />

          {as !== 'sticky' && (
            <>
              {/* Wealth medal */}
              {showWealthMedal && !!data?.wealthMedalLevel && <WealthMedal data={data.wealthMedalLevel} />}

              {/* Fans medal badge */}
              {(showMedal || mode === 'dashboard') && (
                <Medal
                  data={data.medal}
                  mode={mode}
                  perkLevel={
                    data?.medal?.resolvedPerkLevel ||
                    getPerkLevel(dashboardLiveGuards, {
                      uid: data.medal.uid,
                    })
                  }
                />
              )}

              <div className={`${styles.username} username`}>
                {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}

                {mode === 'dashboard' && (
                  <UserDropdown event={data}>
                    <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
                  </UserDropdown>
                )}
              </div>
            </>
          )}

          <div className={`${styles.price} price`}>{priceDisplay}</div>

          {/* Main gift image, always show */}
          {/* `gift-image-wrap` 为老版本 class，现在已经迁移到 `gift-icon-wrap`，之后可以删除掉 */}
          <span
            className={`${styles.giftIconWrap} gift-image-wrap gift-icon-wrap ${mode === 'dashboard' ? 'cursor-pointer' : ''}`}
            onClick={
              mode === 'dashboard'
                ? () => {
                    if (mode === 'dashboard') {
                      addGiftEffectQueue({
                        giftId: data.giftId,
                        amount: 1,
                        manual: true,
                      })
                    }
                  }
                : undefined
            }
          >
            <picture className={`inline-flex`}>
              <img
                className={clsx(loadIconFailed && 'gift-icon-fallback')}
                src={loadIconFailed ? '/gift-fallback.png' : imageUrl}
                alt={'礼物'}
                referrerPolicy='no-referrer'
                onError={() => setLoadIconFailed(true)}
                loading='lazy'
              />
            </picture>
          </span>

          {/* Alt gift image on top, hidden by default */}
          <span
            className={`${styles.giftIconWrap} ${styles.hidden} gift-image-wrap gift-icon-wrap gift-icon-wrap-alt-top`}
          >
            <picture className={`inline-flex`}>
              <img
                className={clsx(loadIconFailed && 'gift-icon-fallback')}
                src={loadIconFailed ? '/gift-fallback.png' : imageUrl}
                alt={'礼物'}
                referrerPolicy='no-referrer'
                onError={() => setLoadIconFailed(true)}
                loading='lazy'
              />
            </picture>
          </span>
        </div>

        <div className={`${styles.hidden} price-alt`}>{priceDisplay}</div>

        {as !== 'sticky' && (
          <div className={`${styles.message} message`}>
            {/* it returns `投喂` which is redundant */}
            {/* {data.giftAction} */}
            {data?.blindGift && isHighlighted ? (
              <span className='gift-blind'>
                <span className='gift-blind-name'>{data.blindGift.original_gift_name}</span>
                <span className='gift-blind-action'>{data.blindGift.gift_action}</span>
              </span>
            ) : null}
            <b className='gift-details-name'>{data.message}</b>
            <span className='gift-details'>
              {data.giftAmount > 1 ? (
                <>
                  <span className={`${styles.giftDetailsSymbol} gift-details-symbol`}>×</span>
                  <span className={`${styles.giftDetailsFigure} gift-details-figure`}>
                    {nf.format(data.giftAmount)}
                  </span>
                </>
              ) : null}{' '}
              <span className={`${styles.giftDetailsPrice} gift-details-price`}>{priceDisplay}</span>
            </span>

            {/* 语音频道/活动定向投喂 */}
            {(data.receiver && data.bizSource === 'voice_chat_room') ||
            (data.receiver?.master?.room_id && data.origin !== data.receiver.master.room_id) ? (
              <span className={clsx(styles.giftReceiver, 'gift-receiver')}>
                <span className='gift-receiver-action'>送给</span>
                <Avatar
                  uid={data.receiver.uid}
                  className='receiver-avatar avatar--receiver'
                  perkLevel={getPerkLevel(dashboardLiveGuards, {
                    roomId: data.origin,
                  })}
                />

                {mode === 'obs' && <b className='gift-receiver-username'>{data.receiver.uname}</b>}

                {mode === 'dashboard' && (
                  <UserDropdown
                    event={{
                      ...data,
                      uid: data.receiver.uid || 0,
                      username: data.receiver.uname,
                      wealthMedalLevel: 0,
                      // 将头像置空，让 dropdown 中的组件强制通过 workers api 去拿被回复用户的头像
                      avatar: '',
                    }}
                  >
                    <b className='gift-receiver-username cursor-pointer'>{data.receiver.uname}</b>
                  </UserDropdown>
                )}
              </span>
            ) : null}

            {/* Alt avatar image on bottom, hidden by default */}
            <Avatar
              uid={data.uid}
              avatar={avatarUrl}
              guard={data.guardType}
              perkLevel={getPerkLevel(dashboardLiveGuards, {
                roomId: data.origin,
              })}
              className={`${styles.hidden} avatar--sender avatar-alt-bottom`}
            />

            {/* Alt gift image on bottom, hidden by default */}
            <span
              className={`${styles.giftIconWrap} ${styles.hidden} gift-image-wrap gift-icon-wrap gift-icon-wrap-alt-bottom`}
            >
              <picture className={`inline-flex`}>
                <img
                  className={clsx(loadIconFailed && 'gift-icon-fallback')}
                  src={loadIconFailed ? '/gift-fallback.png' : imageUrl}
                  alt={'礼物'}
                  referrerPolicy='no-referrer'
                  onError={() => setLoadIconFailed(true)}
                  loading='lazy'
                />
              </picture>
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
