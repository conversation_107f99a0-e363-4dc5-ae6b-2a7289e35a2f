import { useMemo } from 'react'
import { useRouter } from 'next/router'

import {
  localStorageOptions,
  useLocalStorage,
  type LocalStorageOptionsKeys,
  type LocalStorageOptionsProps,
} from '@/hooks/useLocalStorage'
import useObs from '@/hooks/useObs'

// https://github.com/sindresorhus/query-string/blob/c5c2efc5addf0d05a6269da4ef2cb365dd1b9e2c/base.js#L303
function parseValue(value: any) {
  if (!Number.isNaN(Number(value)) && typeof value === 'string' && value.trim() !== '') {
    value = Number(value)
  } else if (value !== null && (value.toLowerCase() === 'true' || value.toLowerCase() === 'false')) {
    value = value.toLowerCase() === 'true'
  }

  return value
}

function isValidKey(key: string): key is LocalStorageOptionsKeys {
  return Object.keys(localStorageOptions).includes(key)
}

export const useOptions = () => {
  const router = useRouter()
  const [localStorageOptions] = useLocalStorage()
  const { scene } = useObs()

  return useMemo(() => {
    const allOptions: Partial<Record<LocalStorageOptionsKeys, any>> = {}

    // First: check if valid query key exists in query params
    for (const key in router.query) {
      if (isValidKey(key)) {
        allOptions[key] = parseValue(router.query[key])
      }
    }

    // Second: get options absent from the query params
    for (const key in localStorageOptions) {
      if (isValidKey(key) && !(key in allOptions)) {
        allOptions[key] = localStorageOptions[key]
      }
    }

    // Last: get options from OBS
    if (scene) {
      const themeMatch = scene.match(/\[scene:(.*?)\]/)

      // Get color scheme
      if (scene.includes('[dark]')) {
        allOptions.colorScheme = 'dark'
      }

      if (scene.includes('[light]')) {
        allOptions.colorScheme = 'light'
      }

      // Get custom scene name
      let sceneName: string | null = null
      if (themeMatch && themeMatch[1]) {
        sceneName = themeMatch[1].trim()
        allOptions.sceneName = sceneName
      }
    }

    return allOptions as LocalStorageOptionsProps
  }, [router.query, localStorageOptions, scene])
}
