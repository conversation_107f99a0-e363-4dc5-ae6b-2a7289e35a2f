@layer template {
  .event {
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.25) calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--event-live-start-text, var(--color-red-500));
    background-color: var(--event-live-start-bg, color-mix(in oklch, var(--color-red-500) 20%, transparent));
    border-radius: var(--event-border-radius);

    &::before {
      content: '🔴';
      margin-right: 0.25em;
    }
  }
}
