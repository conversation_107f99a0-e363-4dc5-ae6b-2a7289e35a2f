@layer components {
  .translation {
    margin-left: calc(var(--1px) * 4);
  }

  .iconWrap {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: calc(var(--event-line-height) * 0.75);
    height: calc(var(--event-line-height) * 0.75);
    /* color: var(--color-bg); */
    background-color: color-mix(in oklch, var(--color-ac) 40%, transparent);
    /* border-radius: calc(var(--1px) * 4); */
    border-radius: 50%;
    pointer-events: none;

    .icon {
      width: calc(var(--event-line-height) * 0.5);
      height: calc(var(--event-line-height) * 0.5);
    }
  }

  .loading {
    opacity: 0.5;
  }

  .text {
    opacity: 0.8;
  }
}
