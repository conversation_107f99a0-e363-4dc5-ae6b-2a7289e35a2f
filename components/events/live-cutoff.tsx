import React from 'react'
import clsx from 'clsx'

import type { Live<PERSON>utoff } from '@/lib/event-parsers/types/live-cutoff'

import type { EventModeProps } from '@/components/event'

import styles from './live-cutoff.module.css'

export interface LiveCutoffProps extends React.HTMLAttributes<HTMLDivElement> {
  data: LiveCutoff
  mode: EventModeProps
}

export default function LiveCutoffItem({ data, mode, ...rest }: LiveCutoffProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <span className='message'>
        直播 {data.uid} 被切断：{data.message}
      </span>
    </div>
  )
}
