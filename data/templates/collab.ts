const TemplateCollab = /* css */ `/* 联动气泡样式 by LAPLACE Chat */

/* 引入 Google Fonts 字体，理论上国内可以直接访问 */
@import url('https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,400;0,600;1,400;1,600&display=swap');

@layer template {
  /* 全局背景透明，可保证 OBS 载入弹幕机的过程中背景全程透明 */
  body {
    background-color: rgba(0, 0, 0, 0);
  }

  /* 全局事件变量 */
  .event {
    /* 应用全局自定义字体 */
    /* --font-sans 为 LAPLACE Chat 内置变量，可调用非衬线字体 */
    --event-font-family: 'Jost', var(--font-sans);

    /* 修改房管图标颜色 */
    --event-danmaku-mod-text: rgb(0, 53, 102);

    /* 修改主播图标颜色 */
    --event-danmaku-streamer-text: rgb(255, 248, 168);
  }

  /* 弹幕自定义样式 */
  .event--message {
    --text: #213d2b;
    --bg: #f1fbf5;

    /* 由于使用了「独立头像布局」，用户名将被包含在聊天气泡中，因此只需要让用户名继承下方弹幕内容的颜色就可以了 */
    --username-text: inherit;
  }

  /* 深色模式下也让用户名强制继承弹幕颜色 */
  .event--message.dark {
    --username-text: inherit;
  }

  /* 总督变量 */
  .event--message.guard-level--1 {
    --text: rgb(61, 24, 23);
    --bg: rgb(253, 213, 186);
  }

  /* 提督变量 */
  .event--message.guard-level--2 {
    --text: rgb(74, 29, 95);
    --bg: rgb(233, 204, 240);
  }

  /* 舰长变量 */
  .event--message.guard-level--3 {
    --text: rgb(26, 24, 85);
    --bg: rgb(200, 221, 252);
  }

  /* 主播变量 */
  .event--message.user-type--streamer {
    --text: rgb(255, 255, 255);
    --bg: rgb(65, 159, 120);
  }

  /* 弹幕事件改为 flex，方便后续修改布局 */
  .event--message {
    display: flex;
    gap: .5em;
  }

  .event--message {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-areas: "avatar meta" "avatar content";
    gap: 0.2em 0.5em;

    /* 隐藏默认头像 */
    .avatar--sender {
      display: none;
    }

    /* 使用外部影子头像 */
    .avatar-alt-outer {
      display: block;

      /* 将头像至底显示，以对应聊天气泡尾巴的位置 */
      align-self: end;
    }

    .meta {
      grid-area: meta;
    }

    /* 弹幕正文改为聊天泡泡样式 */
    .message {
      grid-area: content;
      background-color: var(--bg);
      padding: calc(var(--1px) * 8) calc(var(--1px) * 14);
      width: fit-content;
      min-width: 3em;
      border-radius: calc(var(--1px) * 18);
      filter: drop-shadow(0 0 1px currentColor);
      font-weight: bold;

      /* 为聊天泡泡增加尾巴 */
      &::before {
        content: "";
        display: inline-block;
        position: absolute;
        bottom: calc(var(--1px) * -5);
        left: calc(var(--1px) * -12);
        border: calc(var(--1px) * 10) solid transparent;
        border-right: calc(var(--1px) * 18) solid var(--bg);
        transform: rotate(-40deg);
      }
    }

    .avatar--sender {
      grid-area: avatar;
    }
  }

  /* 放大表情尺寸 */
  .event--message .emote {
    max-height: calc(var(--event-line-height) * 2);
  }

  /* 为自定义大表情固定高度，防止 OBS 模式滚动时显示不全 */
  .event--message .emote.emote--large {
    min-height: calc(var(--event-line-height) * 2);
  }

  /* 一个简易的渐变淡出效果，可以让组件边缘看起来没那么突兀 */
  .event-list {
    --offset: 30px;
    -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 1) var(--offset), rgba(0, 0, 0, 1) calc(100% - var(--offset)), rgba(0, 0, 0, 0));
    mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 1) var(--offset), rgba(0, 0, 0, 1) calc(100% - var(--offset)), rgba(0, 0, 0, 0));
  }

  /* 以下为联动样式 */
  .origin-index--0.event--message {
    /* 将 index 0 的弹幕事件推向右侧，模拟聊天软件中本地用户（本人）发送的消息，用来代表当前主播直播间的弹幕 */
    grid-template-columns: 1fr auto;
    grid-template-areas: "meta avatar" "content avatar";

    /* 调整 index 0 用户名位置 */
    .meta {
      justify-self: right;
      margin-right: 0;
    }

    .message {
      justify-self: right;
      /* 调整 index 0 的弹幕内容对齐方式，个人觉得看起来有点怪，因此默认禁用 */
      /* text-align: right; */

      /* 调整 index 0 聊天气泡尾巴的对应位置 */
      &::before {
        left: auto;
        right: calc(var(--1px) * -12);
        transform: rotate(-140deg);
      }
    }
  }
}

`

export default TemplateCollab
