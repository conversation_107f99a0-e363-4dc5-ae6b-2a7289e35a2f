import { useState } from 'react'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import { IconRefresh } from '@tabler/icons-react'

import type { EventFetcherResp } from '@/types'

import { nf } from '@/utils/numberFormat'

import useCloudEventsDryrun from '@/hooks/useCloudEventsDryrun'
import { useDebouncedValue } from '@/hooks/useDebouncedValue'
import { useLocalStorage } from '@/hooks/useLocalStorage'

import OptionLabel from '@/components/OptionLabel'
import { Alert } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { InputWithLabel } from '@/components/ui/input-with-label'
import { Loading } from '@/components/ui/loading'

export function EventFetcherInput({
  room,
  eventsFromCloud,
  builtInEventFetcherWhitelist,
}: {
  room: number
  eventsFromCloud: EventFetcherResp | undefined
  builtInEventFetcherWhitelist: number[]
}) {
  const { t } = useTranslation()

  const [localStorageOptions, setLocalStorageOptions] = useLocalStorage()

  const { danmakuFetcherApi, eventFetcherAuth } = localStorageOptions

  const [eventFetcherApiToTest, setEventFetcherApiToTest] = useState('')
  const [debouncedEventFetcherApiToTest] = useDebouncedValue(eventFetcherApiToTest, 400)
  const [eventFetcherAuthKeyToTest, setEventFetcherAuthKeyToTest] = useState('')
  const [debouncedEventFetcherAuthKeyToTest] = useDebouncedValue(eventFetcherAuthKeyToTest, 400)

  const {
    eventsFromCloudDryrun: data,
    isCloudEventsDryrunLoading: loading,
    isCloudEventsDryrunValidating: validating,
    isCloudEventsDryrunError: error,
    cloudEventsDryrunMutate: mutate,
  } = useCloudEventsDryrun(room, debouncedEventFetcherApiToTest, debouncedEventFetcherAuthKeyToTest)

  const getCloudStatus = () => {
    if (danmakuFetcherApi) {
      if (eventsFromCloud?.status === 200) {
        return {
          color: 'text-emerald-500',
          message: <Alert tint={'success'}>当前直播间已开启云端同步，关闭本控制台依然可以正常统计礼物</Alert>,
        }
      }
      return {
        color: 'text-fg',
        message: (
          <Alert tint={'warning'}>
            当前自定义 API 未包含本直播间的云端事件，控制台关闭时的礼物将无法统计，请保持本页面开启
          </Alert>
        ),
      }
    }

    if (builtInEventFetcherWhitelist.includes(room)) {
      return {
        color: 'text-purple-500',
        message: (
          <Alert tint={'info'}>当前直播间为本站 VIP，已开启内置云端同步，关闭本控制台依然可以正常统计礼物</Alert>
        ),
      }
    }

    return {
      color: 'text-fg',
      message: (
        <>
          <Alert tint={'warning'}>当前直播间未开启云端同步，控制台关闭时的礼物将无法统计，请保持本页面开启</Alert>
          <div>
            访问{' '}
            <a href='https://subspace.institute/docs/laplace-chat/event-fetcher' target='_blank'>
              亚空间研究所
            </a>{' '}
            了解如何通过 laplace-event-fetcher 自行搭建云端事件
          </div>
        </>
      ),
    }
  }

  const status = getCloudStatus()

  return (
    <div className='space-y-2'>
      <div className='mb-4 space-y-2'>{status.message}</div>

      <div className='grid grid-cols-1 gap-x-2 gap-y-4 @md:grid-cols-2'>
        <InputWithLabel
          label={
            <OptionLabel
              label={t('danmakuFetcherApi.label')}
              desc={t('danmakuFetcherApi.desc')}
              // refLink='https://subspace.institute/docs/laplace-chat/event-fetcher'
              refLinkLabel='相关文档'
            />
          }
          placeholder={t('danmakuFetcherApi.placeholder')}
          id={'danmakuFetcherApi'}
          value={danmakuFetcherApi}
          onChange={event => {
            setEventFetcherApiToTest(event.currentTarget.value)
            setLocalStorageOptions({
              ...localStorageOptions,
              danmakuFetcherApi: event.currentTarget.value,
            })
          }}
          error={eventFetcherApiToTest && error ? t('danmakuFetcherApiTest.error') : false}
          rightSection={
            <div className='flex items-center justify-center'>
              <Button
                variant={'link'}
                className='mr-1.5 p-0.25'
                onClick={() => {
                  setEventFetcherApiToTest(danmakuFetcherApi)
                  mutate()
                }}
                disabled={!danmakuFetcherApi}
                // loading={isCloudEventsDryrunLoading || isCloudEventsDryrunValidating}
                aria-label={t('danmakuFetcherApiTest.label')}
              >
                {loading || validating ? <Loading size={'1rem'} /> : <IconRefresh size={'1rem'} />}
              </Button>
            </div>
          }
        />

        <InputWithLabel
          type='password'
          label={<OptionLabel label={t('eventFetcherAuth.label')} desc={t('eventFetcherAuth.desc')} />}
          placeholder={t('eventFetcherAuth.placeholder')}
          id={'eventFetcherAuth'}
          value={eventFetcherAuth}
          onChange={event => {
            setEventFetcherAuthKeyToTest(event.currentTarget.value)
            setLocalStorageOptions({
              ...localStorageOptions,
              eventFetcherAuth: event.currentTarget.value,
            })
          }}
          disabled={!danmakuFetcherApi}
          data-1p-ignore
        />
      </div>

      {Array.isArray(data?.data) && !validating && (
        <>
          <div className={clsx('text-sm', data.data.length > 0 ? 'text-emerald-500' : 'text-amber-500')}>
            成功载入 {nf.format(data.data.length)} 条事件
          </div>

          {/* <div className='max-h-[300px] overflow-y-auto'>
            <pre>{JSON.stringify(eventsFromCloudDryrun, null, 2)}</pre>
          </div> */}
        </>
      )}
      {error && <pre>{error.toString()}</pre>}
      {data?.success === false && <pre>{data.message}</pre>}
    </div>
  )
}
