import {
  IconAsterisk,
  IconBroadcast,
  IconDashboard,
  IconForbid,
  IconInfoCircle,
  IconLink,
  IconTestPipe,
} from '@tabler/icons-react'

import { cn } from '@/lib/cn'

import { TooltipOrPopover } from '@/components/ui/tooltip-or-popover'

interface OptionLabelProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The label text or node to display
   */
  label: string | React.ReactNode
  /**
   * Optional description that appears in tooltip
   */
  desc?: string | React.ReactNode
  /**
   * Indicates if this option is deprecated
   * Shows a deprecated icon when true
   */
  deprecated?: boolean
  /**
   * Indicates if this is a beta feature
   * Shows a beta/test icon when true
   */
  beta?: boolean
  /**
   * Indicates if this option is not available in OBS mode
   */
  noObs?: boolean
  /**
   * Indicates if this option is not available in dashboard mode
   */
  noDashboard?: boolean
  /**
   * Indicates if this option is required
   */
  showRequired?: boolean
  /**
   * External link to the option
   */
  refLink?: string
  /**
   * 参考链接的文本
   */
  refLinkLabel?: string
}

export default function OptionLabel({
  label,
  desc,
  deprecated,
  beta,
  noObs,
  noDashboard,
  showRequired,
  refLink,
  refLinkLabel = '参考链接',
  className,
  ...props
}: OptionLabelProps) {
  return (
    <div className={cn('flex items-center gap-1', className)} {...props}>
      {/* {desc ? (
        <TooltipOrPopover componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }} label={desc}>
          <span>{label}</span>
        </TooltipOrPopover>
      ) : (
        <span>{label}</span>
      )} */}
      <span className='font-medium'>{label}</span>
      {showRequired && (
        <TooltipOrPopover componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }} label={'必填'}>
          <div className='rounded-sm py-[0.125em]'>
            <IconAsterisk className='size-[0.75em] text-rose-500' />
          </div>
        </TooltipOrPopover>
      )}
      {desc ? (
        <TooltipOrPopover componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }} label={desc}>
          <IconInfoCircle className='text-fg/60 size-[1em] rounded-sm' />
        </TooltipOrPopover>
      ) : null}
      {noObs && (
        <TooltipOrPopover componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }} label={'该选项只适用于控制台模式'}>
          <IconDashboard className='size-[1em] rounded-sm text-amber-500' />
        </TooltipOrPopover>
      )}
      {noDashboard && (
        <TooltipOrPopover componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }} label={'该选项只适用于 OBS 模式'}>
          <IconBroadcast className='size-[1em] rounded-sm text-amber-500' />
        </TooltipOrPopover>
      )}
      {deprecated && (
        <TooltipOrPopover
          componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }}
          label={'弃用选项：该选项已弃用，在未来的版本更新中可能会被移除，如果您正在使用并强烈需求这个功能，请与我联系'}
        >
          <IconForbid className='size-[1em] rounded-sm text-rose-500' />
        </TooltipOrPopover>
      )}
      {beta && (
        <TooltipOrPopover
          componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }}
          label={
            '实验功能：新增加的功能，可能会由于各种因素导致该功能不稳定或发生变化，推荐喜欢尝鲜的用户测试，如果使用该功能遇到问题，欢迎进行反馈'
          }
        >
          <IconTestPipe className='size-[1em] rounded-sm text-teal-500' />
        </TooltipOrPopover>
      )}
      {refLink && (
        <TooltipOrPopover componentProps={{ tooltip: { trigger: { tabIndex: 0 } } }} label={refLinkLabel}>
          <a href={refLink} target='_blank' className='rounded-sm text-purple-500'>
            <IconLink className='size-[1em]' />
          </a>
        </TooltipOrPopover>
      )}
    </div>
  )
}
