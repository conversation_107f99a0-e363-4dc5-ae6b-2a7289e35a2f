import React from 'react'
import clsx from 'clsx'

import type { LotteryStart } from '@/lib/event-parsers/types/lottery-start'

import { nf } from '@/utils/numberFormat'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import type { AsComponentProps, EventModeProps } from '@/components/event'

import styles from './lottery-start.module.css'

export interface LotteryStartProps extends React.HTMLAttributes<HTMLDivElement> {
  data: LotteryStart
  mode: EventModeProps
  as: AsComponentProps
}

export default function LotteryStartItem({ data, mode, as, ...rest }: LotteryStartProps) {
  const options = useOptions()
  const { colorScheme } = options

  const price = data.giftPriceNormalized
  const priceDisplay = (
    <>
      <span className={`${styles.priceCurrency} price-currency`}>CN¥</span>
      <span className={`${styles.priceFigure} price-figure`}>{nf.format(price)}</span>
    </>
  )

  return (
    <div
      className={clsx(
        styles.event,
        // 天选不区分是否 highlight，为了与其他 gift 事件统一，默认为 highlight
        styles.eventSizeHighlight,
        styles[`showAs_${as}`],
        colorScheme === 'dark' && [styles.dark, 'dark'],
        'event',
        `event-size--highlight`,
        `event--${data.type}`,
        `event-type--${data.type}`,
        `event-show-as--${as}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        data.read && [styles.read, 'read']
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      data-read={data.read}
      {...rest}
    >
      <div className={`${styles.content} content`}>
        <div className={`${styles.top} top`}>
          <Avatar uid={data.origin} isRoom />
          <Avatar uid={data.origin} isRoom className={`${styles.hidden} avatar-alt-top`} />

          <div className={`${styles.message} message`}>
            天选时刻
            {as !== 'sticky' && (
              <>
                {'：'}
                <b>{data.rewardName}</b>
              </>
            )}
          </div>

          <span className={`${styles.giftIconWrap} lottery-start-icon-wrap`}>
            <picture className='inline-flex'>
              <img src={'/lottery.png'} alt={`天选`} referrerPolicy='no-referrer' loading='lazy' />
            </picture>
          </span>

          {/* 装饰用 */}
          <span
            className={`${styles.giftIconWrap} ${styles.hidden} lottery-start-icon-wrap lottery-start-icon-wrap-alt-top`}
          >
            <picture className='inline-flex'>
              <img src={'/lottery.png'} alt={`天选`} referrerPolicy='no-referrer' loading='lazy' />
            </picture>
          </span>
        </div>

        {as !== 'sticky' && (
          <div className={`${styles.message} message`}>
            <span className='lottery-start-details-message'>
              <b>{data.messageToJoin || data.message}</b>
            </span>{' '}
            {data.giftName && (
              <span className='lottery-start-details-requirements'>
                需要投喂 <b>{data.giftName}</b> <span className={`price`}>{priceDisplay}</span>
              </span>
            )}
            <Avatar uid={data.origin} isRoom className={`${styles.hidden} avatar-alt-bottom`} />
            <span
              className={`${styles.giftIconWrap} ${styles.hidden} lottery-start-icon-wrap lottery-start-icon-wrap-alt-bottom`}
            >
              <picture className='inline-flex'>
                <img src={'/lottery.png'} alt={`天选`} referrerPolicy='no-referrer' loading='lazy' />
              </picture>
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
