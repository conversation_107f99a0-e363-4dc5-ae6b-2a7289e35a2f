'use client'

import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import useLocalStorageState from 'use-local-storage-state'
import { loader, Editor as MonacoEditor } from '@monaco-editor/react'
import {
  IconAdjustments,
  IconArrowsMaximize,
  IconArrowsMinimize,
  IconBook,
  IconCheck,
  IconClipboard,
  IconTrash,
} from '@tabler/icons-react'

import { cn } from '@/lib/cn'
import { Api } from '@/lib/const'
import useGlobalStore from '@/lib/store'

import { availableTemplates } from '@/utils/builtInTemplates'

import { useDebouncedValue } from '@/hooks/useDebouncedValue'
import { useLocalStorage } from '@/hooks/useLocalStorage'

import ColorSchemeSwitcher from '@/components/ColorSchemeSwitcher'
import DownloadButton from '@/components/DownloadButton'
import { BuiltinTemplateSelect } from '@/components/editor-builtin-template-select'
import { RemoteTemplateSelect } from '@/components/editor-remote-template-select'
import { Inspector } from '@/components/Inspector'
import OptionLabel from '@/components/OptionLabel'
import { Alert } from '@/components/ui/alert'
import { alertDialog } from '@/components/ui/alert-dialog.manager'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ColorPicker } from '@/components/ui/color-picker'
import { CopyButton } from '@/components/ui/copy-button'
import { Divider } from '@/components/ui/divider'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown'
import { InputWithLabel } from '@/components/ui/input-with-label'
import { SwitchWithLabel } from '@/components/ui/switch-with-label'

// https://github.com/suren-atoyan/monaco-loader/blob/master/src/config/index.js
// https://cdn.jsdelivr.net/npm/monaco-editor@0.36.1/min/vs
// https://www.npmjs.com/package/monaco-editor?activeTab=versions
// https://github.com/microsoft/monaco-editor/issues/4071
loader.config({ paths: { vs: `${Api.Workers}/api/jsd/monaco-editor@0.52.2/min/vs` } })

// loader.config({ 'vs/nls': { availableLanguages: { '*': 'zh-cn' } } })

export function Editor() {
  const [hasExternalRes, setHasExternalRes] = useState(false)
  const [isEditorFullscreen, setIsEditorFullscreen] = useState(false)
  const [customCss, setCustomCss] = useLocalStorageState('customCss', { defaultValue: '' })
  const [editorContent, setEditorContent] = useState(customCss || '')
  const [debouncedContent] = useDebouncedValue(editorContent, 400)

  const demoBgColor = useGlobalStore(state => state.demoBgColor)
  const updateDemoBgColor = useGlobalStore.getState().updateDemoBgColor
  const demoGiftEffectLayer = useGlobalStore(state => state.demoGiftEffectLayer)
  const updateDemoGiftEffectLayer = useGlobalStore.getState().updateDemoGiftEffectLayer

  const [localStorageOptions, setLocalStorageOptions] = useLocalStorage()
  const { colorScheme, uiLang, sceneName, showGiftEffect, enableCloudTheme, cloudTheme } = localStorageOptions

  const { t } = useTranslation()

  const openModal = (value: string) =>
    alertDialog.open({
      title: '确认应用样式模版？',
      children: (
        <div>即将应用新的样式模版，此操作会直接覆盖当前工作区的所有内容，请确认已在别处备份当前 CSS 样式后再继续</div>
      ),
      labels: { confirm: '确认应用', cancel: '取消' },
      onCancel: () => console.log('Update template cancelled'),
      onConfirm: async () => {
        if (value && value === 'empty') {
          setEditorContent('')
          setCustomCss('')
          setLocalStorageOptions({
            ...localStorageOptions,
            cloudTheme: '',
          })
        }

        if (value && value !== 'empty') {
          const css = await import(`@/data/templates/${value}`)
          const newContent = css.default
          setEditorContent(newContent)
          setCustomCss(newContent)
          setLocalStorageOptions({
            ...localStorageOptions,
            cloudTheme: value,
          })
        }
      },
    })

  const openEmptyModal = () =>
    alertDialog.open({
      title: '清空工作区？',
      children: <div>此操作会直接清空当前工作区的所有内容，请确认已在别处备份当前 CSS 样式后再继续</div>,
      labels: { confirm: '确认清空', cancel: '取消' },
      onCancel: () => console.log('Empty workspace cancelled'),
      onConfirm: () => {
        setEditorContent('')
        setCustomCss('')
      },
      // cancelProps: { tint: 'accent', variant: 'solid' },
      confirmProps: { tint: 'red' },
    })

  // Save debounced content to localStorage
  useEffect(() => {
    setCustomCss(debouncedContent)
  }, [debouncedContent, setCustomCss])

  useEffect(() => {
    // TODO: it does not reactive
    if (uiLang === 'zh-Hans') {
      loader.config({ 'vs/nls': { availableLanguages: { '*': 'zh-cn' } } })
    } else {
      loader.config({ 'vs/nls': { availableLanguages: { '*': '' } } })
    }
  }, [uiLang])

  useEffect(() => {
    const regex = /url\(['"]?https?:\/\/[^)]+\)/gi
    if (customCss && regex.test(customCss)) {
      setHasExternalRes(true)
    } else {
      setHasExternalRes(false)
    }
  }, [customCss])

  return (
    <>
      <Divider
        className='mt-4 mb-3'
        label={
          <div className='flex items-center gap-x-1'>
            {t('Templates')}
            <Badge asChild tint={'accent'} size={'sm'}>
              <a
                href={'https://subspace.institute/docs/laplace-chat/templates'}
                className='focus-ring gap-x-0.5'
                target='_blank'
              >
                <IconBook className='size-3' />
                {t('Documentation')}
              </a>
            </Badge>
          </div>
        }
      />

      <div className='grid grid-cols-1 gap-4 @2xl:grid-cols-2'>
        <div className='flex-auto space-y-3'>
          <BuiltinTemplateSelect
            placeholder='在此选择一个内置模版…'
            data={availableTemplates}
            onChange={(value: string) => {
              openModal(value)
            }}
          />

          <SwitchWithLabel
            className='flex-1'
            label={
              <OptionLabel
                label={'云端载入'}
                desc={
                  '默认关闭。当您开启本选项时，将默认从本站内置的云端载入模版，您不需要在 OBS 中复制下方 CSS 样式，样式将自动保持最新状态'
                }
                noDashboard
              />
            }
            checked={enableCloudTheme}
            onChange={e =>
              setLocalStorageOptions({
                ...localStorageOptions,
                enableCloudTheme: e.currentTarget.checked,
              })
            }
          />
        </div>

        <RemoteTemplateSelect />
      </div>

      <Divider className='mt-4 mb-3' label={t('Advanced CSS Editor')} />

      <div className={cn('mt-2 space-y-2', isEditorFullscreen && 'bg-bg absolute inset-0 z-11 mt-0 p-4 pt-2')}>
        <div className='flex items-center justify-between gap-2'>
          <div className='flex items-center gap-2'>
            <ColorPicker initialColor={demoBgColor} onColorChange={updateDemoBgColor} placeholder='预览背景色' />

            <Inspector />

            <CopyButton value={customCss} timeout={2000}>
              {({ copied, copy }) => {
                if (copied) {
                  toast.success(`CSS 样式复制成功`, {
                    id: 'template-copyied',
                    description: `请打开 OBS 或对应的直播间软件，将其粘贴至对应的自定义样式输入框中`,
                  })
                }

                return (
                  <Button
                    onClick={copy}
                    disabled={!customCss}
                    className='p-1.5'
                    variant={copied ? 'solid' : 'link'}
                    tint={copied ? 'accent' : 'default'}
                    aria-label='复制到剪切板'
                  >
                    {copied ? <IconCheck size='1.125rem' /> : <IconClipboard size='1.125rem' />}
                  </Button>
                )
              }}
            </CopyButton>

            <DownloadButton label='下载/备份模版' data={customCss} fileName={`theme`} />

            <Button
              variant='link'
              tint='rose'
              onClick={() => {
                openEmptyModal()
              }}
              className='p-1.5'
              disabled={!customCss}
              aria-label='清空工作区'
            >
              <IconTrash size='1.125rem' />
            </Button>
          </div>

          <div className='flex items-center gap-2'>
            {isEditorFullscreen && <ColorSchemeSwitcher />}

            <Button
              onClick={() => {
                setIsEditorFullscreen(!isEditorFullscreen)
              }}
              className='p-1'
              tint={isEditorFullscreen ? 'accent' : 'default'}
              variant={isEditorFullscreen ? 'default' : 'link'}
              aria-label={isEditorFullscreen ? '缩小编辑器' : '最大化编辑器'}
            >
              {isEditorFullscreen ? <IconArrowsMinimize size='1.125rem' /> : <IconArrowsMaximize size='1.125rem' />}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger className='group focus-ring data-[state=open]:text-ac flex items-center rounded-sm'>
                <IconAdjustments size='1.125rem' />
              </DropdownMenuTrigger>
              <DropdownMenuContent className='w-56' align='end'>
                <DropdownMenuCheckboxItem
                  disabled={!showGiftEffect}
                  checked={demoGiftEffectLayer}
                  onCheckedChange={value => updateDemoGiftEffectLayer(value)}
                >
                  <div>
                    <div>显示礼物特效图层</div>
                    <div className='text-fg/50 text-xs'>仅限于配置器中预览及测试，不会对 OBS 模式产生影响</div>
                  </div>
                </DropdownMenuCheckboxItem>

                <DropdownMenuSeparator />

                <div className='space-y-2 px-3 pt-3 pb-1.5'>
                  <div className='space-y-2'>
                    <InputWithLabel
                      label={<OptionLabel label={t('sceneName.label')} />}
                      placeholder={t('sceneName.placeholder')}
                      value={sceneName}
                      onChange={event =>
                        setLocalStorageOptions({
                          ...localStorageOptions,
                          sceneName: event.currentTarget.value,
                        })
                      }
                    />
                    <div className='text-fg/50 text-xs'>{t('sceneName.desc')}</div>
                  </div>
                </div>

                <DropdownMenuSeparator />

                <DropdownMenuItem asChild>
                  <a href='https://storybook.laplace.live/?path=/docs/laplace-chat_welcome--docs' target='_blank'>
                    <div>
                      <div>查看前端组件簿</div>
                      <div className='text-fg/50 text-xs'>方便您更好的了解事件的结构</div>
                    </div>
                  </a>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <a href='https://fonts.google.com' target='_blank'>
                    <div>
                      <div>Google Fonts</div>
                      <div className='text-fg/50 text-xs'>外部 web fonts 字形库</div>
                    </div>
                  </a>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <MonacoEditor
          className={'border [&_.monaco-editor]:w-full! [&_.overflow-guard]:w-full!'}
          // height="400px"
          height={isEditorFullscreen ? `calc(100dvh - 8px - 16px - 32px - 8px)` : 400}
          onMount={(editor, monaco) => {
            const model = editor.getModel()
            if (model) {
              model.updateOptions({ tabSize: 2, insertSpaces: true })
            }
          }}
          onChange={value => {
            setEditorContent(value ?? '')
          }}
          // https://github.com/microsoft/monaco-editor/issues/4071
          defaultLanguage='css'
          defaultValue={customCss || ''}
          value={editorContent}
          theme={colorScheme === 'dark' ? 'vs-dark' : 'light'}
          loading={uiLang === 'zh-Hans' ? '载入编辑器…' : 'Loading editor…'}
          // options={{
          //   contextmenu: false,
          // }}
        />
      </div>

      <div className='mt-4 space-y-4'>
        {hasExternalRes && (
          <Alert tint='danger'>
            该模版包含外部资源（包含在 <code>url(...)</code> 链接内），如果您使用的是第三方模版，请确认该资源为正常资源
          </Alert>
        )}

        {enableCloudTheme ? (
          <Alert tint='info'>
            您已开启云端模版，此处的 CSS 样式仅供参考，您可以删除其中大部分内容，仅保留您需要覆盖的 CSS
            样式，然后将其复制到 OBS 的「自定义 CSS」中，即可覆盖云端模版的样式
          </Alert>
        ) : (
          <Alert tint='warning'>
            请注意，此处仅提供预览，不会直接应用到 OBS，设计完后需要将上方样式复制到 OBS 的「自定义 CSS」中方可生效
          </Alert>
        )}
      </div>
    </>
  )
}
