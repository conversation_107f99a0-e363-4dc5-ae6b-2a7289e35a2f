import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useEventBridgeAutoConnect, useEventBridgeSettings } from '@/lib/event-bridge/storage'
import { useEventBridgeStore } from '@/lib/event-bridge/store'
import type { ConnectionSettings } from '@/lib/event-bridge/utils'

import OptionLabel from '@/components/OptionLabel'
import { Alert } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { InputWithLabel } from '@/components/ui/input-with-label'
import { Label } from '@/components/ui/label'

export const EventBridgeConnectionManager: React.FC = () => {
  const [storedSettings, setStoredSettings] = useEventBridgeSettings()
  const [autoConnect, setAutoConnect] = useEventBridgeAutoConnect()

  const { t } = useTranslation()

  // Get Event Bridge state and actions from the store
  const { isConnected, isConnecting, connectionError, connect, disconnect } = useEventBridgeStore()

  // Connection settings form state
  const [address, setAddress] = useState('localhost')
  const [port, setPort] = useState<number>(9696)
  const [password, setPassword] = useState('')

  // Load saved settings on component render
  useEffect(() => {
    if (storedSettings) {
      setAddress(storedSettings.address)
      setPort(storedSettings.port)
      if (storedSettings.password) {
        setPassword(storedSettings.password)
      }
    }
  }, [storedSettings])

  // Connect to Event Bridge WebSocket server
  const handleConnect = async () => {
    const settings: ConnectionSettings = {
      address,
      port,
      password: password || undefined,
    }

    // Save settings before attempting connection
    setStoredSettings(settings)

    // Wait for settings to be saved to localStorage
    await new Promise(resolve => setTimeout(resolve, 100))

    // Try to connect
    await connect(settings)
  }

  // Handle manual disconnect
  const handleDisconnect = async () => {
    await disconnect()
  }

  return (
    <div className='space-y-3'>
      <div className='grid gap-x-2 gap-y-3'>
        <div className='grid grid-cols-1 items-start gap-x-2 gap-y-4 @sm:grid-cols-3'>
          <div className='space-y-2'>
            <InputWithLabel
              id='event-bridge-address'
              value={address}
              onChange={e => setAddress(e.target.value)}
              placeholder='localhost'
              label={
                <OptionLabel
                  label={t('eventBridge.address.label', 'Server')}
                  desc={t('eventBridge.address.desc', 'Enter the IP address or domain name of the Event Bridge server')}
                />
              }
              disabled={isConnected || isConnecting}
            />
          </div>

          <div className='space-y-2'>
            <InputWithLabel
              type='number'
              id='event-bridge-port'
              value={port}
              onChange={e => setPort(Number(e.target.value))}
              placeholder='9696'
              label={
                <OptionLabel
                  label={t('eventBridge.port.label', 'Port')}
                  desc={t('eventBridge.port.desc', 'Enter the port of the Event Bridge server')}
                />
              }
              disabled={isConnected || isConnecting}
              defaultValue={9696}
              min={1}
              max={65535}
            />
          </div>

          <div className='space-y-2'>
            <InputWithLabel
              id='event-bridge-password'
              type='password'
              value={password}
              placeholder='Optional'
              onChange={e => setPassword(e.target.value)}
              label={
                <OptionLabel
                  label={t('eventBridge.password.label', 'Password')}
                  desc={t('eventBridge.password.desc', 'Optional password for Event Bridge authentication')}
                />
              }
              disabled={isConnected || isConnecting}
            />
          </div>
        </div>

        <div className='flex flex-col gap-2 @sm:flex-row @sm:items-center'>
          <div className='flex flex-auto items-center space-x-2'>
            <Checkbox
              id='event-bridge-auto-connect'
              checked={autoConnect}
              onChange={e => setAutoConnect(e.target.checked)}
            />
            <Label htmlFor='event-bridge-auto-connect'>{t('eventBridge.autoConnect', 'Auto Connect')}</Label>
          </div>

          {!isConnected ? (
            <Button
              onClick={handleConnect}
              disabled={isConnecting || !address || !port}
              tint='accent'
              loading={isConnecting}
            >
              {t('eventBridge.connect', 'Connect')}
            </Button>
          ) : (
            <Button onClick={handleDisconnect} tint='rose'>
              {t('eventBridge.disconnect', 'Disconnect')}
            </Button>
          )}

          {/* <div>
            {isConnected ? (
              <Badge variant='dot' tint='green'>
                Connected
              </Badge>
            ) : (
              <Badge variant='dot' tint='default'>
                Disconnected
              </Badge>
            )}
          </div> */}
        </div>

        {connectionError && <Alert tint='warning'>{connectionError}</Alert>}
      </div>
    </div>
  )
}
