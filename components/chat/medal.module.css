@layer components {
  .fansMedal {
    --medal-color-default: #a0a2a8;

    position: relative;
    display: flex;
    align-items: center;
    border: var(--1px) solid;
    border-color: var(--medal-border-color, var(--medal-color, var(--medal-color-default)));
    border-radius: calc(var(--1px) * 12);
    line-height: 1.2;
    font-size: calc(var(--1px) * 14);
    font-weight: normal;
    background-image: linear-gradient(
      45deg,
      var(--medal-color, var(--medal-color-default)),
      var(--medal-color-alt, var(--medal-color-default))
    );
    white-space: nowrap;
    /* Make .fansMedalContent clipped with correct border-radius */
    /* overflow: hidden; */
  }

  .fansMedalContent {
    color: #fff;
    padding-left: calc(var(--1px) * 6);
    padding-right: calc(var(--1px) * 3);

    /* 常规粉丝牌 */
    /* .fansMedalType0 & {
    } */

    /* 大航海套票 */
    .fansMedalType1 & {
      padding-right: calc(var(--1px) * 6);
    }
  }

  .fansMedalLevel {
    color: var(--medal-color, var(--medal-color-default));
    font-size: calc(var(--1px) * 11);
    margin-right: calc(var(--1px) * 2);
    padding: 0 calc(var(--1px) * 3.5);
    background: #fff;
    border-radius: calc(var(--1px) * 12);
    font-weight: bold;
  }

  .fansMedalDimmed {
    filter: grayscale(0.8);
    opacity: 0.5;
  }

  /* 大航海套票 */
  .fansMedalType1 {
    --medal-color: rgba(67, 94, 206, 0.8);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: var(--medal-color);

    --guard-icon: url(https://rsrc.laplace.cn/assets/guard-icons/icon-group.png);
  }

  /* 1-4 */
  .fansMedalTier1 {
    --medal-color: rgba(87, 98, 167, 0.84);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: var(--medal-color);
  }

  /* 5-8 */
  .fansMedalTier2 {
    --medal-color: rgba(88, 102, 199, 0.84);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: var(--medal-color);
  }

  /* 9-12 */
  .fansMedalTier3 {
    --medal-color: rgba(89, 111, 224, 0.84);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: var(--medal-color);
  }

  /* 13-16 */
  .fansMedalTier4 {
    --medal-color: rgba(200, 93, 196, 0.84);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: var(--medal-color);
  }

  /* 17-20 */
  .fansMedalTier5 {
    --medal-color: hsla(0, 62%, 64%, 0.84);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: var(--medal-color);
  }

  /* 21-24 */
  .fansMedalTier6 {
    --medal-color: rgba(67, 179, 227, 0.96);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: #5fc7f4ff;
  }

  /* 25-28 */
  .fansMedalTier7 {
    --medal-color: rgba(71, 117, 239, 0.96);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: #58a1f8ff;
  }

  /* 29-32 */
  .fansMedalTier8 {
    --medal-color: rgba(150, 96, 229, 0.96);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: #d47affff;
  }

  /* 33-36 */
  .fansMedalTier9 {
    --medal-color: rgba(190, 73, 96, 0.96);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: #f18087ff;
  }

  /* 37-40 */
  .fansMedalTier10 {
    --medal-color: rgba(255, 132, 43, 0.8);
    --medal-color-alt: var(--medal-color);
    --medal-border-color: #ffce20ff;
  }

  .fansMedalHasGuard {
    margin-left: calc(var(--1px) * 2);

    /* v2 版本粉丝牌不再单独高亮上舰状态 */
    /* &.fansMedalHasGuard3 {
      --medal-border-color: #67e8ff;
    } */

    /* &.fansMedalHasGuard2,
    &.fansMedalHasGuard1 {
      --medal-border-color: #ffe854;
    } */

    .fansMedalContent {
      padding-left: calc(var(--1px) * 3 + calc(var(--event-line-height) * 0.8));

      /* 大航海套票 */
      .fansMedalType1 & {
        padding-left: calc(var(--1px) * 0 + calc(var(--event-line-height) * 0.8));
      }
    }

    .guardBadge {
      position: absolute;
      width: calc(var(--event-line-height) * 1.125);
      height: calc(var(--event-line-height) * 1.125);
      top: calc(var(--1px) * -5);
      left: calc(var(--1px) * -5);

      /* 大航海套票 */
      .fansMedalType1 & {
        width: calc(var(--event-line-height) * 0.7);
        height: calc(var(--event-line-height) * 0.7);
        top: calc(var(--1px) * 0);
        left: calc(var(--1px) * 0);
      }
    }
  }

  .guardBadge {
    width: var(--event-line-height);
    height: var(--event-line-height);
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: var(--guard-icon);
    flex-shrink: 0;
  }

  .guardBadge1 {
    --guard-icon: url(https://rsrc.laplace.cn/assets/guard-icons/icon-1.png);

    /* 目前大航海图标 千舰 和 万舰 的图标是一样的，只有 frame 头像框有区分 */
    &.perkLevel2,
    &.perkLevel3 {
      --guard-icon: url(https://rsrc.laplace.cn/assets/guard-icons/icon-1-1k.png);
    }
  }
  .guardBadge2 {
    --guard-icon: url(https://rsrc.laplace.cn/assets/guard-icons/icon-2.png);

    &.perkLevel2,
    &.perkLevel3 {
      --guard-icon: url(https://rsrc.laplace.cn/assets/guard-icons/icon-2-1k.png);
    }
  }
  .guardBadge3 {
    --guard-icon: url(https://rsrc.laplace.cn/assets/guard-icons/icon-3.png);

    &.perkLevel2,
    &.perkLevel3 {
      --guard-icon: url(https://rsrc.laplace.cn/assets/guard-icons/icon-3-1k.png);
    }
  }
}
