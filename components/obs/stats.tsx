import { useTranslation } from 'react-i18next'

import { useOBSStore } from '@/lib/obs/store'

import { nf } from '@/utils/numberFormat'

export const OBSStats: React.FC = () => {
  const { isConnected, stats, streamStatus } = useOBSStore()
  const { t } = useTranslation()

  // Format a value with unit
  const formatValue = (value: number, unit: string, precision = 2): string => {
    return `${value.toFixed(precision)} ${unit}`
  }

  // Format percentage
  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`
  }

  // Format bitrate
  const formatBitrate = (bytes: number): string => {
    return `${(bytes * 8).toFixed(0)} kbps`
  }

  if (!isConnected) {
    return null
  }

  return (
    <div>
      {/* Stream Status */}
      <div className='grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4'>
        <StatCard
          title='Streaming'
          value={streamStatus?.outputActive ? 'Active' : 'Inactive'}
          color={streamStatus?.outputActive ? 'green' : 'default'}
        />

        {streamStatus && streamStatus.outputActive && (
          <>
            <StatCard
              title='Bitrate'
              value={formatBitrate(
                streamStatus.outputDuration ? streamStatus.outputBytes / streamStatus.outputDuration : 0
              )}
              color='default'
            />

            <StatCard
              title='Congestion'
              value={formatPercentage(streamStatus.outputCongestion * 100)}
              color={
                streamStatus.outputCongestion > 0.5 ? 'red' : streamStatus.outputCongestion > 0.1 ? 'yellow' : 'green'
              }
            />

            <StatCard
              title='Dropped Frames'
              value={`${streamStatus.outputSkippedFrames} / ${streamStatus.outputTotalFrames} (${formatPercentage((streamStatus.outputSkippedFrames / Math.max(1, streamStatus.outputTotalFrames)) * 100)})`}
              color={streamStatus.outputSkippedFrames > 0 ? 'yellow' : 'green'}
            />
          </>
        )}

        {stats && (
          <>
            <StatCard
              title='FPS'
              value={formatValue(stats.activeFps, 'fps', 1)}
              color={stats.activeFps < 30 ? 'yellow' : 'green'}
            />

            <StatCard
              title='CPU Usage'
              value={formatPercentage(stats.cpuUsage)}
              color={stats.cpuUsage > 80 ? 'red' : stats.cpuUsage > 50 ? 'yellow' : 'green'}
            />

            <StatCard
              title='Memory Usage'
              value={`${nf.format(stats.memoryUsage, { maximumFractionDigits: 0 })} MB`}
              color={stats.memoryUsage > 80 ? 'red' : stats.memoryUsage > 50 ? 'yellow' : 'green'}
            />

            {/* <StatCard
            title='Disk Space'
            value={formatValue(stats.availableDiskSpace / 1024 / 1024, 'GB')}
            color={stats.availableDiskSpace < 10 * 1024 * 1024 ? 'red' : 'green'}
          /> */}

            <StatCard
              title='Skipped Frames (Output)'
              value={stats.outputSkippedFrames.toString()}
              color={stats.outputSkippedFrames > 0 ? 'yellow' : 'green'}
            />

            <StatCard
              title='Skipped Frames (Render)'
              value={stats.renderSkippedFrames.toString()}
              color={stats.renderSkippedFrames > 0 ? 'yellow' : 'green'}
            />

            <StatCard
              title='Frame Render Time'
              value={formatValue(stats.averageFrameRenderTime, 'ms')}
              color={stats.averageFrameRenderTime > 10 ? 'yellow' : 'green'}
            />

            {/* <StatCard
            title='WebSocket Messages'
            value={`${stats.webSocketSessionIncomingMessages} in / ${stats.webSocketSessionOutgoingMessages} out`}
            color='default'
          /> */}
          </>
        )}
      </div>
    </div>
  )
}

// Stat card component
interface StatCardProps {
  title: string
  value: string
  color: 'red' | 'yellow' | 'green' | 'default'
}

const StatCard: React.FC<StatCardProps> = ({ title, value, color }) => {
  const getColorClass = () => {
    switch (color) {
      case 'red':
        return 'bg-rose-500'
      case 'yellow':
        return 'bg-yellow-500'
      case 'green':
        return 'bg-emerald-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className='space-y-2 rounded-md border p-3'>
      <h3 className='text-fg/60 text-xs leading-none font-normal'>{title}</h3>
      <div className='flex items-center gap-x-1'>
        <div className={`h-4 w-1 rounded-md ${getColorClass()}`} />
        <span className='font-logo text-lg leading-none font-bold'>{value}</span>
      </div>
    </div>
  )
}
