import React from 'react'
import clsx from 'clsx'

import type { EffectMessage } from '@/lib/event-parsers/types/effect-message'
import useGlobalStore from '@/lib/store'

import { getPerkLevel } from '@/utils/eventAssetsUtils'

import { useCssTransition } from '@/hooks/useCssTransition'
import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import Medal from '@/components/chat/medal'
import WealthMedal from '@/components/chat/wealth-medal'
import type { EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'
import UserDropdown from '@/components/UserDropdown'

import medalStyles from '../chat/medal.module.css'
import styles from './danmaku.module.css'

export interface EffectDanmakuProps extends React.HTMLAttributes<HTMLDivElement> {
  data: EffectMessage
  mode: EventModeProps
}

/**
 * Effect Message event component
 */
export default function EffectDanmakuItem({ data, mode, ...rest }: EffectDanmakuProps) {
  const dashboardLiveGuards = useGlobalStore(state => state.dashboardLiveGuards)

  const cssStatus = useCssTransition()
  const options = useOptions()
  const {
    colorScheme,
    showUsername,
    showAvatar,
    showMedal,
    showModBadge,
    baseFontSize,
    showGuardBadge,
    showWealthMedal,
    useCst,
  } = options

  const isAltLayout = false // Effect messages should not use alt layout
  const MainDom = React.Fragment
  const mainDomProps = {}

  const avatarSize = 320 * (baseFontSize / 20)
  const avatarUrl = data?.avatar ? `${data.avatar}@${avatarSize}w_${avatarSize}h` : undefined

  return (
    <div
      className={clsx(
        styles.event,
        styles[`guardLevel${data.guardType}`],
        colorScheme === 'dark' && [styles.dark, 'dark'],
        'event',
        `event--message`,
        `event--${data.type}`,
        `event-type--message`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        `guard-level--${data.guardType}`,
        data.read && [styles.read, 'read'],
        mode === 'obs' && cssStatus
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      data-read={data.read}
      {...rest}
    >
      <MainDom {...mainDomProps}>
        <div className={clsx(styles.meta, 'meta')}>
          <TimeDisplay date={data.timestampNormalized} localTime={!useCst} />

          {/* Avatar */}
          {showAvatar && (
            <Avatar
              avatar={avatarUrl}
              uid={data.uid}
              guard={data.guardType}
              className='sender-avatar avatar--sender'
              perkLevel={getPerkLevel(dashboardLiveGuards, {
                roomId: data.origin,
              })}
            />
          )}

          {/* Wealth medal */}
          {showWealthMedal && !!data?.wealthMedalLevel && <WealthMedal data={data.wealthMedalLevel} />}

          {/* Fans medal badge */}
          {(showMedal || mode === 'dashboard') && (
            <Medal data={data.medal} mode={mode} perkLevel={data.medal.resolvedPerkLevel} />
          )}

          {/* Username */}
          {(showUsername || mode === 'dashboard') && (
            <div className={`${styles.username} username`}>
              {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}
              {mode === 'dashboard' && (
                <UserDropdown event={data}>
                  <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
                </UserDropdown>
              )}
            </div>
          )}

          {/* Guard badge */}
          {showGuardBadge !== false && data.guardType > 0 && (
            <span
              className={clsx(
                medalStyles.guardBadge,
                medalStyles[`guardBadge${data.guardType}`],
                medalStyles[
                  `perkLevel${getPerkLevel(dashboardLiveGuards, {
                    roomId: data.origin,
                  })}`
                ],
                'guard-badge-in-meta',
                'guard-badge',
                `guard-badge--${data.guardType}`,
                `perk-level-${getPerkLevel(dashboardLiveGuards, {
                  roomId: data.origin,
                })}`
              )}
            ></span>
          )}
        </div>

        {/* Content */}
        <span className={`${styles.message} message`}>
          <span className={`effect-message-action`}>{data.messageRaw.action}</span>{' '}
          <picture className={`${styles.effectMessageIconWrap} effect-message-icon-wrap inline-flex`}>
            <img src={data.messageRaw.img} alt={'礼物图标'} referrerPolicy='no-referrer' loading='lazy' />
          </picture>{' '}
          <span className={`effect-message-name`}>{data.messageRaw.name}</span>{' '}
          <span className={`effect-message-num`}>{data.messageRaw.num}</span>{' '}
          <span className={`effect-message-prefix`}>{data.messageRaw.prefix}</span>
          <span className={`effect-message-text`}>{data.messageRaw.text}</span>
        </span>
      </MainDom>
    </div>
  )
}
