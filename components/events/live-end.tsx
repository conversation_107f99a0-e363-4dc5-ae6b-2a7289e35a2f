import React from 'react'
import clsx from 'clsx'

import type { LiveEnd } from '@/lib/event-parsers/types/live-end'

import type { EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'

import styles from './live-end.module.css'

export interface LiveEndProps extends React.HTMLAttributes<HTMLDivElement> {
  data: LiveEnd
  mode: EventModeProps
}

export default function LiveEndItem({ data, mode, ...rest }: LiveEndProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <span className='message'>
        {data.message} <TimeDisplay date={data.timestampNormalized} localTime={false} forceDisplay />
      </span>
    </div>
  )
}
