const TemplateOrange = /* css */ `/***** 原作者：@橙橙子君 *****/
/* 注：本主题支持通过调节深色模式切换样式：
      浅色模式——基础样式（白底）
      深色模式——主题色样式（灰底/彩色底） */

/* 引用在线字体 */
/* 思源黑体 */
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@200;300;400;500;600;700;900&display=swap");

/* 使用层叠层，请勿删除此处和最后一行的后括号 */
@layer template {

  /* 全局背景透明，可保证 OBS 载入弹幕机的过程中背景全程透明 */
  body {
    background-color: rgba(0, 0, 0, 0);
  }


  /*** 全局设定 ***/
  /** 字体设定 **/
  /* 建议使用免费可商用字体、避免版权问题 */
  /* 注意考虑弹幕多语言问题（简、繁、日语），添加备用字体，建议最后添加全语言字体（如思源系列、阿里巴巴普惠体）保底 */
  .event {
    --event-font-family: "荆南麦圆体", "阿里巴巴普惠体",
      /* 以上字体请在电脑上安装，可自定义已安装字体，可添加、修改，名字需准确 */
      /* 以下为在线字体 */
      'Noto Sans SC',
      /* 思源黑体 */
      /* 下面为系统自带字体（非免费商用），不建议使用，如商用请注意版权问题 */
      var(--fontstack-sans-serif);
    /* 字重 （字体粗细）*/
    font-weight: 500;
  }

  /** 间距调整 **/
  /* 注：--event-font-size 对应设置中的基准字号，
       --1px = var(--event-font-size) * 0.0625，
       --event-line-height = var(--event-font-size) * 1.5
       可在改变字号的情况下缩放弹幕样式而不改变布局 */
  /* 例：calc(var(--1px) * 50); 当基准字号=20时，输出像素=20*0.065*50=65px */
  /* 弹幕类 */
  .event--message {
    margin-bottom: calc(var(--1px) * 12);
  }

  /* 互动消息类 */
  .event--interaction,
  .event--like-click {
    margin-bottom: calc(var(--1px) * 6);
  }

  /* 礼物SC舰长类 */
  .event--gift.event-show-as--normal.event-size--normal,
  .event--gift.event-show-as--normal.event-size--highlight,
  .event--mvp.event-show-as--normal,
  .event--red-envelope-start.event-show-as--normal,
  .event--red-envelope-result,
  .event--lottery-start.event-show-as--normal,
  .event--lottery-result,
  .event--superchat.event-show-as--normal,
  .event--toast.event-show-as--normal {
    margin-bottom: calc(var(--1px) * 12);
  }

  /** 弹幕样式（颜色） **/
  /* 总变量 */
  .event--message {
    --text: #000;
    --bg: rgb(245, 243, 242);
  }

  /* 总督变量 */
  .guard-level--1 {
    --bg: rgb(173, 66, 47);
  }

  /* 提督变量 */
  .guard-level--2 {
    --bg: rgb(81, 55, 139);
  }

  /* 舰长变量 */
  .guard-level--3 {
    --bg: rgb(50, 115, 105);
  }

  /* 普通用户变量 */
  .guard-level--0 {
    --bg: rgb(102, 102, 102);
  }

  /* 主播变量 */
  .user-type--streamer {
    --bg: rgb(242, 184, 24);
  }

  /* 房管变量 */
  .user-type--mod {
    --bg: rgb(30, 80, 162);
  }

  /** 礼物SC文本颜色变量 **/
  /* 此处可使用 .event-superchat-rank--[n] 和 .event-gift-rank--[n] 分别定义样式，
也可以像我这样使用 .event-price-rank--[n] 统一定义样式
.event-price-rank 档次：
    1 -- RMB 30
    2 -- RMB 50
    3 -- RMB 100
    4 -- RMB 500
    5 -- RMB 1000
    6 -- RMB 2000 */
  /* bg1为颜色主色调，bg2为颜色次色调，
主色调用于填充普通礼物文字、高亮礼物背景、SC正文（下半）背景、礼物栏；
次色调用于填充SC信息（上半）背景 */
  .event-price-rank--1 {
    --bg1: rgb(42, 96, 178);
    --bg2: rgb(237, 245, 255);
  }

  .event-price-rank--2 {
    --bg1: rgb(66, 125, 157);
    --bg2: rgb(219, 255, 253);
  }

  .event-price-rank--3 {
    --bg1: rgb(226, 181, 43);
    --bg2: rgb(255, 241, 197);
  }

  .event-price-rank--4 {
    --bg1: rgb(225, 148, 67);
    --bg2: rgb(254, 234, 209);
  }

  .event-price-rank--5 {
    --bg1: rgb(229, 77, 77);
    --bg2: rgb(255, 231, 228);
  }

  .event-price-rank--6 {
    --bg1: rgb(147, 33, 58);
    --bg2: rgb(230, 177, 183);
  }

  /** 阴影设定 **/
  /* 大阴影 */
  .event--message,
  .event--message .avatar-wrap,
  .event--gift.event-show-as--normal.event-size--highlight,
  .event--mvp.event-show-as--normal,
  .event--superchat.event-show-as--normal,
  .event--toast.event-show-as--normal {
    filter: drop-shadow(0 0 calc(var(--1px) * 1) rgb(45, 40, 103, 0.6));
  }

  /* 小阴影 */
  .event--interaction,
  .event--like-click,
  .event--user-block,
  .event--live-warning,
  .event--live-cutoff,
  .event--room-mute-on,
  .event--room-mute-off,
  .event--room-name-update,
  .event--notice,
  .event--live-start,
  .event--live-end,
  .event--gift.event-show-as--normal.event-size--normal,
  .event-show-as--sticky {
    filter: drop-shadow(0 0 calc(var(--1px) * 0.8) rgb(45, 40, 103, 0.6));
  }


  /*** 弹幕部分 ***/
  /** 气泡样式 **/
  .event--message {
    position: relative;
    display: block;
    /* margin相关变量用于调节气泡外边距 */
    margin-left: calc(var(--1px) * 50);
    margin-right: calc(var(--1px) * 12);
    margin-top: calc(var(--1px) * 8);
    margin-bottom: calc(var(--1px) * 10);
    /* padding相关变量用于调节气泡内边距
      四边写法为：padding:上 右 下 左; */
    padding: calc(var(--1px) * 8) calc(var(--1px) * 16) calc(var(--1px) * 4) calc(var(--1px) * 12);
    /* color为正文颜色，background-color为气泡背景颜色 */
    color: var(--text);
    background-color: rgb(245, 243, 242);
    /* avatar-size为头像大小 */
    --avatar-size: calc(var(--1px) * 36);
    /* width: fit-content意为弹幕长度与内容匹配
      width: 百分比/具体值可以固定弹幕长度 */
    width: fit-content;
    min-width: 4em;
    /* border相关变量用于调节气泡四角弧度 */
    border-top-left-radius: calc(var(--1px) * 4);
    border-top-right-radius: calc(var(--1px) * 10);
    border-bottom-left-radius: calc(var(--1px) * 4);
    border-bottom-right-radius: calc(var(--1px) * 10);
    /* 以上为基本属性的简单介绍，下同不再赘述
      更多属性相关可参考 https://www.runoob.com/cssref/ */
  }

  /** 名字样式 **/
  .event--message .username {
    position: relative;
    display: block;
    border-radius: calc(var(--1px) * 4);
    padding: calc(var(--1px) * 2) calc(var(--1px) * 6);
    color: #fff;
    background-color: var(--bg);
    /* font-size为字体大小，
     line-height为元素高（此处为名字背景的高度） */
    font-size: calc(var(--1px) * 14.5);
    line-height: calc(var(--1px) * 14.5);
    font-weight: 500;
  }

  /** 正文样式 **/
  .event--message .message {
    position: relative;
    /* 如果弹幕需要换行，使用display: block;
      如果弹幕不需要换行，使用display: inline; */
    display: block;
    font-size: calc(var(--1px) * 18);
    line-height: calc(var(--1px) * 24);
    padding: calc(var(--1px) * 4.5) calc(var(--1px) * 1);
  }

  /** 回复样式 **/
  .event--message .reply {
    align-items: baseline;
    font-size: 1em;
    --avatar-size: 1em;
    /* 此处可修改回复名字的颜色 */
    color: rgb(48, 135, 191);
  }

  .event--message .reply-avatar {
    /* 如果不需要显示回复头像，使用display: none; */
    margin-right: 0.2em;
  }

  /** 头像独立显示 **/
  .event--message .meta .avatar-wrap {
    position: absolute;
    /* 修改margin-left最后的数值可适当调整头像位置 */
    margin-left: calc(var(--1px) * -56);
  }

  /* 如果不想让头像独立显示，可删除用两侧符号注解掉本项及下面的【调整行内位置】，
    并调整【气泡样式】中margin-left最后的数值 */

  /** 右侧装饰条 **/
  .event--message::before {
    content: '';
    position: absolute;
    /* 修改width最后的数值可调整装饰条宽度 */
    width: calc(var(--1px) * 8);
    background-color: var(--bg);
    right: calc(var(--1px) * 0);
    top: calc(var(--1px) * 0);
    bottom: calc(var(--1px) * 0);
    /* border-radius的数值应与弹幕气泡中的border-radius一致 */
    border-top-right-radius: calc(var(--1px) * 10);
    border-bottom-right-radius: calc(var(--1px) * 10);
  }

  /* 普通用户不显示装饰条 */
  .event--message.guard-level--0.user-type--user::before {
    display: none;
  }

  /** 房管图标颜色 **/
  .event--message .mod-badge {
    color: var(--bg);
  }

  /** 主播图标颜色 **/
  .event--message.user-type--streamer .meta svg {
    color: var(--bg);
  }

  /** 信息栏溢出后自动换行，如果不需要可删除此行 **/
  .event--message .meta {
    flex-wrap: wrap;
  }


  /*** 互动信息部分 ***/
  /** 气泡样式 **/
  .event--interaction,
  .event--like-click {
    position: relative;
    display: block;
    margin-left: calc(var(--1px) * 40);
    margin-right: calc(var(--1px) * 12);
    padding: calc(var(--1px) * 1) calc(var(--1px) * 8) calc(var(--1px) * 1) calc(var(--1px) * 8);
    background-color: rgb(245, 243, 242, 0.9);
    width: fit-content;
    border-radius: calc(var(--1px) * 6);
  }

  /** 名字样式 **/
  .event--interaction .username,
  .event--like-click .username {
    position: relative;
    display: block;
    border-radius: calc(var(--1px) * 4);
    padding: calc(var(--1px) * 2) calc(var(--1px) * 6);
    color: #fff;
    background-color: var(--bg);
    font-size: calc(var(--1px) * 14.5);
    line-height: calc(var(--1px) * 14.5);
    font-weight: 500;
  }

  /* 点赞这块有bug，只能强行添加背景了 */
  .event--like-click .username {
    background-color: rgb(102, 102, 102);
    opacity: 0.9;
  }

  /* 修复点赞位置 */
  svg.tabler-icon.tabler-icon-thumb-up-filled {
    position: absolute;
    margin-top: 0.36em;
  }

  .event--like-click .message {
    padding-right: 1em;
  }

  /** 正文样式 **/
  /* 总样式 */
  .event--interaction .message,
  .event--like-click .message {
    position: relative;
    /* 正文应用全局颜色去掉两侧标记即可 */
    /* color: #000; */
    font-size: calc(var(--1px) * 14);
  }

  /* 进入直播间 */
  .event--interaction.event-action--enter .message {
    color: #000;
  }

  /* 关注直播间 */
  .event--interaction.event-action--follow .message {
    color: rgb(225, 138, 59);
  }

  /* 分享直播间 */
  .event--interaction.event-action--share .message {
    color: rgb(48, 135, 191);
  }

  /* 特别关注直播间 */
  .event--interaction.event-action--follow-special .message {
    color: rgb(232, 121, 0);
  }

  /* 互相关注直播间 */
  .event--interaction.event-action--follow-mutual .message {
    color: rgb(237, 64, 151)
  }

  /* 点赞直播间 */
  .event--like-click .message {
    color: rgb(240, 98, 146);
  }

  /** 头像独立显示 **/
  .event--interaction .avatar-wrap,
  .event--like-click .avatar-wrap {
    position: absolute;
    margin-left: calc(var(--1px) * -38);
    filter: drop-shadow(0 0 0.5px rgb(45, 40, 103));
  }

  /** 调整行内位置（不建议修改） **/
  .event--interaction .meta,
  .event--like-click .meta {
    height: calc(var(--event-line-height) * 1);
  }


  /*** 系统信息（禁言、违规提醒、切断直播）部分 ***/
  /** 气泡样式 **/
  .event--user-block,
  .event--live-warning,
  .event--live-cutoff,
  .event--room-mute-on,
  .event--room-mute-off,
  .event--room-name-update,
  .event--notice,
  .event--live-start,
  .event--live-end {
    position: relative;
    display: block;
    margin: calc(var(--1px) * 6) calc(var(--1px) * 8) calc(var(--1px) * 6) calc(var(--1px) * 8);
    padding: calc(var(--1px) * 2) calc(var(--1px) * 12) calc(var(--1px) * 2) calc(var(--1px) * 12);
    font-size: calc(var(--1px) * 14);
    background-color: rgb(246, 249, 228, 0.3);
    width: fit-content;
    border-radius: calc(var(--1px) * 6);
  }

  /** 禁言样式 **/
  .event--user-block {
    color: rgb(255, 63, 54);
  }

  /** 超管警告样式 **/
  .event.event--live-warning {
    color: rgb(255, 209, 41);
  }

  /** 切断直播样式 **/
  .event--live-cutoff {
    color: rgb(255, 25, 18);
  }

  /** 全局禁言样式 **/
  .event--room-mute-on {
    color: rgb(255, 63, 54);
  }

  /** 取消全局禁言样式 **/
  .event--room-mute-off {
    color: rgb(245, 243, 242);
  }

  /** 直播间更新信息样式 **/
  .event--room-name-update {
    color: rgb(245, 243, 242);
  }

  /** 通用公告样式 **/
  /* 一般文本 */
  .event--notice .notice-text {
    color: rgb(245, 243, 242) !important;
  }

  /* 强调文本 */
  .event--notice .notice-highlight {
    color: rgb(250, 192, 61) !important;
  }

  /** 开播样式 **/
  .event--live-start {
    color: rgb(255, 25, 18);
  }

  /** 下播样式 **/
  .event--live-end {
    color: #000;
  }


  /*** 礼物部分 ***/
  /** 普通礼物样式 **/
  .event--gift.event-show-as--normal.event-size--normal {
    position: relative;
    display: block;
    margin-left: calc(var(--1px) * 1);
    margin-right: calc(var(--1px) * 12);
    padding: calc(var(--1px) * 4) calc(var(--1px) * 12) calc(var(--1px) * 2) calc(var(--1px) * 12);
    background-color: rgb(236, 227, 204, 1);
    --avatar-size: calc(var(--1px) * 20);
    width: fit-content;
    border-radius: calc(var(--1px) * 6);
  }

  /* 普通礼物用户名颜色匹配用户身份 */
  .event--gift.event-show-as--normal.event-size--normal .username {
    color: var(--bg);
  }

  /* 普通礼物显示匹配颜色梯度 */
  .event--gift.event-show-as--normal.event-size--normal .message {
    color: var(--bg1);
  }

  /** 高亮礼物样式 **/
  .event--gift.event-show-as--normal.event-size--highlight {
    position: relative;
    display: block;
    margin-left: calc(var(--1px) * 1);
    margin-right: calc(var(--1px) * 12);
    padding: calc(var(--1px) * 6) calc(var(--1px) * 6) calc(var(--1px) * 6) calc(var(--1px) * 10);
    font-size: calc(var(--1px) * 16);
    background-color: var(--bg1);
    width: max;
    border-radius: calc(var(--1px) * 10);
  }

  /** 守护圣殿样式 **/
  .event--mvp.event-show-as--normal {
    position: relative;
    display: block;
    margin-left: calc(var(--1px) * 1);
    margin-right: calc(var(--1px) * 12);
    padding: calc(var(--1px) * 4) calc(var(--1px) * 6) calc(var(--1px) * 4) calc(var(--1px) * 10);
    font-size: calc(var(--1px) * 16);
    width: max;
    border-radius: calc(var(--1px) * 10);
  }

  /** 调整礼物价格位置 **/
  /* 隐藏原礼物价格 */
  /* 打开此项后，价格将显示在粉丝牌后面
   打开：display: flex;
   关闭：display: none;  */
  .event--gift.event-show-as--normal.event-size--highlight .price,
  .event--mvp.event-show-as--normal .price {
    display: none;
  }

  /* 显示独立礼物价格，并调整位置 */
  /* 打开此项后，价格将显示在正文后面，礼物前面
   打开：display: block;
   关闭：display: none;  */
  .event--gift.event-show-as--normal.event-size--highlight .price-alt,
  .event--mvp.event-show-as--normal .price-alt {
    display: block;
    position: absolute;
    right: calc(var(--1px) * 60);
    bottom: calc(var(--1px) * 2);
  }

  /* 控制礼物内容显示范围，确保不会盖过价格和图片 */
  .event--gift.event-show-as--normal.event-size--highlight .message,
  .event--mvp.event-show-as--normal .message {
    margin-right: calc(var(--1px) * 70);
  }

  /* 控制礼物顶栏显示范围，确保不会盖过图片 */
  .event--gift.event-show-as--normal.event-size--highlight .top,
  .event--mvp.event-show-as--normal .top {
    margin-right: calc(var(--1px) * 20);
  }

  /** 红包&天选时刻 **/
  /* 红包样式 */
  .event--red-envelope-start.event-show-as--normal,
  .event--red-envelope-result {
    position: relative;
    display: block;
    margin-left: calc(var(--1px) * 1);
    margin-right: calc(var(--1px) * 12);
    padding: calc(var(--1px) * 6) calc(var(--1px) * 6) calc(var(--1px) * 6) calc(var(--1px) * 10);
    font-size: calc(var(--1px) * 16);
    color: #fff;
    background-color: rgb(220, 80, 80) !important;
    border-radius: calc(var(--1px) * 10);
  }

  /* 内容字体均设置为白色 */
  .event--red-envelope-start.event-show-as--normal .message,
  .event--red-envelope-result .message {
    color: #fff;
  }

  /* 调整普通红包样式 */
  .event--red-envelope-start.event-show-as--normal.event-size--normal {
    padding: calc(var(--1px) * 8) calc(var(--1px) * 12) calc(var(--1px) * 2) calc(var(--1px) * 12);
    --avatar-size: calc(var(--1px) * 20);
    width: fit-content;
    border-radius: calc(var(--1px) * 6);
  }

  /* 高亮红包口令内容 */
  .event--red-envelope-start.event-show-as--normal.event-size--highlight .red-envelope-start-details-name {
    display: block;
  }

  /* 奖品图标开关 */
  .event--red-envelope-start.event-show-as--normal .red-envelope-start-rewards {
    /* 如果不需要显示中奖礼物图标，使用display: none; */
    display: inline;
    padding-left: calc(var(--1px) * 4);
  }

  /** 调整高亮红包价格位置 **/
  /* 隐藏原礼物价格 */
  /* 打开此项后，价格将显示在粉丝牌后面
   打开：display: flex;
   关闭：display: none;  */
  .event--red-envelope-start.event-show-as--normal.event-size--highlight .price {
    display: none;
  }

  /* 显示高亮红包独立价格，并调整位置 */
  /* 打开此项后，价格将显示在正文后面，礼物前面
   打开：display: block;
   关闭：display: none;  */
  .event--red-envelope-start.event-show-as--normal.event-size--highlight .price-alt {
    display: block;
    position: absolute;
    right: calc(var(--1px) * 60);
    bottom: calc(var(--1px) * 2);
    font-weight: 700;
  }

  /* 天选时刻样式 */
  .event--lottery-start.event-show-as--normal,
  .event--lottery-result {
    position: relative;
    display: block;
    margin-left: calc(var(--1px) * 1);
    margin-right: calc(var(--1px) * 12);
    padding: calc(var(--1px) * 6) calc(var(--1px) * 6) calc(var(--1px) * 6) calc(var(--1px) * 10);
    font-size: calc(var(--1px) * 16);
    background-color: rgb(71, 39, 194) !important;
    border-radius: calc(var(--1px) * 10);
  }

  /* 强行调整红包和天选的粗体字 */
  .event--red-envelope-start.event-show-as--normal b,
  .event--lottery-start.event-show-as--normal b {
    font-weight: 500;
  }


  /*** SC 样式 ***/
  /** 总样式 **/
  .event--superchat.event-show-as--normal {
    margin-left: calc(var(--1px) * 1);
    margin-right: calc(var(--1px) * 12);
    border-radius: calc(var(--1px) * 10);
  }

  /** SC 上方样式 **/
  .event--superchat.event-show-as--normal .top {
    color: rgb(102, 102, 102);
    background-color: var(--bg2);
    padding: calc(var(--1px) * 6) calc(var(--1px) * 16) calc(var(--1px) * 4) calc(var(--1px) * 14);
    font-size: calc(var(--1px) * 16);
  }

  /** SC 下方正文样式 **/
  .event--superchat.event-show-as--normal .message {
    background-color: var(--bg1);
    font-weight: 500;
    padding: calc(var(--1px) * 4) calc(var(--1px) * 12) calc(var(--1px) * 6) calc(var(--1px) * 12);
    font-size: calc(var(--1px) * 16);
  }

  /** SC 时间（见控制台） **/
  .event--superchat .timestamp {
    color: rgb(102, 102, 102);
    font-size: calc(var(--1px) * 14.5);
  }


  /*** 舰长样式 ***/
  /** 总样式 **/
  .event--toast.event-show-as--normal {
    display: flex;
    align-items: center;
    margin-left: calc(var(--1px) * 1);
    margin-right: calc(var(--1px) * 12);
    font-size: calc(var(--1px) * 16);
    border-radius: calc(var(--1px) * 100);
    background-color: var(--bg);
    padding: calc(var(--1px) * 8) calc(var(--1px) * 10) calc(var(--1px) * 8) calc(var(--1px) * 10);
    --avatar-size: calc(var(--1px) * 40);
    /* badge-size为舰长图标大小 */
    --badge-size: calc(var(--1px) * 48);
  }

  /** 文本范围 **/
  .event--toast.event-show-as--normal .username,
  .event--toast.event-show-as--normal .price,
  .event--toast.event-show-as--normal .message {
    padding-left: calc(var(--1px) * 4);
    padding-right: calc(var(--1px) * 50);
  }

  /** 隐藏价格 **/
  /* 打开：display: block;
    关闭：display: none;  */
  .event--toast.event-show-as--normal .price {
    display: none;
  }


  /*** 礼物条样式 ***/
  /** 总样式 **/
  .event-show-as--sticky {
    font-size: calc(var(--1px) * 16);
  }

  /** 颜色方案关联（不用修改） **/
  /* SC设置 */
  .event--superchat.event-show-as--sticky .top {
    color: #fff;
    background-color: var(--bg1) !important;
    /* 当使用!important时，所有同名元素及其子元素全部覆盖应用该属性 */
  }

  /* 礼物设置 */
  .event--gift.event-show-as--sticky.event-size--highlight {
    background: var(--bg1);
  }

  /* 高亮红包设置 */
  .event--red-envelope-start.event-show-as--sticky {
    background-color: rgb(220, 80, 80);
  }

  /* 天选时刻设置 */
  .event--lottery-start.event-show-as--sticky {
    background-color: rgb(71, 39, 194);
  }

  /* 舰长设置 */
  .event--toast.event-show-as--sticky {
    background-color: var(--bg);
    /* 以下两项可自己探索，去掉后为默认样式 */
    --badge-size: calc(var(--event-line-height) * 1.25);
    background-position: center right calc(var(--event-font-size) * 0.1);
  }


  /*** 动画样式 ***/
  .event {
    animation-timing-function: cubic-bezier(0.2, 0, 0.8, 1);
    animation-fill-mode: both;
    animation-delay: 1000ms;
  }

  /** 从下往上升起 + 淡入 */
  @keyframes anim_bottom_up_fade {
    0% {
      opacity: 0;
      transform: translateY(calc(var(--1px) * 10));
    }

    40% {
      opacity: 0;
    }

    100% {
      opacity: 1;
      transform: none;
    }
  }

  /** 弹幕动画 **/
  .event--message {
    animation-timing-function: cubic-bezier(0.8, 0, 0.2, 1);
    animation: anim_bottom_up_fade 200ms;
    animation-fill-mode: both;
  }

  /** 礼物、SC、大航海动画 **/
  .event--toast,
  .event--superchat,
  .event--gift,
  .event--mvp,
  .event--red-envelope-start,
  .event--red-envelope-result,
  .event--lottery-start,
  .event--lottery-result {
    animation-timing-function: cubic-bezier(0.8, 0, 0.2, 1);
    animation: anim_bottom_up_fade 200ms;
    animation-fill-mode: both;
  }


  /*** 其他设置 ***/
  /** 弹幕表情调整 **/
  /* 以下表情大小既可以用像素固定，也可以用倍率调整比例 */
  /* 大表情（包括直播间通用表情、房间表情、粉丝团表情、自购表情） */
  .event--message .emote {
    /* max-width和max-height可自行调整，
     图片等比拉伸后长宽满足其中一个条件即可 */
    max-width: calc(var(--event-font-size) * 3);
    max-height: calc(var(--event-font-size) * 3);
  }

  /* 小表情（b站emoji） */
  .bmote-wrap img {
    width: calc(var(--event-line-height) * 0.9);
    height: auto;
    margin: 0 calc(var(--1px) * 1);
  }

  /** 粉丝牌调整 **/
  /* 整体调整 */
  .fans-medal {
    font-size: calc(var(--1px) * 13);
  }

  /* 以下调整粉丝牌文字到两边距离 */
  /* 粉丝团调整 */
  .event .fans-medal-content {
    padding-left: calc(var(--event-line-height) * 0.1);
    padding-right: calc(var(--event-line-height) * 0.1);
  }

  /* 舰长调整 */
  .event .fans-medal-has-guard .fans-medal-content {
    padding-left: calc(var(--1px)*2 + var(--event-line-height) * 0.65);
    padding-right: calc(var(--event-line-height) * 0.13);
  }

  /** 舰长图标调整 **/
  /* 整体大小调整 */
  .event .fans-medal .guard-badge,
  .guard-badge {
    width: calc(var(--1px) * 22);
    height: calc(var(--1px) * 22);
  }

  /* 粉丝牌舰长图标位置 */
  .event .fans-medal .guard-badge {
    /* 可使用top，bottom，left，right自行调整图标位置 */
    top: calc(var(--1px) * -3);
    left: calc(var(--1px) * -5);
  }

  /* 隐藏后面舰长图标，保留粉丝牌舰长标志 */
  .event--message .meta .guard-badge {
    display: none;
  }

  .event--message .fans-medal-content .guard-badge {
    display: block;
  }

  /** 房管、主播图标大小 **/
  .event--message .mod-badge,
  .event--message.user-type--streamer .meta svg {
    width: auto;
    height: calc(var(--event-font-size) * 1.35);
  }

  /** 弹幕显示时间 **/
  /* 如果不需要显示时间，使用display: none; */
  .event--message .timestamp {
    display: block;
    padding-left: calc(var(--1px) * 2);
    font-size: calc(var(--1px) * 14);
    line-height: calc(var(--1px) * 14.5);
  }

  /* 如果需要显示秒数，使用display: block; */
  .timestamp .second,
  .timestamp .minute .separator {
    display: none;
  }

  /** 高能用户设置（榜1、榜2、榜3） **/
  .current-rank {
    color: #fff;
    /* background-image用于填充渐变色，颜色和渐变角度可自行调整
      如果想填充纯色，删除此行，选择background-color属性 */
    background-image: linear-gradient(135deg, rgb(255, 78, 139), rgb(255, 148, 79));
    border-radius: calc(var(--1px) * 4);
    padding: calc(var(--1px) * 1) calc(var(--1px) * 4);
    font-size: calc(var(--1px) * 13.5);
    font-weight: 500;
    line-height: calc(var(--event-line-height) * 0.68);
  }

  /** 用户等级底色 **/
  .user-level {
    background-color: #fff;
  }

  /** 深色模式主题 **/
  /* 打开深色模式切换为主题色版样式 */
  .scheme-dark .event--message,
  .scheme-dark .event--message .reply,
  .scheme-dark .event--message .mod-badge,
  .scheme-dark .event--message.user-type--streamer .meta svg,
  .scheme-dark .event--interaction,
  .scheme-dark .event--like-click,
  .scheme-dark .event--interaction .message,
  .scheme-dark .event--like-click .message {
    color: rgb(245, 243, 242);
    background-color: var(--bg);
  }

  .scheme-dark .event--message .username,
  .scheme-dark .event--message::before,
  .scheme-dark .event--interaction .username,
  .scheme-dark .event--like-click .username {
    color: var(--bg);
    background-color: rgb(245, 243, 242);
  }

  .scheme-dark .event--like-click {
    background-color: rgb(102, 102, 102);
  }

  .scheme-dark .event--like-click .username {
    color: rgb(102, 102, 102);
  }

  /** 元素顺序调整 **/
  /* 调整弹幕中元素的顺序 */
  /* 只需要把需要的元素放前面加减顺序即可
    如果不想改变名字位置，可删除此项或order: 0; */
  /* 目前顺序为以下几个档位（优先级从上到下减弱）
   1. 头像
   2. 荣耀等级
   3. 用户名
   4. 粉丝牌
   5. 舰长标、礼物图片
   6. 房管标、主播标、高能用户
   7. 价格、时间 */
  .avatar-wrap {
    order: -3;
  }

  .wealth-medal {
    order: -1;
  }

  .username {
    order: 0;
  }

  .fans-medal {
    order: 1;
  }

  .guard-badge,
  .gift-image-wrap {
    order: 2;
  }

  .mod-badge,
  .streamer-badge,
  .current-rank {
    order: 3;
  }

  .price,
  .timestamp {
    order: 4;
  }

  /** 礼物条置底 **/
  /* 需要应用去掉两侧标记即可 */
  /* .gift-sticky-bar {
    order: 1;
  } */
}
`

export default TemplateOrange
