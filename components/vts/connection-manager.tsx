import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useVTSAutoConnect, useVTSConnectionSettings } from '@/lib/vts/storage'
import { useVTSStore } from '@/lib/vts/store'
import { checkVTSConnection } from '@/lib/vts/utils'

import OptionLabel from '@/components/OptionLabel'
import { Alert } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { InputWithLabel } from '@/components/ui/input-with-label'
import { Label } from '@/components/ui/label'

export const VTSConnectionManager: React.FC = () => {
  const { t } = useTranslation()
  const [storedSettings, setStoredSettings] = useVTSConnectionSettings()
  const [autoConnect, setAutoConnect] = useVTSAutoConnect()
  const {
    isConnected,
    isConnecting,
    connectionError,
    config,
    setConfig,
    connect,
    disconnect,
    refreshStats,
    refreshModels,
    refreshHotkeys,
    refreshExpressions,
  } = useVTSStore()

  // Default values for the form
  const [host, setHost] = useState<string>('localhost')
  const [port, setPort] = useState<number>(8001)
  const [testingConnection, setTestingConnection] = useState<boolean>(false)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)

  // Load config from store on mount
  useEffect(() => {
    if (config) {
      if (config.host) setHost(config.host)
      if (config.port) setPort(config.port)
    }
  }, [config])

  // Load saved settings from localStorage if available
  useEffect(() => {
    if (storedSettings) {
      // Update the config in the store
      setConfig(storedSettings)

      // Update the form values
      setHost(storedSettings.host)
      setPort(storedSettings.port)
    }
  }, [storedSettings, setConfig])

  // Update host in store
  const handleHostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setHost(value)
    setConfig({ host: value })
    // Clear previous test result when settings change
    setTestResult(null)
  }

  // Update port in store when valid
  const handlePortChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value)
    setPort(value)

    if (!isNaN(value) && value > 0 && value < 65536) {
      setConfig({ port: value })
      // Clear previous test result when settings change
      setTestResult(null)
    }
  }

  // Handle connect/disconnect
  const handleToggleConnection = () => {
    if (isConnected) {
      disconnect()
    } else {
      // Save current settings to localStorage before connecting
      setStoredSettings({
        host,
        port,
      })
      connect()
    }
  }

  // Refresh all data
  const handleRefresh = async () => {
    if (!isConnected) return

    await refreshStats()
    await refreshModels()
    await refreshHotkeys()
    await refreshExpressions()
  }

  // Test if VTube Studio is running and accessible
  const testConnection = async () => {
    setTestingConnection(true)
    setTestResult(null)

    try {
      if (isNaN(port)) {
        setTestResult({
          success: false,
          message: 'Invalid port number',
        })
        return
      }

      const result = await checkVTSConnection(host, port)
      setTestResult(result)
    } catch (error) {
      setTestResult({
        success: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`,
      })
    } finally {
      setTestingConnection(false)
    }
  }

  return (
    <div className='space-y-3'>
      <div className='grid gap-x-2 gap-y-3'>
        <div className='grid grid-cols-1 items-start gap-x-2 gap-y-4 @xs:grid-cols-2'>
          <div>
            <InputWithLabel
              id='vts-host'
              value={host}
              onChange={handleHostChange}
              disabled={isConnected || isConnecting}
              placeholder='localhost'
              label={
                <OptionLabel
                  label={t('vts.host.label', 'VTS IP')}
                  desc={t('vts.host.desc', 'IP address of the VTube Studio WebSocket')}
                />
              }
            />
          </div>

          <div>
            <InputWithLabel
              id='vts-port'
              value={port}
              onChange={handlePortChange}
              type='number'
              min={1}
              max={65535}
              defaultValue={8001}
              disabled={isConnected || isConnecting}
              placeholder='8001'
              label={
                <OptionLabel
                  label={t('vts.port.label', 'Port')}
                  desc={t('vts.port.desc', 'Port of the VTube Studio WebSocket')}
                />
              }
            />
          </div>
        </div>
      </div>

      <div className='flex flex-col gap-2 @sm:flex-row @sm:items-center'>
        <div className='flex flex-auto items-center space-x-1'>
          <Checkbox id='vts-auto-connect' checked={autoConnect} onChange={e => setAutoConnect(e.target.checked)} />
          <Label htmlFor='vts-auto-connect'>{t('vts.autoConnect', 'Auto Connect')}</Label>
        </div>

        <Button onClick={handleToggleConnection} disabled={isConnecting} tint={isConnected ? 'red' : 'accent'}>
          {isConnected
            ? t('vts.disconnect', 'Disconnect')
            : isConnecting
              ? t('vts.connecting', 'Connecting…')
              : t('vts.connect', 'Connect')}
        </Button>

        {/* {isConnected && (
            <Button onClick={handleRefresh} variant='link'>
              ↻
            </Button>
          )} */}

        {/* {!isConnected && (
            <Button onClick={testConnection} disabled={isConnecting || testingConnection} variant='outline'>
              {testingConnection ? 'Testing...' : 'Test Connection'}
            </Button>
          )} */}

        {/* <div>
            {isConnected ? (
              <Badge variant='dot' tint={'green'}>
                {t('vts.connected', 'Connected')}
              </Badge>
            ) : (
              <Badge variant='dot' tint={'default'}>
                {t('vts.disconnected', 'Disconnected')}
              </Badge>
            )}
          </div> */}
      </div>

      {/* Connection test results */}
      {testResult && <Alert tint={testResult.success ? 'success' : 'danger'}>{testResult.message}</Alert>}

      {connectionError && <Alert tint='danger'>{connectionError}</Alert>}
    </div>
  )
}
