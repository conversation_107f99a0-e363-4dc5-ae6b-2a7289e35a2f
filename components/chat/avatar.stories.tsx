import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'

import Avatar from '@/components/chat/avatar'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Components/Avatar',
  component: Avatar,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof Avatar>

export default meta
type Story = StoryObj<typeof meta>

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    uid: 2763,
  },
}

export const CustomSize: Story = {
  args: {
    uid: 2763,
    size: 64,
  },
}

export const NoAvatar: Story = {
  args: {
    uid: 18571231231,
  },
}

/**
 * 总督
 */
export const Guard1: Story = {
  args: {
    uid: 2763,
    guard: 1,
  },
  render: args => {
    return (
      <div className='flex gap-2'>
        <Avatar {...args} perkLevel={1} />
        <Avatar {...args} perkLevel={2} />
        <Avatar {...args} perkLevel={3} />
        <Avatar {...args} perkLevel={3} className='avatar-custom-size-1' />
        <Avatar {...args} perkLevel={3} className='avatar-custom-size-2' />
      </div>
    )
  },
}

/**
 * 提督
 */
export const Guard2: Story = {
  args: {
    uid: 2763,
    guard: 2,
  },
  render: args => {
    return (
      <div className='flex gap-2'>
        <Avatar {...args} perkLevel={1} />
        <Avatar {...args} perkLevel={2} />
        <Avatar {...args} perkLevel={3} />
        <Avatar {...args} perkLevel={3} className='avatar-custom-size-1' />
        <Avatar {...args} perkLevel={3} className='avatar-custom-size-2' />
      </div>
    )
  },
}

/**
 * 舰长
 */
export const Guard3: Story = {
  args: {
    uid: 2763,
    guard: 3,
  },
  render: args => {
    return (
      <div className='flex gap-2'>
        <Avatar {...args} perkLevel={1} />
        <Avatar {...args} perkLevel={2} />
        <Avatar {...args} perkLevel={3} />
        <Avatar {...args} perkLevel={3} className='avatar-custom-size-1' />
        <Avatar {...args} perkLevel={3} className='avatar-custom-size-2' />
      </div>
    )
  },
}

/**
 * 舰队指挥官
 */
export const GuardLeader: Story = {
  args: {
    uid: 2763,
    guardLeader: true,
  },
}

/**
 * 头像框
 */
export const AvatarFrame: Story = {
  args: {
    uid: 2132180406,
    frame: 'https://i0.hdslb.com/bfs/garb/item/5438986ddca5cc9e5171ce7baeee0e5e9c62e863.png',
    // frame: 'https://i0.hdslb.com/bfs/vc/071eb10548fe9bc482ff69331983d94192ce9507.png',
  },
  render: args => {
    return (
      <div className='flex gap-2'>
        <Avatar {...args} />
        <Avatar {...args} className='avatar-custom-size-1 [--avatar-size:48px]' />
        <Avatar {...args} className='avatar-custom-size-2 [--avatar-size:120px]' />
      </div>
    )
  },
}

/**
 * 通过房间号获取头像
 */
export const RoomNumberAsUid: Story = {
  args: {
    uid: 25034104,
    isRoom: true,
  },
}
