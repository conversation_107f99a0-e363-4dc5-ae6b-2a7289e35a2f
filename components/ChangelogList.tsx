import { ChangelogItems } from '@/data/changelog'

import { formatDate } from '@/utils/formatDate'

import { Alert } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Divider } from '@/components/ui/divider'

export function ChangelogList() {
  return (
    <div className='grid gap-4'>
      {ChangelogItems.map((item, idx) => {
        const isBetaRelease = idx === 0 && item.pending
        let isLatestRelease = idx === 0 ? !item.pending : false
        if (idx === 1 && ChangelogItems[0]?.pending) isLatestRelease = true

        return (
          <div key={item.id}>
            <div className='mb-4 flex items-center gap-2'>
              <Divider
                className='flex-auto'
                label={
                  <div className='flex flex-wrap items-center gap-2'>
                    {formatDate(new Date(item.timestamp), {
                      format: {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      },
                    })}
                    {isBetaRelease && (
                      <Badge variant='solid' tint={'orange'} size='sm' radius='md'>
                        Beta
                      </Badge>
                    )}
                    {isLatestRelease && (
                      <Badge variant='solid' tint={'blue'} size='sm' radius='md'>
                        Latest Stable
                      </Badge>
                    )}
                  </div>
                }
              />
              <span className='text-fg/60 text-xs'>#{item.id}</span>
            </div>

            {isBetaRelease && (
              <Alert tint='warning' className='mb-4' label={'以下改动仅限于开发节点'}>
                下列功能目前仅在测试服务器中可用，并非最终改动，可能尚未推送上线，可从介绍 - FAQ
                中获取开发节点进行测试和体验
              </Alert>
            )}
            {item.content}
          </div>
        )
      })}

      <div>
        <hr className='my-3' />
        完整更新历史请访问{' '}
        <a href='https://subspace.institute/docs/laplace-chat/changelog' target='_blank'>
          Subspace Institute 亚空间研究所
        </a>
      </div>
    </div>
  )
}
