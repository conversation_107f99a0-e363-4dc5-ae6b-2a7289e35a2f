import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import { IconCircleX, IconDatabase, IconUpload } from '@tabler/icons-react'

import { cn } from '@/lib/cn'

import { Button } from '@/components/ui/button'

interface DropzoneProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  /** Custom class name */
  className?: string
  /** Custom styles */
  style?: React.CSSProperties
  /** Children elements */
  children?: React.ReactNode
  /** Custom drag active content */
  dragActiveContent?: React.ReactNode
  /** Hide file list */
  hideFileList?: boolean
  /** Maximum number of files allowed */
  maxFiles?: number
}

export const Dropzone = ({
  accept,
  multiple = false,
  className,
  style,
  children,
  dragActiveContent,
  maxFiles,
  onChange,
  disabled = false,
  hideFileList = false,
  value,
  ...props
}: DropzoneProps) => {
  const [isDragActive, setIsDragActive] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const dropzoneRef = useRef<HTMLDivElement>(null)

  const { t } = useTranslation()

  // Helper to check if adding new files would exceed limit - wrapped in useCallback
  const limiter = useCallback(
    (newFilesCount: number) => {
      if (!maxFiles) return false
      const currentCount = selectedFiles?.length || 0
      return currentCount + newFilesCount > maxFiles
    },
    [maxFiles, selectedFiles]
  )

  // Helper to validate file extensions - wrapped in useCallback
  const isValidFileType = useCallback(
    (file: File): boolean => {
      if (!accept) return true

      // Convert accept string to array of valid extensions
      const validExtensions = accept.split(',').map(ext => ext.trim().toLowerCase().replace('*', ''))

      const fileName = file.name.toLowerCase()
      return validExtensions.some(ext => fileName.endsWith(ext))
    },
    [accept]
  )

  // Helper to process and validate files - wrapped in useCallback
  const processFiles = useCallback(
    (files: File[]): File[] => {
      // If multiple is not allowed, only take the first file
      if (!multiple && files.length > 1) {
        files = [files[0]!]
      }

      const validFiles: File[] = []
      const invalidFiles: string[] = []

      files.forEach(file => {
        if (isValidFileType(file)) {
          validFiles.push(file)
        } else {
          invalidFiles.push(file.name)
        }
      })

      if (invalidFiles.length > 0) {
        toast.error(
          t('Invalid file type: {{invalidFiles}}. Allowed: {{accept}}', {
            invalidFiles: invalidFiles.join(', '),
            accept,
          })
        )
      }

      return validFiles
    },
    [isValidFileType, t, accept, multiple]
  )

  // Handle drag events
  const handleDrag = useCallback(
    (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()

      if (disabled) return

      if (e.type === 'dragenter' || e.type === 'dragover') {
        setIsDragActive(true)
      } else if (e.type === 'dragleave' || e.type === 'drop') {
        setIsDragActive(false)
      }
    },
    [disabled]
  )

  // Handle dropped files
  const handleDrop = useCallback(
    (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()

      if (disabled || !onChange || !inputRef.current) return

      const droppedFiles = Array.from(e.dataTransfer?.files || [])
      const validFiles = processFiles(droppedFiles)

      if (validFiles.length === 0) return

      // Check file limit
      if (maxFiles && limiter(validFiles.length)) {
        toast.error(t('Maximum {{maxFiles}} files allowed', { maxFiles }))
        return
      }

      setIsDragActive(false)

      // Create a DataTransfer to combine existing and new files
      const dataTransfer = new DataTransfer()

      // Add existing files if multiple is allowed
      if (multiple && selectedFiles) {
        Array.from(selectedFiles).forEach(file => {
          dataTransfer.items.add(file)
        })
      }

      // Add new valid files
      validFiles.forEach(file => {
        dataTransfer.items.add(file)
      })

      // Update input and state
      inputRef.current.files = dataTransfer.files
      setSelectedFiles(dataTransfer.files)

      // Create and dispatch synthetic change event
      const event = new Event('change', { bubbles: true })
      Object.defineProperty(event, 'target', { value: inputRef.current })
      onChange(event as unknown as React.ChangeEvent<HTMLInputElement>)
    },
    [disabled, onChange, selectedFiles, maxFiles, processFiles, limiter, t, multiple]
  )

  // Update change handler - wrapped in useCallback
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!inputRef.current) return

      const newFiles = Array.from(e.target.files || [])
      const validFiles = processFiles(newFiles)

      if (validFiles.length === 0) return

      // Check file limit
      if (maxFiles && limiter(validFiles.length)) {
        toast.error(t('Maximum {{maxFiles}} files allowed', { maxFiles }))
        return
      }

      const dataTransfer = new DataTransfer()

      // Add existing files if multiple is allowed
      if (multiple && selectedFiles) {
        Array.from(selectedFiles).forEach(file => {
          dataTransfer.items.add(file)
        })
      }

      // Add new valid files
      validFiles.forEach(file => {
        dataTransfer.items.add(file)
      })

      // Update input and state
      inputRef.current.files = dataTransfer.files
      setSelectedFiles(dataTransfer.files)

      if (onChange) {
        // Create a new event with the combined files
        const newEvent = new Event('change', { bubbles: true }) as unknown as React.ChangeEvent<HTMLInputElement>
        Object.defineProperty(newEvent, 'target', {
          writable: false,
          value: { ...e.target, files: dataTransfer.files },
        })
        onChange(newEvent)
      }
    },
    [processFiles, limiter, maxFiles, onChange, selectedFiles, t, multiple]
  )

  // Handle the remove file handler to also maintain proper state - wrapped in useCallback
  const handleRemoveFile = useCallback(
    (index: number) => {
      if (!selectedFiles || !inputRef.current || !onChange) return

      const dataTransfer = new DataTransfer()
      Array.from(selectedFiles).forEach((file, i) => {
        if (i !== index) {
          dataTransfer.items.add(file)
        }
      })

      inputRef.current.files = dataTransfer.files
      setSelectedFiles(dataTransfer.files)

      // Create and dispatch synthetic change event with updated files
      const event = new Event('change', { bubbles: true }) as unknown as React.ChangeEvent<HTMLInputElement>
      Object.defineProperty(event, 'target', {
        writable: false,
        value: { ...inputRef.current, files: dataTransfer.files },
      })
      onChange(event)
    },
    [selectedFiles, onChange]
  )

  // Trigger file dialog when clicking the dropzone
  const openFileDialog = () => {
    if (!disabled && inputRef.current) {
      inputRef.current.click()
    }
  }

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
  }

  // Add event listeners
  useEffect(() => {
    const dropzone = dropzoneRef.current
    if (!dropzone) return

    dropzone.addEventListener('dragenter', handleDrag)
    dropzone.addEventListener('dragleave', handleDrag)
    dropzone.addEventListener('dragover', handleDrag)
    dropzone.addEventListener('drop', handleDrop)

    return () => {
      dropzone.removeEventListener('dragenter', handleDrag)
      dropzone.removeEventListener('dragleave', handleDrag)
      dropzone.removeEventListener('dragover', handleDrag)
      dropzone.removeEventListener('drop', handleDrop)
    }
  }, [handleDrag, handleDrop])

  return (
    <div className='w-full space-y-2'>
      <div
        ref={dropzoneRef}
        className={cn(
          'group/dropzone relative flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-3 text-center transition-all',
          'hover:border-ac hover:bg-ac/5',
          isDragActive && 'border-ac bg-ac/10',
          disabled && 'cursor-not-allowed opacity-50',
          className
        )}
        style={style}
        onClick={openFileDialog}
      >
        <input
          ref={inputRef}
          type='file'
          accept={accept}
          multiple={multiple}
          className='hidden'
          onChange={handleChange}
          disabled={disabled}
          {...props}
        />

        {isDragActive && dragActiveContent ? (
          dragActiveContent
        ) : (
          <>
            {children || (
              <>
                <IconUpload className='group-hover/dropzone:text-ac size-8 transition-colors' />
                <div className='text-base'>
                  <span className='text-ac'>{t('Click to upload')}</span> {t('or drag and drop')}
                </div>
                {accept && (
                  <div className='text-fg/60 text-sm'>
                    {t('Allowed types: ')}
                    {accept}
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>

      {/* File List */}
      {!hideFileList && selectedFiles && selectedFiles.length > 0 && (
        <div className='space-y-2'>
          {Array.from(selectedFiles).map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className='bg-bg/40 flex items-center justify-between rounded-lg border px-3 py-2 text-sm'
            >
              <div className='flex items-center gap-2'>
                <IconDatabase className='text-fg/60 size-4 shrink-0' />
                <div className='min-w-0 flex-1'>
                  <div className='truncate font-medium'>{file.name}</div>
                  <div className='text-fg/60 text-xs'>{formatFileSize(file.size)}</div>
                </div>
              </div>
              <Button
                variant='link'
                size='sm'
                className='p-0'
                onClick={e => {
                  e.stopPropagation()
                  handleRemoveFile(index)
                }}
                aria-label={t('Remove file')}
              >
                <IconCircleX className='text-fg/60 size-4 hover:text-rose-500' />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default Dropzone
