// https://www.svgviewer.dev/svg-to-react-jsx

import * as React from 'react'

function IconUp({ className, ...props }: React.ComponentProps<'svg'>) {
  // const id = React.useId()

  return (
    <svg fill='none' viewBox='0 0 500 500' xmlns='http://www.w3.org/2000/svg' className={className} {...props}>
      <path
        d='m407.967 189.5c3.8-8.8 2-19-4.6-26l-136-144.0004c-4.5-4.8-10.8-7.4997-17.4-7.4997s-12.9 2.6997-17.4 7.4997l-135.9997 144.0004c-6.6 7-8.4 17.2-4.6 26 3.7999 8.8 12.4997 14.5 21.9997 14.5h76s21.613 136.946-93.3997 259.453c-13.5672 14.452-.9293 30.349 17.3997 21.497 118.033-57.003 196-150.432 196-280.95h76c9.6 0 18.2-5.7 22-14.5z'
        fill='currentColor'
      />
    </svg>
  )
}

function IconDown({ className, ...props }: React.ComponentProps<'svg'>) {
  // const id = React.useId()

  return (
    <svg fill='none' viewBox='0 0 500 500' xmlns='http://www.w3.org/2000/svg' className={className} {...props}>
      <path
        d='m407.967 310.001c3.8 8.8 2 19-4.6 26l-136 144c-4.5 4.8-10.8 7.5-17.4 7.5s-12.9-2.7-17.4-7.5l-135.9998-144c-6.6-7-8.4-17.2-4.6-26s12.4998-14.5 21.9998-14.5h76s21.613-136.946-93.3997-259.4533c-13.5672-14.4513-.9293-30.34887 17.3997-21.4969 118.033 57.0029 196 150.4322 196 280.9502h76c9.6 0 18.2 5.7 22 14.5z'
        fill='currentColor'
      />
    </svg>
  )
}

function IconLogo({ className, ...props }: React.ComponentProps<'svg'>) {
  return (
    <svg fill='none' viewBox='0 0 1280 1280' xmlns='http://www.w3.org/2000/svg' className={className} {...props}>
      <linearGradient id='a'>
        <stop offset={0} stopColor='var(--color-bg)' />
        <stop offset={0.447917} stopColor='var(--color-bg)' />
        <stop offset={1} stopColor='color-mix(in oklch, var(--color-bg) 80%, transparent)' />
      </linearGradient>
      <linearGradient
        id='b'
        gradientUnits='userSpaceOnUse'
        x1={640.163}
        x2={640.163}
        xlinkHref='#a'
        y1={206.286}
        y2={621.836}
      />
      <linearGradient id='c'>
        <stop offset={0} stopColor='#ffaf75' />
        <stop offset={1} stopColor='#ffaf75' stopOpacity={0} />
      </linearGradient>
      <linearGradient
        id='d'
        gradientUnits='userSpaceOnUse'
        x1={640.162}
        x2={640.162}
        xlinkHref='#c'
        y1={434.804}
        y2={621.712}
      />
      <linearGradient id='e'>
        {/* <stop offset={0} stopColor='#ffd8e3' />
        <stop offset={0.510417} stopColor='#ffd8e3' />
        <stop offset={1} stopColor='#ffd8e3' stopOpacity={0.2} /> */}
        <stop offset={0} stopColor='var(--color-bg)' />
        <stop offset={0.447917} stopColor='color-mix(in oklch, var(--color-bg) 70%, transparent)' />
        <stop offset={1} stopColor='color-mix(in oklch, var(--color-bg) 20%, transparent)' />
      </linearGradient>
      <linearGradient
        id='f'
        gradientUnits='userSpaceOnUse'
        x1={1015.77}
        x2={655.895}
        xlinkHref='#e'
        y1={423.956}
        y2={631.731}
      />
      <linearGradient
        id='g'
        gradientUnits='userSpaceOnUse'
        x1={1016.09}
        x2={656.214}
        xlinkHref='#a'
        y1={857.72}
        y2={649.945}
      />
      <linearGradient
        id='h'
        gradientUnits='userSpaceOnUse'
        x1={818.19}
        x2={656.323}
        xlinkHref='#c'
        y1={743.46}
        y2={650.006}
      />
      <linearGradient
        id='i'
        gradientUnits='userSpaceOnUse'
        x1={640.099}
        x2={640.099}
        xlinkHref='#e'
        y1={1073.78}
        y2={658.233}
      />
      <linearGradient
        id='j'
        gradientUnits='userSpaceOnUse'
        x1={264.233}
        x2={624.109}
        xlinkHref='#a'
        y1={857.28}
        y2={649.506}
      />
      <linearGradient
        id='k'
        gradientUnits='userSpaceOnUse'
        x1={462.135}
        x2={624.002}
        xlinkHref='#c'
        y1={743.021}
        y2={649.567}
      />
      <linearGradient
        id='l'
        gradientUnits='userSpaceOnUse'
        x1={264.551}
        x2={624.428}
        xlinkHref='#e'
        y1={423.219}
        y2={630.994}
      />
      <path
        d='m435.2 118.241c74.609-43.0755 111.914-64.6134 151.575-73.0435 35.091-7.4589 71.359-7.4589 106.45 0 39.661 8.4301 76.966 29.968 151.575 73.0435l144.656 83.518c74.614 43.075 111.914 64.613 139.044 94.745 24.01 26.661 42.14 58.07 53.23 92.189 12.53 38.562 12.53 81.638 12.53 167.79v167.034c0 86.152 0 129.228-12.53 167.79-11.09 34.119-29.22 65.528-53.23 92.189-27.13 30.134-64.43 51.674-139.044 94.744l-144.656 83.52c-74.609 43.07-111.914 64.61-151.575 73.04-35.091 7.46-71.359 7.46-106.45 0-39.661-8.43-76.966-29.97-151.575-73.04l-144.656-83.52c-74.61-43.07-111.915-64.61-139.045-94.744-24.006-26.661-42.14-58.07-53.2258-92.189-12.5295-38.562-12.5295-81.638-12.5295-167.79v-167.034c0-86.152 0-129.228 12.5295-167.79 11.0858-34.119 29.2198-65.528 53.2258-92.189 27.13-30.132 64.435-51.67 139.045-94.745z'
        // fill='#fb7299'
        className={'fill-ac'}
      />
      <g clipRule='evenodd' fillRule='evenodd'>
        <path
          d='m640.163 621.836c52.267-53.654 84.464-126.954 84.464-207.775s-32.197-154.121-84.464-207.775c-52.268 53.654-84.465 126.954-84.465 207.775s32.197 154.121 84.465 207.775z'
          fill='url(#b)'
        />
        <path
          d='m640.162 621.712c-23.509-24.133-37.991-57.102-37.991-93.454s14.482-69.321 37.991-93.454c23.509 24.133 37.99 57.102 37.99 93.454s-14.481 69.321-37.99 93.454z'
          fill='url(#d)'
        />
        <path
          d='m655.895 631.731c20.332-72.091 67.714-136.625 137.707-177.035 69.992-40.411 149.571-49.178 222.168-30.74-20.33 72.092-67.711 136.626-137.704 177.036-69.993 40.411-149.571 49.177-222.171 30.739z'
          fill='url(#f)'
        />
        <path
          d='m656.215 649.945c20.331 72.092 67.713 136.625 137.706 177.036 69.992 40.41 149.571 49.177 222.169 30.739-20.331-72.092-67.712-136.625-137.705-177.036-69.993-40.41-149.571-49.177-222.17-30.739z'
          fill='url(#g)'
        />
        <path
          d='m656.323 650.006c32.654-8.293 68.447-4.349 99.928 13.827 31.482 18.175 52.794 47.202 61.939 79.627-32.654 8.293-68.447 4.35-99.929-13.826s-52.793-47.202-61.938-79.628z'
          fill='url(#h)'
        />
        <path
          d='m640.099 658.233c52.267 53.654 84.465 126.955 84.464 207.775 0 80.821-32.197 154.122-84.464 207.772-52.267-53.65-84.464-126.951-84.464-207.772 0-80.82 32.197-154.121 84.464-207.775z'
          fill='url(#i)'
        />
        <path
          d='m624.109 649.506c-20.331 72.091-67.713 136.625-137.706 177.035-69.993 40.411-149.571 49.177-222.17 30.74 20.332-72.092 67.713-136.626 137.706-177.036s149.571-49.177 222.17-30.739z'
          fill='url(#j)'
        />
        <path
          d='m624.002 649.567c-9.145 32.426-30.457 61.452-61.938 79.628-31.482 18.176-67.275 22.119-99.929 13.826 9.145-32.426 30.457-61.452 61.938-79.628 31.482-18.176 67.275-22.119 99.929-13.826z'
          fill='url(#k)'
        />
        <path
          d='m624.428 630.994c-20.332-72.091-67.714-136.625-137.706-177.035-69.993-40.41-149.571-49.177-222.171-30.739 20.332 72.091 67.714 136.625 137.707 177.035 69.992 40.41 149.571 49.177 222.17 30.739z'
          fill='url(#l)'
        />
      </g>
    </svg>
  )
}

function IconBrandFishAudio({ className, ...props }: React.ComponentProps<'svg'>) {
  // const id = React.useId()

  return (
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='61.1 180.15 377.8 139.718' className={className} {...props}>
      <path
        d='M431.911 245.181c3.842 0 6.989 1.952 6.989 4.337v14.776c0 2.385-3.147 4.337-6.989 4.337-3.846 0-6.99-1.952-6.99-4.337v-14.776c0-2.385 3.144-4.337 6.99-4.337ZM404.135 250.955c3.846 0 6.989 1.952 6.989 4.337v32.528c0 2.385-3.143 4.337-6.989 4.337-3.842 0-6.989-1.952-6.989-4.337v-32.528c0-2.385 3.147-4.337 6.989-4.337ZM376.363 257.688c3.842 0 6.989 1.952 6.989 4.337v36.562c0 2.385-3.147 4.337-6.989 4.337-3.846 0-6.993-1.952-6.993-4.337v-36.562c0-2.386 3.147-4.337 6.993-4.337ZM348.587 263.26c3.846 0 6.989 1.952 6.989 4.337v36.159c0 2.385-3.143 4.337-6.989 4.337-3.842 0-6.989-1.952-6.989-4.337v-36.159c0-2.385 3.147-4.337 6.989-4.337ZM320.811 268.177c3.846 0 6.989 1.952 6.989 4.337v31.318c0 2.385-3.143 4.337-6.989 4.337-3.842 0-6.989-1.952-6.989-4.337v-31.318c0-2.385 3.147-4.337 6.989-4.337ZM293.179 288.148c3.846 0 6.989 1.952 6.989 4.337v9.935c0 2.384-3.147 4.336-6.989 4.336s-6.99-1.951-6.99-4.336v-9.935c0-2.386 3.144-4.337 6.99-4.337Z'
        style={{
          fill: 'currentColor',
          opacity: 0.5,
          fillRule: 'evenodd',
        }}
      />
      <path
        d='M431.911 205.441c3.842 0 6.989 1.952 6.989 4.337v24.459c0 2.385-3.147 4.337-6.989 4.337-3.846 0-6.99-1.952-6.99-4.337v-24.459c0-2.385 3.144-4.337 6.99-4.337ZM404.135 189.026c3.846 0 6.989 1.952 6.989 4.337v43.622c0 2.385-3.143 4.337-6.989 4.337-3.842 0-6.989-1.951-6.989-4.337v-43.622c0-2.385 3.147-4.337 6.989-4.337ZM376.363 182.848c3.842 0 6.989 1.953 6.989 4.337v56.937c0 2.384-3.147 4.337-6.989 4.337-3.846 0-6.993-1.952-6.993-4.337v-56.937c0-2.385 3.147-4.337 6.993-4.337ZM348.587 180.15c3.846 0 6.989 1.952 6.989 4.337v66.619c0 2.385-3.143 4.337-6.989 4.337-3.842 0-6.989-1.952-6.989-4.337v-66.619c0-2.385 3.147-4.337 6.989-4.337ZM320.811 181.84c3.846 0 6.989 1.952 6.989 4.337v67.627c0 2.385-3.143 4.337-6.989 4.337-3.842 0-6.989-1.951-6.989-4.337v-67.627c0-2.386 3.147-4.337 6.989-4.337ZM293.179 186.076c3.846 0 6.989 1.952 6.989 4.337v84.37c0 2.385-3.147 4.337-6.989 4.337s-6.99-1.951-6.99-4.337v-84.37c0-2.386 3.144-4.337 6.99-4.337ZM264.829 193.262c3.846 0 6.989 1.953 6.989 4.337v95.667c0 2.385-3.143 4.337-6.989 4.337-3.843 0-6.99-1.951-6.99-4.337v-95.667c0-2.385 3.147-4.337 6.99-4.337ZM237.057 205.441c3.842 0 6.989 1.953 6.989 4.337v92.036c0 2.385-3.147 4.337-6.989 4.337-3.846 0-6.99-1.951-6.99-4.337v-92.036c0-2.385 3.144-4.337 6.99-4.337ZM209.281 221.302c3.846 0 6.989 1.952 6.989 4.337v80.134c0 2.385-3.147 4.337-6.989 4.337s-6.99-1.952-6.99-4.337v-80.134c0-2.386 3.144-4.337 6.99-4.337ZM181.505 232.271c3.846 0 6.993 1.952 6.993 4.336v78.924c0 2.385-3.147 4.337-6.993 4.337-3.842 0-6.989-1.951-6.989-4.337v-78.924c0-2.385 3.147-4.336 6.989-4.336ZM153.873 241.348c3.846 0 6.989 1.953 6.989 4.337v42.009c0 2.384-3.147 4.337-6.989 4.337-3.843 0-6.99-1.952-6.99-4.337v-42.009c0-2.385 3.147-4.337 6.99-4.337ZM125.266 200.398c3.842 0 6.989 1.953 6.989 4.337v58.55c0 2.384-3.147 4.337-6.989 4.337-3.843 0-6.99-1.951-6.99-4.337v-58.55c0-2.385 3.144-4.337 6.99-4.337ZM96.7 204.231c3.842 0 6.989 1.953 6.989 4.337v18.004c0 2.384-3.147 4.337-6.989 4.337s-6.989-1.952-6.989-4.337v-18.004c0-2.385 3.143-4.337 6.989-4.337ZM68.089 201.81c3.846 0 6.99 1.953 6.99 4.337v8.12c0 2.384-3.147 4.336-6.99 4.336-3.842 0-6.989-1.951-6.989-4.336v-8.12c0-2.385 3.143-4.337 6.989-4.337ZM153.873 194.94c3.846 0 6.989 1.953 6.989 4.337v6.102c0 2.384-3.147 4.337-6.989 4.337-3.843 0-6.99-1.952-6.99-4.337v-6.102c0-2.385 3.147-4.337 6.99-4.337Z'
        style={{
          fill: 'currentColor',
          fillRule: 'evenodd',
        }}
      />
    </svg>
  )
}

function IconElevenLabs({ className, ...props }: React.ComponentProps<'svg'>) {
  // const id = React.useId()

  return (
    <svg viewBox='0 0 400 400' fill='none' xmlns='http://www.w3.org/2000/svg' className={className} {...props}>
      <path d='M230 54H290V346H230V54Z' fill='currentColor' />
      <path d='M110 54H170V346H110V54Z' fill='currentColor' />
    </svg>
  )
}

function IconStepFun({ className, ...props }: React.ComponentProps<'svg'>) {
  // const id = React.useId()

  return (
    <svg viewBox='0 0 26 26' fill='none' xmlns='http://www.w3.org/2000/svg' className={className} {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M23.8239 0.324219H24.8438V1.34032H23.8239V0.324219ZM23.8239 1.34032H24.8438V2.35657H23.8244H22.8044V1.34048L23.8239 1.34032ZM3.15586 13.6565V2.35653H4.19067V13.6565H3.15586ZM14.2185 14.3792H25.8186V15.3871H19.2537V25.6743H14.2185V14.3792ZM6.35307 3.91995L6.34978 17.2161H0.398438V22.1032H11.426V8.78247H22.5177V3.91995H6.35307ZM23.8239 3.37411V2.35817H24.8438V3.37411V4.39021H23.8239V3.37411ZM25.8658 1.34048H24.8458V2.35657H25.8658V1.34048ZM22.8037 1.34048H21.7837V2.35657H22.8037V1.34048Z'
        fill='url(#laplace-chat-icon-stepfun)'
      />
      <defs>
        <linearGradient
          id='laplace-chat-icon-stepfun'
          x1={5.68433e-8}
          y1={-5.55007e-8}
          x2={26}
          y2={26}
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='var(--color-sky-500, #0F95F5)' />
          <stop offset={1} stopColor='var(--color-blue-500, #0267FD)' />
        </linearGradient>
      </defs>
    </svg>
  )
}

function IconAliyun({ className, ...props }: React.ComponentProps<'svg'>) {
  // const id = React.useId()

  return (
    <svg viewBox='0 0 550 550' fill='none' xmlns='http://www.w3.org/2000/svg' className={className} {...props}>
      <path d='M187.35 253.29H362.7V293.37H187.35V253.29Z' fill='var(--color-orange-500)' />
      <path
        d='M449.54 109.67H334.31L362.7 149.75L446.2 176.47C461.23 181.48 471.25 196.51 471.25 211.54V338.46C471.25 353.49 461.23 368.52 446.2 373.53L362.7 400.25L334.31 440.33H449.54C497.97 440.33 536.38 401.92 536.38 353.49V198.18C536.38 149.75 497.97 109.67 449.54 109.67ZM102.18 373.53C87.15 368.52 77.13 353.49 77.13 338.46V211.54C77.13 196.51 87.15 181.48 102.18 176.47L185.68 149.75L214.07 109.67H98.84C50.41 109.67 12 149.75 12 198.18V351.82C12 400.25 50.41 438.66 98.84 438.66H214.07L185.68 398.58L102.18 373.53Z'
        fill='var(--color-orange-500)'
      />
    </svg>
  )
}

function IconCartesia({ className, ...props }: React.ComponentProps<'svg'>) {
  // const id = React.useId()

  return (
    <svg
      fill='none'
      height={164}
      viewBox='0 0 164 164'
      width={164}
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      className={className}
      {...props}
    >
      <clipPath id='laplace-chat-icon-cartesia'>
        <path d='m0 0h164v164h-164z' />
      </clipPath>
      <g clipPath='url(#laplace-chat-icon-cartesia)' fill='currentColor'>
        <path d='m81.9993 0h-27.3333v27.3333h27.3333z' />
        <path d='m136.665 0h-27.333v27.3333h27.333z' />
        <path d='m54.6673 27.334h-27.3333v27.3333h27.3333z' />
        <path d='m109.333 27.334h-27.333v27.3333h27.333z' />
        <path d='m164.001 27.334h-27.333v27.3333h27.333z' />
        <path d='m27.3333 54.666h-27.3333v27.3333h27.3333z' />
        <path d='m81.9993 54.666h-27.3333v27.3333h27.3333z' />
        <path d='m27.3333 82h-27.3333v27.333h27.3333z' />
        <path d='m81.9993 82h-27.3333v27.333h27.3333z' />
        <path d='m54.6673 109.332h-27.3333v27.333h27.3333z' />
        <path d='m109.333 109.332h-27.333v27.333h27.333z' />
        <path d='m164.001 109.332h-27.333v27.333h27.333z' />
        <path d='m81.9993 136.668h-27.3333v27.333h27.3333z' />
        <path d='m136.665 136.668h-27.333v27.333h27.333z' />
      </g>
    </svg>
  )
}

export { IconUp, IconDown, IconLogo, IconBrandFishAudio, IconElevenLabs, IconStepFun, IconAliyun, IconCartesia }
