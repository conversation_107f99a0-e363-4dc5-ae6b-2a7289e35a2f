import { useEffect, useState } from 'react'
import { toast } from 'sonner'
// TODO: find types
// @ts-expect-error need lib fix
import DomInspector from '@sparanoid/dom-inspector'
import { IconClick } from '@tabler/icons-react'

import { useCopyToClipboard } from '@/hooks/use-copy-to-clipboard'
import { useHotkeys } from '@/hooks/use-hotkeys'

import { Button } from '@/components/ui/button'

export function Inspector() {
  const [domInsp, setDomInsp] = useState()
  const [domInspActive, setDomInspActive] = useState(false)
  const { copyToClipboard } = useCopyToClipboard({ timeout: 2000 })

  useHotkeys([
    [
      'Escape',
      () => {
        if (domInspActive) {
          setDomInspActive(false)
          if (domInsp) {
            // @ts-expect-error need lib fix
            domInsp.destroy()
          }
          console.log('Inspector destroied')
        }
      },
    ],
  ])

  useEffect(() => {
    if (domInsp) {
      // @ts-expect-error need lib fix
      domInsp.enable()
    }
  }, [domInsp])

  const toggleInspector = () => {
    if (domInspActive) {
      setDomInspActive(false)
      if (domInsp) {
        // @ts-expect-error need lib fix
        domInsp.destroy()
      }
    } else {
      setDomInspActive(true)
      setDomInsp(
        typeof window !== 'undefined' &&
          new DomInspector({
            root: '.in-obs',
            transformInput: (classname: string) => {
              console.log(`classname`, classname)

              const regex = /^[a-zA-Z0-9_-]+_{2,}[a-zA-Z0-9_-]+$/
              return classname.replace(regex, '')
            },
            onClick: (classes: string) => {
              console.log('Clicked element classes:', classes)
              copyToClipboard(classes)
              toast.success(`CSS 类已复制到剪切板`, {
                id: `copy-audio-${classes}`,
                description: '请注意，直接复制的类可能并非最终优化的类，请根据实际需求调整',
              })
              // setDomInspActive(false)
              // domInsp && domInsp.destroy()
            },
          })
      )
    }
  }

  return (
    <Button
      onClick={toggleInspector}
      className='p-1'
      tint={domInspActive ? 'accent' : 'default'}
      variant={domInspActive ? 'solid' : 'link'}
      aria-label={domInspActive ? '再次点击或按 esc 取消审查元素' : '审查元素'}
    >
      <IconClick size='1.125rem' />
    </Button>
  )
}
