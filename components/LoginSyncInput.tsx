'use client'

import { memo, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import type { BilibiliInternal } from '@laplace.live/internal'
import {
  IconExclamationCircle,
  IconEye,
  IconEyeOff,
  IconPasswordUser,
  IconRefresh,
  IconRefresh<PERSON><PERSON>t,
  IconRefreshOff,
} from '@tabler/icons-react'

import { translateManager } from '@/utils/translateManager'
import { ttsManager } from '@/utils/ttsManager'

import { useDebouncedValue } from '@/hooks/useDebouncedValue'
import { useLocalStorage } from '@/hooks/useLocalStorage'
import useLoginSyncDryrun from '@/hooks/useLoginSyncDryrun'

import Avatar from '@/components/chat/avatar'
import OptionLabel from '@/components/OptionLabel'
import { Button } from '@/components/ui/button'
import { InputWithLabel } from '@/components/ui/input-with-label'
import { Loading } from '@/components/ui/loading'

interface LoginSyncIndicatorProps {
  data: BilibiliInternal.HTTPS.Prod.Nav | undefined
  loading?: boolean
  disabled?: boolean
  error?: boolean
}

export const LoginSyncIndicator = memo(function LoginSyncIndicator({
  data,
  loading,
  disabled,
  error,
}: LoginSyncIndicatorProps) {
  let label: React.ReactNode = ''
  let icon: React.ReactNode = <IconPasswordUser size='1.25rem' />
  // console.log('data', data)

  if (data) {
    if (data.code === 0) {
      label = `同步密钥验证成功：${data.data.uname}`
      icon = <Avatar uid={data.data.mid} />
    }

    if (data.code === 404) {
      label = '同步密钥不存在，请检查登录状态并重新同步'
      icon = <IconRefreshOff size={'1.25rem'} className='text-rose-500' />
    }

    if (data.code === -101) {
      label = '当前同步密钥未登录，请检查登录状态并重新同步'
      icon = <IconRefreshAlert size={'1.25rem'} className='text-orange-500' />
    }
  }

  if (loading) {
    label = '正在认证登录状态'
    icon = <Loading className='text-ac' size='1.25rem' />
  }

  if (error) {
    label = '获取同步信息错误，请检查同步密钥'
    icon = <IconExclamationCircle size={'1.25rem'} className='text-rose-500' />
  }

  return <div className='flex items-center'>{icon}</div>
})

export function LoginSyncInput() {
  const [loginSyncTokenVisible, setLoginSyncTokenVisible] = useState(false)

  const { t } = useTranslation()

  const [localStorageOptions, setLocalStorageOptions] = useLocalStorage()
  const { loginSyncToken, loginSyncServer } = localStorageOptions

  const [debouncedLoginSyncTokenToTest] = useDebouncedValue(loginSyncToken, 400)
  const [debouncedLoginSyncServerToTest] = useDebouncedValue(loginSyncServer, 400)
  const {
    dataFromLoginSyncDryrun,
    isLoginSyncDryrunLoading,
    isLoginSyncDryrunValidating,
    isLoginSyncDryrunError,
    loginSyncDryrunMutate,
  } = useLoginSyncDryrun(debouncedLoginSyncTokenToTest, debouncedLoginSyncServerToTest)

  // Update the translate manager's cached token whenever the loginSyncToken changes
  useEffect(() => {
    translateManager.refreshLoginSyncToken()
    ttsManager.refreshLoginSyncToken()
  }, [loginSyncToken])

  return (
    <div>
      <InputWithLabel
        inputClassName='font-mono'
        type={loginSyncTokenVisible ? 'text' : 'password'}
        id='laplace-login-sync-token-input'
        data-1p-ignore
        label={
          <div
            className='flex items-center gap-x-1'
            // https://github.com/radix-ui/primitives/issues/2248
            onFocusCapture={e => {
              e.stopPropagation()
            }}
          >
            <OptionLabel
              label={t('loginSyncToken.label')}
              desc={t('loginSyncToken.desc')}
              refLink='https://laplace.live/login-sync'
              refLinkLabel='相关文档'
            />
            <Button
              variant='link'
              tint={loginSyncTokenVisible ? 'accent' : 'default'}
              className='p-0'
              onClick={() => {
                setLoginSyncTokenVisible(!loginSyncTokenVisible)
              }}
              aria-label={t('loginSyncToken.toggle')}
            >
              {loginSyncTokenVisible ? <IconEyeOff size={'1em'} /> : <IconEye size={'1em'} />}
            </Button>
          </div>
        }
        placeholder={t('loginSyncToken.placeholder')}
        value={loginSyncToken}
        onChange={event => {
          // setLoginSyncTokenToTest(event.currentTarget.value)
          setLocalStorageOptions({
            ...localStorageOptions,
            loginSyncToken: event.currentTarget.value,
          })
        }}
        // localStorage-only method. Not working in OBS mode
        // onChange={event => setLoginSyncToken(event.currentTarget.value)}
        autoComplete='off'
        error={isLoginSyncDryrunError ? t('loginSyncTokenTest.error') : false}
        rightSection={
          <div className='flex items-center justify-center'>
            <Button
              variant={'link'}
              className='mr-1.5 p-0.25'
              onClick={() => {
                // setLoginSyncTokenToTest(loginSyncToken)
                loginSyncDryrunMutate()
              }}
              disabled={!loginSyncToken || isLoginSyncDryrunLoading || isLoginSyncDryrunValidating}
              // loading={isLoginSyncDryrunLoading || isLoginSyncDryrunValidating}
              aria-label={t('loginSyncTokenTest.label')}
            >
              {isLoginSyncDryrunLoading || isLoginSyncDryrunValidating ? (
                <Loading size={'1rem'} />
              ) : (
                <IconRefresh size={'1rem'} />
              )}
            </Button>
          </div>
        }
      />
      {dataFromLoginSyncDryrun && !isLoginSyncDryrunValidating && (
        <div className='mt-1 text-sm'>
          {(() => {
            switch (dataFromLoginSyncDryrun.code) {
              case 0:
                return (
                  <div className='flex items-center gap-x-1 text-emerald-500'>
                    验证成功
                    <a
                      href={`https://space.bilibili.com/${dataFromLoginSyncDryrun.data.fetcher}`}
                      target='_blank'
                      rel='noopener noreferrer'
                    >
                      <Avatar
                        className='[--avatar-size:16px]'
                        avatar={dataFromLoginSyncDryrun.data.face}
                        uid={Number(dataFromLoginSyncDryrun.data.fetcher)}
                      />
                    </a>
                    <code>{dataFromLoginSyncDryrun.data.uname}</code>
                  </div>
                )
              case 404:
                return <span className='text-rose-500'>同步密钥不存在，请检查登录状态并重新同步</span>
              case -101:
                return <span className='text-orange-500'>当前同步密钥未登录，请检查登录状态并重新同步</span>
              default:
                return (
                  <span className='text-rose-500'>
                    未知错误 {dataFromLoginSyncDryrun.code}
                    {dataFromLoginSyncDryrun.message && `: ${dataFromLoginSyncDryrun.message}`}
                  </span>
                )
            }
          })()}
        </div>
      )}
      {isLoginSyncDryrunError && <pre className='mt-1'>{isLoginSyncDryrunError.toString()}</pre>}
    </div>
  )
}
