@layer template {
  .event {
    --avatar-size: calc(var(--event-font-size) * 2);
    --gift-size: calc(var(--event-font-size) * 2);
    --fallback-text: var(--event-gift-text, var(--event-message-text, var(--text-color)));
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--fallback-text);
  }

  .read {
    opacity: 0.5 !important;
    filter: grayscale(0.8) !important;
  }

  .eventSizeNormal {
    --avatar-size: var(--event-line-height);
    --gift-size: var(--event-line-height);

    padding-top: calc(var(--event-font-size) * 0.125);
    padding-bottom: calc(var(--event-font-size) * 0.125);
    /* TODO: a temp `display: block` padding fix, this method causes non-even padding */
    /* padding-bottom: 0; */

    .message {
      color: var(--event-red-envelope-start-normal-text, var(--red-darker));
      background: var(--event-red-envelope-start-normal-bg, transparent);
    }

    &.dark .message {
      color: var(--event-red-envelope-start-normal-text, var(--red));
      background: var(--event-red-envelope-start-normal-bg, transparent);
    }

    .content {
      display: block;
    }

    .message {
      display: inline;
      margin-left: calc(var(--event-font-size) * 0.125);
      vertical-align: top;
    }

    .price {
      display: none;
    }
  }

  .eventSizeHighlight {
    --gift-size: calc(var(--event-font-size) * 3.75);

    font-size: calc(var(--event-font-size) * 1.125);
    border-radius: var(--event-border-radius);
    color: var(--event-gift-text, #fff);
    background: var(--event-red-envelope-start-bg, var(--red-darker));

    @container (max-width: 320px) {
      --gift-size: calc(var(--event-font-size) * 2);
    }

    &.dark {
      color: var(--event-red-envelope-start-text, #fff);
      background: var(--event-red-envelope-start-bg, var(--red-50));
    }

    .content {
      position: relative;
      display: grid;
      padding-right: calc(var(--avatar-size) * 1.25);
      gap: calc(var(--event-font-size) * 0.25);
    }

    .giftDetailsPrice {
      display: none;
    }

    .top {
      gap: calc(var(--event-font-size) * 0.375);
    }

    .giftIconWrap {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .showAs_sticky {
    --avatar-size: var(--event-line-height);
    --gift-size: calc(var(--event-line-height) * 1.25);

    &.eventSizeHighlight {
      --event-border-radius: calc(var(--1px) * 100);

      font-size: var(--event-font-size);
      padding: calc(var(--event-font-size) * 0.25);
      padding-right: calc(var(--event-font-size) * 0.5);

      .giftIconWrap {
        top: calc(var(--1px) * -3);
      }
    }
  }

  .username {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    word-break: break-word;

    a {
      color: inherit;
    }
  }

  .priceCurrency {
    opacity: 0.8;
    font-weight: normal;
  }

  .priceFigure {
    font-weight: bold;
  }

  .message {
    line-height: var(--event-line-height);
    word-break: break-word;
  }

  .top {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--event-font-size) * 0.25);
    line-height: var(--event-line-height);
    font-weight: bold;
  }

  .giftDetailsSymbol {
    opacity: 0.6;
  }

  .giftDetailsFigure {
    opacity: 0.6;
  }

  .giftIconWrap {
    display: inline-block;
    /* This help avoid inline image height issues */
    max-height: var(--gift-size);
    pointer-events: none;

    img {
      width: var(--gift-size);
      height: var(--gift-size);
    }
  }

  .giftReceiver {
    --avatar-size: 1.2em;
    display: inline-flex;
    align-items: center;
  }

  .rewardsItemsWrap {
    display: inline-flex;
    flex-wrap: wrap;
    vertical-align: top;
    gap: 0.125em;

    .rewardsItemWrap {
      position: relative;
      display: inline-flex;

      .rewardsItemCount {
        position: absolute;
        top: -0.25em;
        right: -0.25em;
        font-size: calc(var(--1px) * 11);
        line-height: 1;
        background-color: var(--yellow-60);
        padding: calc(var(--1px) * 1) calc(var(--1px) * 3);
        border-radius: 0.5em;
      }

      .rewardsItemPictureWrap {
        img {
          width: var(--event-line-height);
          height: var(--event-line-height);
        }
      }
    }
  }

  .hidden {
    display: none;
  }
}
