import tex from './tex.mjs'

const lang = Object.freeze(JSON.parse("{\"displayName\":\"LaTeX\",\"name\":\"latex\",\"patterns\":[{\"match\":\"(?<=\\\\\\\\(?:[@\\\\w]|[@\\\\w]{2}|[@\\\\w]{3}|[@\\\\w]{4}|[@\\\\w]{5}|[@\\\\w]{6}))\\\\s\",\"name\":\"meta.space-after-command.latex\"},{\"begin\":\"((\\\\\\\\)(?:usepackage|documentclass))\\\\b(?=[\\\\[{])\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.preamble.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"}},\"end\":\"(?<=})\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.preamble.latex\",\"patterns\":[{\"include\":\"#multiline-optional-arg\"},{\"begin\":\"((?:\\\\G|(?<=]))\\\\{)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"support.class.latex\",\"end\":\"(})\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"$self\"}]}]},{\"begin\":\"((\\\\\\\\)in(?:clude|put))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.include.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.include.latex\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"((\\\\\\\\)((?:sub){0,2}section|(?:sub)?paragraph|chapter|part|addpart|addchap|addsec|minisec|frametitle)\\\\*?)((?:\\\\[[^\\\\[]*?]){0,2})(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.section.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"4\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"5\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"entity.name.section.latex\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.function.section.$3.latex\",\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{songs}\\\\{.*})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"meta.data.environment.songs.latex\",\"end\":\"(\\\\\\\\end\\\\{songs}(?:\\\\s*\\\\n)?)\",\"name\":\"meta.function.environment.songs.latex\",\"patterns\":[{\"include\":\"text.tex.latex#songs-chords\"}]},{\"begin\":\"\\\\s*((\\\\\\\\)beginsong)(?=\\\\{)\",\"captures\":{\"1\":{\"name\":\"support.function.be.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"4\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"end\":\"((\\\\\\\\)endsong)(?:\\\\s*\\\\n)?\",\"name\":\"meta.function.environment.song.latex\",\"patterns\":[{\"include\":\"#multiline-arg-no-highlight\"},{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=[]}]))\\\\s*\",\"contentName\":\"meta.data.environment.song.latex\",\"end\":\"\\\\s*(?=\\\\\\\\endsong)\",\"patterns\":[{\"include\":\"text.tex.latex#songs-chords\"}]}]},{\"begin\":\"(?:^\\\\s*)?\\\\\\\\begin\\\\{(lstlisting|minted|pyglist)}(?=[\\\\[{])\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\\\\\end\\\\{\\\\1}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(asy(?:|mptote))(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.asy\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.asy\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(bash)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.shell\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.shell\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(c(?:|pp))(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.cpp.embedded.latex\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.cpp.embedded.latex\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(css)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.css\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.css\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(gnuplot)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.gnuplot\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.gnuplot\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(h(?:s|askell))(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.haskell\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.haskell\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(html)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"text.html\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"text.html.basic\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(java)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.java\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.java\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(j(?:l|ulia))(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.julia\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.julia\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(j(?:s|avascript))(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.js\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.js\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(lua)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.lua\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.lua\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(py|python|sage)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.python\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.python\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(r(?:b|uby))(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.ruby\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.ruby\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(rust)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.rust\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.rust\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(t(?:s|ypescript))(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.ts\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.ts\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(xml)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"text.xml\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"text.xml\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)(yaml)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"source.yaml\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)})\",\"patterns\":[{\"include\":\"source.yaml\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)([A-Za-z]*)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"meta.function.embedded.latex\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:lstlisting|minted|pyglist)})\",\"name\":\"meta.embedded.block.generic.latex\"}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{asy(?:|code)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{asy(?:|code)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.asymptote\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{asy(?:|code)\\\\*?})\",\"patterns\":[{\"include\":\"source.asymptote\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{cppcode\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{cppcode\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.cpp.embedded.latex\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{cppcode\\\\*?})\",\"patterns\":[{\"include\":\"source.cpp.embedded.latex\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{dot(?:2tex|code)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{dot(?:2tex|code)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.dot\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{dot(?:2tex|code)\\\\*?})\",\"patterns\":[{\"include\":\"source.dot\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{gnuplot\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{gnuplot\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.gnuplot\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{gnuplot\\\\*?})\",\"patterns\":[{\"include\":\"source.gnuplot\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{hscode\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{hscode\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.haskell\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{hscode\\\\*?})\",\"patterns\":[{\"include\":\"source.haskell\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{java(?:code|verbatim|block|concode|console|converbatim)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{java(?:code|verbatim|block|concode|console|converbatim)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.java\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{java(?:code|verbatim|block|concode|console|converbatim)\\\\*?})\",\"patterns\":[{\"include\":\"source.java\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{jl(?:code|verbatim|block|concode|console|converbatim)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{jl(?:code|verbatim|block|concode|console|converbatim)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.julia\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{jl(?:code|verbatim|block|concode|console|converbatim)\\\\*?})\",\"patterns\":[{\"include\":\"source.julia\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{julia(?:code|verbatim|block|concode|console|converbatim)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{julia(?:code|verbatim|block|concode|console|converbatim)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.julia\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{julia(?:code|verbatim|block|concode|console|converbatim)\\\\*?})\",\"patterns\":[{\"include\":\"source.julia\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{luacode\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{luacode\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.lua\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{luacode\\\\*?})\",\"patterns\":[{\"include\":\"source.lua\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{py(?:code|verbatim|block|concode|console|converbatim)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{py(?:code|verbatim|block|concode|console|converbatim)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.python\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{py(?:code|verbatim|block|concode|console|converbatim)\\\\*?})\",\"patterns\":[{\"include\":\"source.python\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{pylab(?:code|verbatim|block|concode|console|converbatim)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{pylab(?:code|verbatim|block|concode|console|converbatim)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.python\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{pylab(?:code|verbatim|block|concode|console|converbatim)\\\\*?})\",\"patterns\":[{\"include\":\"source.python\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|python|pythonq|pythonrepl)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|python|pythonq|pythonrepl)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.python\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|python|pythonq|pythonrepl)\\\\*?})\",\"patterns\":[{\"include\":\"source.python\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{scalacode\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{scalacode\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.scala\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{scalacode\\\\*?})\",\"patterns\":[{\"include\":\"source.scala\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{sympy(?:code|verbatim|block|concode|console|converbatim)\\\\*?}(?:\\\\[[-0-9A-Z_a-z]*])?(?=[\\\\[{]|\\\\s*$)\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\s*\\\\\\\\end\\\\{sympy(?:code|verbatim|block|concode|console|converbatim)\\\\*?}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}}},{\"begin\":\"^(?=\\\\s*)\",\"contentName\":\"source.python\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{sympy(?:code|verbatim|block|concode|console|converbatim)\\\\*?})\",\"patterns\":[{\"include\":\"source.python\"}]}]},{\"begin\":\"\\\\s*\\\\\\\\begin\\\\{((?:[A-Za-z]*code|lstlisting|minted|pyglist)\\\\*?)}(?:\\\\[.*])?(?:\\\\{.*})?\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"meta.function.embedded.latex\",\"end\":\"\\\\\\\\end\\\\{\\\\1}(?:\\\\s*\\\\n)?\",\"name\":\"meta.embedded.block.generic.latex\"},{\"begin\":\"((?:^\\\\s*)?\\\\\\\\begin\\\\{((?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?))})(?:\\\\[[^]]*]){0,2}(?=\\\\{)\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"(\\\\\\\\end\\\\{\\\\2})\",\"patterns\":[{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:asy(?:|mptote))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.asy\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.asy\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:bash)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.shell\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.shell\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:c(?:|pp))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.cpp.embedded.latex\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.cpp.embedded.latex\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:css)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.css\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.css\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:gnuplot)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.gnuplot\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.gnuplot\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:h(?:s|askell))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.haskell\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.haskell\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:html)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"text.html\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"text.html.basic\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:java)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.java\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.java\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:j(?:l|ulia))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.julia\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.julia\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:j(?:s|avascript))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.js\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.js\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:lua)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.lua\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.lua\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:py|python|sage)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.python\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.python\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:r(?:b|uby))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.ruby\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.ruby\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:rust)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.rust\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.rust\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:t(?:s|ypescript))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.ts\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.ts\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:xml)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"text.xml\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"text.xml\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:yaml)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"source.yaml\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"source.yaml\"}]}]},{\"begin\":\"\\\\G(\\\\{)(?:__|[a-z\\\\s]*)(?i:tikz(?:|picture))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"text.tex.latex\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"include\":\"text.tex.latex\"}]}]},{\"begin\":\"\\\\G(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(})\\\\s*$\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"^(\\\\s*)\",\"contentName\":\"meta.function.embedded.latex\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{(?:RobExt)?(?:CacheMeCode|PlaceholderPathFromCode\\\\*?|PlaceholderFromCode\\\\*?|SetPlaceholderCode\\\\*?)})\",\"name\":\"meta.embedded.block.generic.latex\"}]}]},{\"begin\":\"(?:^\\\\s*)?\\\\\\\\begin\\\\{(terminal\\\\*?)}(?=[\\\\[{])\",\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"\\\\\\\\end\\\\{\\\\1}\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)([A-Za-z]*)(})\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"meta.function.embedded.latex\",\"end\":\"^\\\\s*(?=\\\\\\\\end\\\\{terminal\\\\*?})\",\"name\":\"meta.embedded.block.generic.latex\"}]},{\"begin\":\"((\\\\\\\\)addplot)\\\\+?(\\\\[[^\\\\[]*])*\\\\s*(gnuplot)\\\\s*(\\\\[[^\\\\[]*])*\\\\s*(\\\\{)\",\"captures\":{\"1\":{\"name\":\"support.function.be.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"variable.parameter.function.latex\"},\"5\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"6\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"\\\\s*(};)\",\"patterns\":[{\"begin\":\"%\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.latex\"}},\"end\":\"$\\\\n?\",\"name\":\"comment.line.percentage.latex\"},{\"include\":\"source.gnuplot\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{((?:fboxv|boxedv|[Vv]|spv)erbatim\\\\*?)})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"markup.raw.verbatim.latex\",\"end\":\"(\\\\\\\\end\\\\{\\\\2})\",\"name\":\"meta.function.verbatim.latex\"},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{VerbatimOut}\\\\{[^}]*})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"markup.raw.verbatim.latex\",\"end\":\"(\\\\\\\\end\\\\{VerbatimOut})\",\"name\":\"meta.function.verbatim.latex\"},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{alltt})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"markup.raw.verbatim.latex\",\"end\":\"(\\\\\\\\end\\\\{alltt})\",\"name\":\"meta.function.alltt.latex\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.function.latex\"}},\"match\":\"(\\\\\\\\)[A-Za-z]+\",\"name\":\"support.function.general.latex\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{([Cc]omment)})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"comment.line.percentage.latex\",\"end\":\"(\\\\\\\\end\\\\{\\\\2})\",\"name\":\"meta.function.verbatim.latex\"},{\"begin\":\"\\\\s*((\\\\\\\\)h(?:ref|yperref|yperimage))(?=[\\\\[{])\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.url.latex\"}},\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.function.hyperlink.latex\",\"patterns\":[{\"include\":\"#multiline-optional-arg-no-highlight\"},{\"begin\":\"(?:\\\\G|(?<=]))(\\\\{)([^}]*)(})(?:\\\\{[^}]*}){2}?(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"markup.underline.link.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.end.latex\"},\"4\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"meta.variable.parameter.function.latex\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"(?:\\\\G|(?<=]))(?:(\\\\{)[^}]*(}))?(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"2\":{\"name\":\"punctuation.definition.arguments.end.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"meta.variable.parameter.function.latex\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"$self\"}]}]},{\"captures\":{\"1\":{\"name\":\"support.function.url.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"5\":{\"name\":\"punctuation.definition.arguments.end.latex\"},\"'\":{\"name\":\"markup.underline.link.latex\"}},\"match\":\"\\\\s*((\\\\\\\\)url)(\\\\{)([^}]*)(})\",\"name\":\"meta.function.link.url.latex\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"match\":\"(\\\\s*\\\\\\\\begin\\\\{document})\",\"name\":\"meta.function.begin-document.latex\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"match\":\"(\\\\s*\\\\\\\\end\\\\{document})\",\"name\":\"meta.function.end-document.latex\"},{\"begin\":\"\\\\s*((\\\\\\\\)begin)(\\\\{)((?:\\\\+?array|equation|(?:IEEE)?eqnarray|multline|align|aligned|alignat|alignedat|flalign|flaligned|flalignat|split|gather|gathered|\\\\+?cases|(?:display)?math|\\\\+?[A-Za-z]*matrix|[BVbpv]?NiceMatrix|[BVbpv]?NiceArray|(?:arg)?m(?:ini|axi))[!*]?)(})(\\\\s*\\\\n)?\",\"captures\":{\"1\":{\"name\":\"support.function.be.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"4\":{\"name\":\"variable.parameter.function.latex\"},\"5\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"contentName\":\"meta.math.block.latex support.class.math.block.environment.latex\",\"end\":\"\\\\s*((\\\\\\\\)end)(\\\\{)(\\\\4)(})(?:\\\\s*\\\\n)?\",\"name\":\"meta.function.environment.math.latex\",\"patterns\":[{\"match\":\"(?<!\\\\\\\\)&\",\"name\":\"keyword.control.equation.align.latex\"},{\"match\":\"\\\\\\\\\\\\\\\\\",\"name\":\"keyword.control.equation.newline.latex\"},{\"include\":\"#definition-label\"},{\"include\":\"text.tex#math-content\"},{\"include\":\"$self\"}]},{\"begin\":\"\\\\s*(\\\\\\\\begin\\\\{empheq}(?:\\\\[.*])?)\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"meta.math.block.latex support.class.math.block.environment.latex\",\"end\":\"\\\\s*(\\\\\\\\end\\\\{empheq})\",\"name\":\"meta.function.environment.math.latex\",\"patterns\":[{\"match\":\"(?<!\\\\\\\\)&\",\"name\":\"keyword.control.equation.align.latex\"},{\"match\":\"\\\\\\\\\\\\\\\\\",\"name\":\"keyword.control.equation.newline.latex\"},{\"include\":\"#definition-label\"},{\"include\":\"text.tex#math-content\"},{\"include\":\"$self\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{(tabular[*xy]?|xltabular|longtable|(?:long)?tabu|(?:long|tall)?tblr|NiceTabular[*X]?|booktabs)}(\\\\s*\\\\n)?)\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"meta.data.environment.tabular.latex\",\"end\":\"(\\\\s*\\\\\\\\end\\\\{(\\\\2)}(?:\\\\s*\\\\n)?)\",\"name\":\"meta.function.environment.tabular.latex\",\"patterns\":[{\"match\":\"(?<!\\\\\\\\)&\",\"name\":\"keyword.control.table.cell.latex\"},{\"match\":\"\\\\\\\\\\\\\\\\\",\"name\":\"keyword.control.table.newline.latex\"},{\"include\":\"$self\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{(itemize|enumerate|description|list)})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"(\\\\\\\\end\\\\{\\\\2}(?:\\\\s*\\\\n)?)\",\"name\":\"meta.function.environment.list.latex\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{tikzpicture})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"(\\\\\\\\end\\\\{tikzpicture}(?:\\\\s*\\\\n)?)\",\"name\":\"meta.function.environment.latex.tikz\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{frame})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"(\\\\\\\\end\\\\{frame})\",\"name\":\"meta.function.environment.frame.latex\",\"patterns\":[{\"include\":\"$self\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{(mpost\\\\*?)})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"(\\\\\\\\end\\\\{\\\\2}(?:\\\\s*\\\\n)?)\",\"name\":\"meta.function.environment.latex.mpost\"},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{markdown})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"contentName\":\"meta.embedded.markdown_latex_combined\",\"end\":\"(\\\\\\\\end\\\\{markdown})\",\"patterns\":[{\"include\":\"text.tex.markdown_latex_combined\"}]},{\"begin\":\"(\\\\s*\\\\\\\\begin\\\\{(\\\\p{Alphabetic}+\\\\*?)})\",\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#begin-env-tokenizer\"}]}},\"end\":\"(\\\\\\\\end\\\\{\\\\2}(?:\\\\s*\\\\n)?)\",\"name\":\"meta.function.environment.general.latex\",\"patterns\":[{\"include\":\"$self\"}]},{\"captures\":{\"1\":{\"name\":\"storage.type.function.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.begin.latex\"},\"4\":{\"name\":\"support.function.general.latex\"},\"5\":{\"name\":\"punctuation.definition.function.latex\"},\"6\":{\"name\":\"punctuation.definition.end.latex\"}},\"match\":\"((\\\\\\\\)(?:newcommand|renewcommand|(?:re)?newrobustcmd|DeclareRobustCommand))\\\\*?(\\\\{)((\\\\\\\\)[^}]*)(})\"},{\"begin\":\"((\\\\\\\\)marginpar)((?:\\\\[[^\\\\[]*?])*)(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.marginpar.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"punctuation.definition.marginpar.begin.latex\"}},\"contentName\":\"meta.paragraph.margin.latex\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.marginpar.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"((\\\\\\\\)footnote)((?:\\\\[[^\\\\[]*?])*)(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.footnote.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"punctuation.definition.footnote.begin.latex\"}},\"contentName\":\"entity.name.footnote.latex\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.footnote.end.latex\"}},\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"((\\\\\\\\)emph)(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.emph.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.emph.begin.latex\"}},\"contentName\":\"markup.italic.emph.latex\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.emph.end.latex\"}},\"name\":\"meta.function.emph.latex\",\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"((\\\\\\\\)textit)(\\\\{)\",\"captures\":{\"1\":{\"name\":\"support.function.textit.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.textit.begin.latex\"}},\"contentName\":\"markup.italic.textit.latex\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.textit.end.latex\"}},\"name\":\"meta.function.textit.latex\",\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"((\\\\\\\\)textbf)(\\\\{)\",\"captures\":{\"1\":{\"name\":\"support.function.textbf.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.textbf.begin.latex\"}},\"contentName\":\"markup.bold.textbf.latex\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.textbf.end.latex\"}},\"name\":\"meta.function.textbf.latex\",\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"begin\":\"((\\\\\\\\)texttt)(\\\\{)\",\"captures\":{\"1\":{\"name\":\"support.function.texttt.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.texttt.begin.latex\"}},\"contentName\":\"markup.raw.texttt.latex\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.texttt.end.latex\"}},\"name\":\"meta.function.texttt.latex\",\"patterns\":[{\"include\":\"text.tex#braces\"},{\"include\":\"$self\"}]},{\"captures\":{\"0\":{\"name\":\"keyword.other.item.latex\"},\"1\":{\"name\":\"punctuation.definition.keyword.latex\"}},\"match\":\"(\\\\\\\\)item\\\\b\",\"name\":\"meta.scope.item.latex\"},{\"begin\":\"((\\\\\\\\)(?:[Aa]uto|foot|full|no|ref|short|[Tt]ext|[Pp]aren|[Ss]mart)?[Cc]ite(?:al)?(?:[pst]|author|year(?:par)?|title)?[ANP]*\\\\*?)((?:(?:\\\\([^)]*\\\\)){0,2}(?:\\\\[[^]]*]){0,2}\\\\{[-.:_\\\\p{Alphabetic}\\\\p{N}]*})*)(<[^]<>]*>)?((?:\\\\[[^]]*])*)(\\\\{)\",\"captures\":{\"1\":{\"name\":\"keyword.control.cite.latex\"},\"2\":{\"name\":\"punctuation.definition.keyword.latex\"},\"3\":{\"patterns\":[{\"include\":\"#autocites-arg\"}]},\"4\":{\"patterns\":[{\"include\":\"#optional-arg-angle-no-highlight\"}]},\"5\":{\"patterns\":[{\"include\":\"#optional-arg-bracket-no-highlight\"}]},\"6\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.citation.latex\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"comment.line.percentage.tex\"},\"2\":{\"name\":\"punctuation.definition.comment.tex\"}},\"match\":\"((%).*)$\"},{\"match\":\"[-.:\\\\p{Alphabetic}\\\\p{N}]+\",\"name\":\"constant.other.reference.citation.latex\"}]},{\"begin\":\"((\\\\\\\\)bibentry)(\\\\{)\",\"captures\":{\"1\":{\"name\":\"keyword.control.cite.latex\"},\"2\":{\"name\":\"punctuation.definition.keyword.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.citation.latex\",\"patterns\":[{\"match\":\"[.:\\\\p{Alphabetic}\\\\p{N}]+\",\"name\":\"constant.other.reference.citation.latex\"}]},{\"begin\":\"((\\\\\\\\)\\\\w*[Rr]ef\\\\*?)(?:\\\\[[^]]*])?(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.ref.latex\"},\"2\":{\"name\":\"punctuation.definition.keyword.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.reference.label.latex\",\"patterns\":[{\"match\":\"[!*,-/:^_\\\\p{Alphabetic}\\\\p{N}]\",\"name\":\"constant.other.reference.label.latex\"}]},{\"include\":\"#definition-label\"},{\"begin\":\"((\\\\\\\\)(?:[Vv]|spv)erb\\\\*?)\\\\s*((\\\\\\\\)scantokens)(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.verb.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"support.function.verb.latex\"},\"4\":{\"name\":\"punctuation.definition.verb.latex\"},\"5\":{\"name\":\"punctuation.definition.begin.latex\"}},\"contentName\":\"markup.raw.verb.latex\",\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.end.latex\"}},\"name\":\"meta.function.verb.latex\",\"patterns\":[{\"include\":\"$self\"}]},{\"captures\":{\"1\":{\"name\":\"support.function.verb.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.verb.latex\"},\"4\":{\"name\":\"markup.raw.verb.latex\"},\"5\":{\"name\":\"punctuation.definition.verb.latex\"}},\"match\":\"((\\\\\\\\)(?:[Vv]|spv)erb\\\\*?)\\\\s*((?<=\\\\s)\\\\S|[^A-Za-z])(.*?)(\\\\3|$)\",\"name\":\"meta.function.verb.latex\"},{\"captures\":{\"1\":{\"name\":\"support.function.verb.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"5\":{\"name\":\"punctuation.definition.arguments.end.latex\"},\"6\":{\"name\":\"punctuation.definition.verb.latex\"},\"7\":{\"name\":\"markup.raw.verb.latex\"},\"8\":{\"name\":\"punctuation.definition.verb.latex\"},\"9\":{\"name\":\"punctuation.definition.verb.latex\"},\"10\":{\"name\":\"markup.raw.verb.latex\"},\"11\":{\"name\":\"punctuation.definition.verb.latex\"}},\"match\":\"((\\\\\\\\)mint(?:|inline))((?:\\\\[[^\\\\[]*?])?)(\\\\{)[A-Za-z]*(})(?:([^A-Za-{])(.*?)(\\\\6)|(\\\\{)(.*?)(}))\",\"name\":\"meta.function.verb.latex\"},{\"captures\":{\"1\":{\"name\":\"support.function.verb.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"punctuation.definition.verb.latex\"},\"5\":{\"name\":\"markup.raw.verb.latex\"},\"6\":{\"name\":\"punctuation.definition.verb.latex\"},\"7\":{\"name\":\"punctuation.definition.verb.latex\"},\"8\":{\"name\":\"markup.raw.verb.latex\"},\"9\":{\"name\":\"punctuation.definition.verb.latex\"}},\"match\":\"((\\\\\\\\)[a-z]+inline)((?:\\\\[[^\\\\[]*?])?)(?:([^A-Za-{])(.*?)(\\\\4)|(\\\\{)(.*?)(}))\",\"name\":\"meta.function.verb.latex\"},{\"captures\":{\"1\":{\"name\":\"support.function.verb.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"punctuation.definition.verb.latex\"},\"5\":{\"name\":\"source.python\",\"patterns\":[{\"include\":\"source.python\"}]},\"6\":{\"name\":\"punctuation.definition.verb.latex\"},\"7\":{\"name\":\"punctuation.definition.verb.latex\"},\"8\":{\"name\":\"source.python\",\"patterns\":[{\"include\":\"source.python\"}]},\"9\":{\"name\":\"punctuation.definition.verb.latex\"}},\"match\":\"((\\\\\\\\)(?:(?:py|pycon|pylab|pylabcon|sympy|sympycon)[cv]?|pyq|pycq|pyif))((?:\\\\[[^\\\\[]*?])?)(?:([^A-Za-{])(.*?)(\\\\4)|(\\\\{)(.*?)(}))\",\"name\":\"meta.function.verb.latex\"},{\"captures\":{\"1\":{\"name\":\"support.function.verb.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"punctuation.definition.verb.latex\"},\"5\":{\"name\":\"source.julia\",\"patterns\":[{\"include\":\"source.julia\"}]},\"6\":{\"name\":\"punctuation.definition.verb.latex\"},\"7\":{\"name\":\"punctuation.definition.verb.latex\"},\"8\":{\"name\":\"source.julia\",\"patterns\":[{\"include\":\"source.julia\"}]},\"9\":{\"name\":\"punctuation.definition.verb.latex\"}},\"match\":\"((\\\\\\\\)j(?:l|ulia)[cv]?)((?:\\\\[[^\\\\[]*?])?)(?:([^A-Za-{])(.*?)(\\\\4)|(\\\\{)(.*?)(}))\",\"name\":\"meta.function.verb.latex\"},{\"begin\":\"((\\\\\\\\)(?:directlua|luadirect|luaexec))(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.verb.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"contentName\":\"source.lua\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"patterns\":[{\"include\":\"source.lua\"}]},{\"match\":\"\\\\\\\\(?:newline|pagebreak|clearpage|linebreak|pause)\\\\b\",\"name\":\"keyword.control.layout.latex\"},{\"begin\":\"\\\\\\\\\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.latex\"}},\"end\":\"\\\\\\\\\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.latex\"}},\"name\":\"meta.math.block.latex support.class.math.block.environment.latex\",\"patterns\":[{\"include\":\"text.tex#math-content\"},{\"include\":\"$self\"}]},{\"begin\":\"\\\\$\\\\$\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.latex\"}},\"end\":\"\\\\$\\\\$\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.latex\"}},\"name\":\"meta.math.block.latex support.class.math.block.environment.latex\",\"patterns\":[{\"match\":\"\\\\\\\\\\\\$\",\"name\":\"constant.character.escape.latex\"},{\"include\":\"text.tex#math-content\"},{\"include\":\"$self\"}]},{\"begin\":\"\\\\$\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.tex\"}},\"end\":\"\\\\$\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.tex\"}},\"name\":\"meta.math.block.tex support.class.math.block.tex\",\"patterns\":[{\"match\":\"\\\\\\\\\\\\$\",\"name\":\"constant.character.escape.latex\"},{\"include\":\"text.tex#math-content\"},{\"include\":\"$self\"}]},{\"begin\":\"\\\\\\\\\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.latex\"}},\"end\":\"\\\\\\\\]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.latex\"}},\"name\":\"meta.math.block.latex support.class.math.block.environment.latex\",\"patterns\":[{\"include\":\"text.tex#math-content\"},{\"include\":\"$self\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.latex\"}},\"match\":\"(\\\\\\\\)(text(s(terling|ixoldstyle|urd|e(ction|venoldstyle|rvicemark))|yen|n(ineoldstyle|umero|aira)|c(ircledP|o(py(left|right)|lonmonetary)|urrency|e(nt(oldstyle)?|lsius))|t(hree(superior|oldstyle|quarters(emdash)?)|i(ldelow|mes)|w(o(superior|oldstyle)|elveudash)|rademark)|interrobang(down)?|zerooldstyle|o(hm|ne(superior|half|oldstyle|quarter)|penbullet|rd((?:femin|mascul)ine))|d(i(scount|ed|v(orced)?)|o(ng|wnarrow|llar(oldstyle)?)|egree|agger(dbl)?|blhyphen(char)?)|uparrow|p(ilcrow|e(so|r(t((?:|ent)housand)|iodcentered))|aragraph|m)|e(stimated|ightoldstyle|uro)|quotes(traight((?:dbl|)base)|ingle)|f(iveoldstyle|ouroldstyle|lorin|ractionsolidus)|won|l(not|ira|e(ftarrow|af)|quill|angle|brackdbl)|a(s(cii(caron|dieresis|acute|grave|macron|breve)|teriskcentered)|cutedbl)|r(ightarrow|e(cipe|ferencemark|gistered)|quill|angle|brackdbl)|g(uarani|ravedbl)|m(ho|inus|u(sicalnote)?|arried)|b(igcircle|orn|ullet|lank|a(ht|rdbl)|rokenbar)))\\\\b\",\"name\":\"constant.character.latex\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.latex\"}},\"match\":\"(\\\\\\\\)(?:[cgl]_+[@_\\\\p{Alphabetic}]+_[a-z]+|[qs]_[@_\\\\p{Alphabetic}]+[@\\\\p{Alphabetic}])\",\"name\":\"variable.other.latex3.latex\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.column-specials.begin.latex\"},\"2\":{\"name\":\"punctuation.definition.column-specials.end.latex\"}},\"match\":\"[<>](\\\\{)\\\\$(})\",\"name\":\"meta.column-specials.latex\"},{\"include\":\"text.tex\"}],\"repository\":{\"autocites-arg\":{\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#optional-arg-parenthesis-no-highlight\"}]},\"2\":{\"patterns\":[{\"include\":\"#optional-arg-bracket-no-highlight\"}]},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"4\":{\"name\":\"constant.other.reference.citation.latex\"},\"5\":{\"name\":\"punctuation.definition.arguments.end.latex\"},\"6\":{\"patterns\":[{\"include\":\"#autocites-arg\"}]}},\"match\":\"((?:\\\\([^)]*\\\\)){0,2})((?:\\\\[[^]]*]){0,2})(\\\\{)([-.:_\\\\p{Alphabetic}\\\\p{N}]+)(})(.*)\"}]},\"begin-env-tokenizer\":{\"captures\":{\"1\":{\"name\":\"support.function.be.latex\"},\"2\":{\"name\":\"punctuation.definition.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"4\":{\"name\":\"variable.parameter.function.latex\"},\"5\":{\"name\":\"punctuation.definition.arguments.end.latex\"},\"6\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"},\"7\":{\"patterns\":[{\"include\":\"$self\"}]},\"8\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"},\"9\":{\"name\":\"punctuation.definition.arguments.begin.latex\"},\"10\":{\"name\":\"variable.parameter.function.latex\"},\"11\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"match\":\"\\\\s*((\\\\\\\\)(?:begin|end))(\\\\{)(\\\\p{Alphabetic}+\\\\*?)(})(?:(\\\\[)([^]]*)(])){0,2}(?:(\\\\{)([^{}]*)(}))?\"},\"definition-label\":{\"begin\":\"((\\\\\\\\)z?label)((?:\\\\[[^\\\\[]*?])*)(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.label.latex\"},\"2\":{\"name\":\"punctuation.definition.keyword.latex\"},\"3\":{\"patterns\":[{\"include\":\"#optional-arg-bracket\"}]},\"4\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.definition.label.latex\",\"patterns\":[{\"match\":\"[!*,-/:^_\\\\p{Alphabetic}\\\\p{N}]\",\"name\":\"variable.parameter.definition.label.latex\"}]},\"multiline-arg-no-highlight\":{\"begin\":\"\\\\G\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.begin.latex\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.end.latex\"}},\"name\":\"meta.parameter.latex\",\"patterns\":[{\"include\":\"$self\"}]},\"multiline-optional-arg\":{\"begin\":\"\\\\G\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"}},\"contentName\":\"variable.parameter.function.latex\",\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"}},\"name\":\"meta.parameter.optional.latex\",\"patterns\":[{\"include\":\"$self\"}]},\"multiline-optional-arg-no-highlight\":{\"begin\":\"(?:\\\\G|(?<=}))\\\\s*\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"}},\"name\":\"meta.parameter.optional.latex\",\"patterns\":[{\"include\":\"$self\"}]},\"optional-arg-angle-no-highlight\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"},\"2\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"}},\"match\":\"(<)[^<]*?(>)\",\"name\":\"meta.parameter.optional.latex\"}]},\"optional-arg-bracket\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"}},\"match\":\"(\\\\[)([^\\\\[]*?)(])\",\"name\":\"meta.parameter.optional.latex\"}]},\"optional-arg-bracket-no-highlight\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"},\"2\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"}},\"match\":\"(\\\\[)[^\\\\[]*?(])\",\"name\":\"meta.parameter.optional.latex\"}]},\"optional-arg-parenthesis\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"},\"2\":{\"name\":\"variable.parameter.function.latex\"},\"3\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"}},\"match\":\"(\\\\()([^(]*?)(\\\\))\",\"name\":\"meta.parameter.optional.latex\"}]},\"optional-arg-parenthesis-no-highlight\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.arguments.optional.begin.latex\"},\"2\":{\"name\":\"punctuation.definition.arguments.optional.end.latex\"}},\"match\":\"(\\\\()[^(]*?(\\\\))\",\"name\":\"meta.parameter.optional.latex\"}]},\"songs-chords\":{\"patterns\":[{\"begin\":\"\\\\\\\\\\\\[\",\"end\":\"]\",\"name\":\"meta.chord.block.latex support.class.chord.block.environment.latex\",\"patterns\":[{\"include\":\"$self\"}]},{\"match\":\"\\\\^\",\"name\":\"meta.chord.block.latex support.class.chord.block.environment.latex\"},{\"include\":\"$self\"}]}},\"scopeName\":\"text.tex.latex\",\"embeddedLangs\":[\"tex\"],\"embeddedLangsLazy\":[\"shellscript\",\"css\",\"gnuplot\",\"haskell\",\"html\",\"java\",\"julia\",\"javascript\",\"lua\",\"python\",\"ruby\",\"rust\",\"typescript\",\"xml\",\"yaml\",\"scala\"]}"))

export default [
...tex,
lang
]
