import React from 'react'
import clsx from 'clsx'

import type { RedEnvelopeStart } from '@/lib/event-parsers/types/red-envelope-start'

import { getGiftRank } from '@/utils/getGiftRank'
import { nf } from '@/utils/numberFormat'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import type { AsComponentProps, EventModeProps } from '@/components/event'
import { TimeDisplay } from '@/components/TimeDisplay'
import { Tooltip } from '@/components/ui/tooltip'
import UserDropdown from '@/components/UserDropdown'

import styles from './red-envelope-start.module.css'

export interface RedEnvelopeStartProps extends React.HTMLAttributes<HTMLDivElement> {
  data: RedEnvelopeStart
  mode: EventModeProps
  as: AsComponentProps
}

export default function RedEnvelopeStartItem({ data, mode, as, ...rest }: RedEnvelopeStartProps) {
  const options = useOptions()
  const { colorScheme, showGiftHighlightAbove, baseFontSize, useCst } = options

  const isGuardEnvelope = data.list.some(gift => gift.gift_id === 0)
  const price = data.priceNormalized
  const avatarSize = 320 * (baseFontSize / 20)
  const avatarUrl = `${data.avatar}@${avatarSize}w_${avatarSize}h`
  const isHighlighted = price > showGiftHighlightAbove
  const priceDisplay = (
    <>
      <span className={`${styles.priceCurrency} price-currency`}>CN¥</span>
      <span className={`${styles.priceFigure} price-figure`}>{nf.format(price)}</span>
    </>
  )
  const tooltipLabel = isGuardEnvelope
    ? '上舰红包金额不对主播分成，当用户使用上舰红包中的优惠券成为舰长后，主播可获得大航海舰长的奖励分成'
    : '该金额为红包奖池总金额，不会对主播产生任何直接收益'

  const giftRank = getGiftRank(price)

  return (
    <div
      className={clsx(
        styles.event,
        isHighlighted || as === 'sticky' ? styles.eventSizeHighlight : styles.eventSizeNormal,
        styles[`redEnvelopeRank${giftRank}`],
        styles[`showAs_${as}`],
        colorScheme === 'dark' && [styles.dark, 'dark'],
        'event',
        `event-size--${isHighlighted || as === 'sticky' ? `highlight` : `normal`}`,
        `event--${data.type}`,
        `event-type--${data.type}`,
        `event-show-as--${as}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        `red-envelope-start-rank--${giftRank}`,
        data.read && [styles.read, 'read']
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      data-red-envelope-start-price={price}
      data-red-envelope-start-rank={giftRank}
      data-read={data.read}
      {...rest}
      style={
        {
          '--inline-avatar-url': `url(${avatarUrl})`,
        } as React.CSSProperties
      }
    >
      <div className={`${styles.content} content`}>
        {as !== 'sticky' && <TimeDisplay date={data.timestampNormalized} localTime={!useCst} />}

        <div className={`${styles.top} top`}>
          <Avatar uid={data.uid} avatar={avatarUrl} />
          <Avatar uid={data.uid} avatar={avatarUrl} className={`${styles.hidden} avatar-alt-top`} />

          {as !== 'sticky' && (
            <div className={`${styles.username} username`}>
              {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}

              {mode === 'dashboard' && (
                <UserDropdown event={data}>
                  <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
                </UserDropdown>
              )}
            </div>
          )}

          <div className={`${styles.price} price`}>
            {mode === 'obs' ? (
              <span>{priceDisplay}</span>
            ) : (
              <Tooltip label={tooltipLabel} triggerOptions={{ asChild: true }}>
                <span>{priceDisplay}</span>
              </Tooltip>
            )}
          </div>

          {/* Main gift image, always show */}
          <span className={`${styles.giftIconWrap} red-envelope-start-icon-wrap`}>
            <picture className='inline-flex'>
              <img src={data.icon || '/red-envelope.webp'} alt={`红包`} referrerPolicy='no-referrer' loading='lazy' />
            </picture>
          </span>

          <span
            className={`${styles.giftIconWrap} ${styles.hidden} red-envelope-start-icon-wrap red-envelope-start-icon-wrap-alt-top`}
          >
            <picture className='inline-flex'>
              <img src={data.icon || '/red-envelope.webp'} alt={`红包`} referrerPolicy='no-referrer' loading='lazy' />
            </picture>
          </span>
        </div>

        <div className={`${styles.hidden} price-alt`}>{priceDisplay}</div>

        {/* 非高亮状态 */}
        {as !== 'sticky' && (
          <div className={`${styles.message} message`}>
            <span className='red-envelope-start-details-action'>送了红包</span>
            <span className={`${styles.rewardsItemsWrap} red-envelope-start-rewards`}>
              {data.list &&
                data.list.length > 0 &&
                data.list.map((item, idx) => {
                  return (
                    <div key={item.gift_id} className={`${styles.rewardsItemWrap} rewards-item-wrap`}>
                      <div className={`${styles.rewardsItemCount} rewards-item-count`}>{item.num}</div>
                      <picture key={idx} className={`${styles.rewardsItemPictureWrap} inline-flex`}>
                        <img src={item.gift_pic} alt={`红包`} referrerPolicy='no-referrer' loading='lazy' />
                      </picture>
                    </div>
                  )
                })}
            </span>{' '}
            {mode === 'obs' && <b className='red-envelope-start-details-name'>{data.message}</b>}
            <span className='red-envelope-start-details'>
              {' '}
              {mode === 'obs' ? (
                <span className={`${styles.giftDetailsPrice} gift-details-price`}>{priceDisplay}</span>
              ) : (
                <Tooltip label={tooltipLabel} triggerOptions={{ asChild: true, className: 'inline' }}>
                  <span className={`${styles.giftDetailsPrice} gift-details-price`}>{priceDisplay}</span>
                </Tooltip>
              )}
            </span>
            <Avatar uid={data.uid} avatar={avatarUrl} className={`${styles.hidden} avatar-alt-top`} />
            <span
              className={`${styles.giftIconWrap} ${styles.hidden} red-envelope-start-icon-wrap red-envelope-start-icon-wrap-alt-bottom`}
            >
              <picture className='inline-flex'>
                <img src={data.icon || '/red-envelope.webp'} alt={`红包`} referrerPolicy='no-referrer' loading='lazy' />
              </picture>
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
