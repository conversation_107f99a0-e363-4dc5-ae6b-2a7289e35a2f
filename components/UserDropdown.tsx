// ./components/UserDropdown.tsx

'use client'

import React, { useEffect, useMemo, useState } from 'react'
import { Speech } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import {
  IconBrandBilibili,
  IconBrandSteamFilled,
  IconBrandYoutubeFilled,
  IconExternalLink,
  IconFilter,
  IconStar,
  IconStarFilled,
} from '@tabler/icons-react'

import { Api } from '@/lib/const'
import { db } from '@/lib/db'
import type { LaplaceEvent } from '@/lib/event-parsers/types'
import useGlobalStore from '@/lib/store'

import { formatDate } from '@/utils/formatDate'
import { parseRichContent, type RichContent } from '@/utils/parseRichContent'
import { timeFromNow } from '@/utils/timeFromNow'
import { ttsManager } from '@/utils/ttsManager'

import { useCopyToClipboard } from '@/hooks/use-copy-to-clipboard'
import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import WealthMedal from '@/components/chat/wealth-medal'
import { MaskBackground } from '@/components/MaskBackground'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuIcon,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown'

interface UserDropdownProps {
  event: LaplaceEvent
  children: React.ReactNode
}

export default function UserDropdown({ event, children }: UserDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [shouldRenderRichContent, setShouldRenderRichContent] = useState(false)
  const [richContent, setRichContent] = useState<RichContent>()
  const [bookmarked, setBookmarked] = useState(false)

  const options = useOptions()
  const { uiLang, baseFontSize, useCst } = options
  const updateSearchQuery = useGlobalStore(state => state.updateSearchQuery)
  const { t } = useTranslation()
  const { isCopied, copyToClipboard } = useCopyToClipboard({ timeout: 200 })

  const avatarSize = useMemo(() => 320 * (baseFontSize / 20), [baseFontSize])

  const isTypeWithRichContent = event.type === 'message' || event.type === 'superchat'
  const isBookmarkableContent =
    event.type === 'message' ||
    event.type === 'interaction' ||
    event.type === 'user-block' ||
    event.type === 'effect-message'

  useEffect(() => {
    let isMounted = true

    async function loadRichContent() {
      if (shouldRenderRichContent) {
        if (isBookmarkableContent) {
          try {
            const existing = await db.eventItems.get(event.id)
            setBookmarked(!!existing)
          } catch (error) {
            console.error('Failed to check bookmark state:', error)
          }
        }

        if (isTypeWithRichContent) {
          try {
            const content = await parseRichContent(event.message)
            // console.log(`content`, content)
            if (isMounted) {
              setRichContent(content)
            }
          } catch (error) {
            console.error('Error parsing rich content:', error)
          }
        }
      }
    }
    loadRichContent()

    return () => {
      isMounted = false
    }
  }, [shouldRenderRichContent, isTypeWithRichContent, isBookmarkableContent, event])

  // Handle open/close states with animation timing
  const handleOpenChange = (open: boolean) => {
    if (open) {
      setShouldRenderRichContent(true)
    } else {
      // Keep content rendered during close animation
      setTimeout(() => {
        setShouldRenderRichContent(false)
      }, 300) // Match this with your dropdown's animation duration
    }
    setIsOpen(open)
  }

  const handleBookmarkToggle = async () => {
    // 不需要额外进行判断，下方已经对 event.type 进行了判断
    // if (event.type !== 'message' && event.type !== 'interaction') return

    try {
      if (bookmarked) {
        // Remove from bookmarks
        await db.eventItems.delete(event.id)
        setBookmarked(false)
        toast.success(t('Event removed from bookmarks'), {
          id: `bookmark-remove-${event.id}`,
        })
      } else {
        // Add to bookmarks
        await db.eventItems.put(event)
        setBookmarked(true)
        toast.success(t('Event saved to bookmarks'), {
          id: `bookmark-add-${event.id}`,
        })
      }
    } catch (error) {
      console.error('Failed to toggle bookmark:', error)
      toast.error(t('Failed to update bookmark status'), {
        id: `bookmark-error-${event.id}`,
      })
    }
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>

      <DropdownMenuContent align='start'>
        {'avatar' in event && event.avatar && <MaskBackground url={event.avatar} />}

        <div className='space-y-2 px-3 py-1.5'>
          <div className='flex items-start gap-2'>
            {'avatar' in event && event.avatar ? (
              <a href={event.avatar} target='_blank' rel='noopener noreferrer'>
                <Avatar
                  uid={event.uid}
                  avatar={`${event.avatar}@${avatarSize}w_${avatarSize}h`}
                  className='[--avatar-size:64px]'
                />
              </a>
            ) : (
              <a href={`${Api.Workers}/bilibili/avatar/${event.uid}`} target='_blank' rel='noopener noreferrer'>
                <Avatar uid={event.uid} avatar={undefined} className='[--avatar-size:64px]' />
              </a>
            )}
            <div className='flex flex-col gap-0.5'>
              <div className='flex items-center gap-1 text-base'>
                {'wealthMedalLevel' in event && <WealthMedal data={event.wealthMedalLevel} />}
                {'username' in event ? (
                  <div
                    className='cursor-pointer'
                    onClick={() => {
                      if (isCopied) return
                      const content = event.username
                      copyToClipboard(content)
                      toast.success(`${content} 已复制到剪切板`, {
                        id: `copy-uid-${content}`,
                      })
                    }}
                  >
                    <div className='font-semibold hover:font-mono hover:font-normal'>{event.username}</div>
                  </div>
                ) : (
                  <div className='font-semibold text-amber-500'>{t('Unknown username')}</div>
                )}
              </div>

              <div className='text-sm'>
                <code
                  className='cursor-pointer'
                  onClick={() => {
                    if (isCopied) return
                    const content = `UID:${event.uid}`
                    copyToClipboard(content)
                    toast.success(`${content} 已复制到剪切板`, {
                      id: `copy-username-${content}`,
                    })
                  }}
                >
                  UID:{event.uid}
                </code>
              </div>

              {'timestampNormalized' in event && event.timestampNormalized > 0 && (
                <div className='text-fg/60 text-sm font-normal'>
                  {formatDate(new Date(event.timestampNormalized), {
                    localTime: !useCst,
                    format: {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                    },
                  })}
                  <span className='opacity-50'>@</span>
                  {event.type !== 'online-rank-update'
                    ? timeFromNow(event.timestampNormalized, {
                        locale: uiLang === 'zh-Hans' ? 'zh-CN' : 'en-US',
                      })
                    : t('Guard expires {{date}}', {
                        date: timeFromNow(event.timestampNormalized, {
                          locale: uiLang === 'zh-Hans' ? 'zh-CN' : 'en-US',
                        }),
                      })}
                </div>
              )}
            </div>
          </div>
        </div>

        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => updateSearchQuery(`uid:${event.uid}`)}>
            <DropdownMenuIcon>
              <IconFilter />
            </DropdownMenuIcon>
            {t('Filter User')}
          </DropdownMenuItem>
        </DropdownMenuGroup>

        {isBookmarkableContent && (
          <DropdownMenuItem onClick={handleBookmarkToggle}>
            {bookmarked ? (
              <>
                <DropdownMenuIcon>
                  <IconStarFilled className='text-rose-500' />
                </DropdownMenuIcon>
                {t('Remove from Bookmarks')}
              </>
            ) : (
              <>
                <DropdownMenuIcon>
                  <IconStar />
                </DropdownMenuIcon>
                {t('Add to Bookmarks')}
              </>
            )}
          </DropdownMenuItem>
        )}

        {event.type !== 'online-rank-update' ? (
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={() => ttsManager.handleEvent(event, { force: true })}>
              <DropdownMenuIcon>
                <Speech />
              </DropdownMenuIcon>
              {t('Text to Speech')}
            </DropdownMenuItem>
          </DropdownMenuGroup>
        ) : null}

        {/* Rich content detection */}
        {isTypeWithRichContent &&
        (richContent?.bilibiliVideos?.length ||
          richContent?.bilibiliUsers?.length ||
          richContent?.steamGames?.length ||
          richContent?.youtubeVideos?.length) ? (
          <div>
            <DropdownMenuLabel>{t('Detected Media')}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {richContent?.bilibiliUsers.length
              ? richContent.bilibiliUsers.map((item, idx) => (
                  <DropdownMenuItem asChild className='text-fg' key={`${idx}-${item.id}`}>
                    <a href={item.url} target='_blank' rel='noopener noreferrer'>
                      <DropdownMenuIcon>
                        <Avatar uid={Number(item.id)} className='[--avatar-size:16px]' />
                      </DropdownMenuIcon>
                      UID:{item.id}
                    </a>
                  </DropdownMenuItem>
                ))
              : null}
            {richContent?.bilibiliVideos.length
              ? richContent.bilibiliVideos.map((item, idx) => (
                  <DropdownMenuItem asChild className='text-fg' key={`${idx}-${item.id}`}>
                    <a href={item.url} target='_blank' rel='noopener noreferrer'>
                      <DropdownMenuIcon>
                        <IconBrandBilibili />
                      </DropdownMenuIcon>
                      {item.id}
                    </a>
                  </DropdownMenuItem>
                ))
              : null}
            {richContent?.youtubeVideos.length
              ? richContent.youtubeVideos.map((item, idx) => (
                  <DropdownMenuItem asChild className='text-fg' key={`${idx}-${item.id}`}>
                    <a href={item.url} target='_blank'>
                      <DropdownMenuIcon>
                        <IconBrandYoutubeFilled />
                      </DropdownMenuIcon>
                      {item.id}
                    </a>
                  </DropdownMenuItem>
                ))
              : null}
            {richContent?.steamGames.length
              ? richContent.steamGames.map((item, idx) => (
                  <DropdownMenuItem asChild className='text-fg' key={`${idx}-${item.id}`}>
                    <a href={item.url} target='_blank'>
                      <DropdownMenuIcon>
                        <IconBrandSteamFilled />
                      </DropdownMenuIcon>
                      {item.id}
                    </a>
                  </DropdownMenuItem>
                ))
              : null}
          </div>
        ) : null}

        <DropdownMenuLabel>{t('External Tools')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild className='text-fg'>
            <a href={`https://space.bilibili.com/${event.uid}`} target='_blank' rel='noopener noreferrer'>
              <DropdownMenuIcon>
                <IconExternalLink />
              </DropdownMenuIcon>
              {t('Bilibili Space…')}
            </a>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className='text-fg'>
            <a href={`https://laplace.live/user/${event.uid}`} target='_blank'>
              <DropdownMenuIcon>
                <IconExternalLink />
              </DropdownMenuIcon>
              {t('User History…')}
            </a>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
