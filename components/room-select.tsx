import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { IconExternalLink, IconSearch, IconTrash, IconX } from '@tabler/icons-react'

import { useDebouncedValue } from '@/hooks/useDebouncedValue'
import type { RoomHistoryItem } from '@/hooks/useLocalStorage'
import { useLocalStorage } from '@/hooks/useLocalStorage'
import useRoomInfo from '@/hooks/useRoomInfo'

import { BiliibliUserWithRoom, BiliibliUserWithRoomSkeleton } from '@/components/biliibli-user-with-room'
import OptionLabel from '@/components/OptionLabel'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'

export function RoomSelect() {
  const [typingRoomId, setTypingRoomId] = useState('')
  const [open, setOpen] = useState(false)
  const [selectedRooms, setSelectedRooms] = useState<RoomHistoryItem[]>([])

  const { t } = useTranslation()

  const [debouncedTypingRoomId] = useDebouncedValue(typingRoomId, 400)
  const { roomInfo, isRoomInfoLoading, isRoomInfoError } = useRoomInfo(debouncedTypingRoomId)

  const [localStorageOptions, setLocalStorageOptions] = useLocalStorage()
  const { roomIds, roomSearchHistory } = localStorageOptions

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTypingRoomId(e.target.value.replace(/[^0-9]/g, ''))
  }

  function addRoomSearchHistory(array: RoomHistoryItem[] = [], newItems: RoomHistoryItem[]) {
    // Check if `newItems` exist in `RoomHistoryItem`, add if not
    newItems.forEach(newItem => {
      const valueExists = array.some(item => item.value.toString() === newItem.value)

      if (!valueExists) {
        array.push(newItem)
      }
    })

    // dedupe
    const dedupedArr = array
      .map(item => ({
        ...item,
        value: item.value.toString(),
        uid: item.uid.toString(),
      }))
      .filter((item, index, self) => index === self.findIndex(uniqueItem => uniqueItem.value === item.value))

    return dedupedArr
  }

  // Filter out selected rooms and current room if it's in the list
  const filteredRoomHistory = roomSearchHistory.filter(
    room =>
      !selectedRooms.some(selected => selected.value === room.value) &&
      (!roomInfo?.data?.room_info || room.value !== roomInfo.data.room_info.room_id.toString())
  )

  function RoomSearchResult() {
    if (!typingRoomId) return null

    if (isRoomInfoError) return <div className='p-2 text-sm text-rose-500'>载入失败</div>
    if (!roomInfo && isRoomInfoLoading)
      return (
        <div className='text-fg/60 px-3 py-1 text-sm'>
          <BiliibliUserWithRoomSkeleton />
        </div>
      )
    if (roomInfo?.code && roomInfo?.code > 0) return <div className='p-2 text-sm text-rose-500'>无效房间</div>

    const roomData = roomInfo?.data
    if (!roomData?.room_info || !roomData.anchor_info?.base_info) {
      return <div className='p-2 text-sm text-rose-500'>房间信息不完整</div>
    }

    // Check if room (either by room_id or short_id) is already selected
    const isRoomSelected = selectedRooms.some(room => {
      const roomId = roomData.room_info.room_id.toString()
      const shortId = roomData.room_info.short_id.toString()
      return room.value === roomId || (shortId !== '0' && room.value === shortId)
    })

    if (isRoomSelected) {
      return <div className='p-2 text-sm text-amber-500/60'>房间已添加</div>
    }

    const newRoom = {
      value: roomData.room_info.room_id.toString(),
      uid: roomData.room_info.uid.toString(),
      label: roomData.room_info.room_id.toString(),
      username: roomData.anchor_info.base_info.uname,
    }

    return (
      <div
        className='hover:bg-ac/10 flex cursor-pointer items-center gap-2 px-3 py-1'
        onClick={() => {
          handleRoomsSelectChange([...selectedRooms, newRoom])
          setOpen(false)
          setTypingRoomId('')
        }}
      >
        <BiliibliUserWithRoom
          uid={roomData.room_info.uid}
          username={roomData.anchor_info.base_info.uname}
          roomId={
            <>
              {roomData.room_info.room_id}
              {roomData.room_info.short_id !== 0 ? `（短房号：${roomData.room_info.short_id}）` : ``}
            </>
          }
        />
      </div>
    )
  }

  function handleRoomsSelectChange(rooms: RoomHistoryItem[]) {
    let roomSearchHistoryMerged = roomSearchHistory || []
    let realRoomIds = rooms.map(room => room.value)

    if (roomInfo?.code === 0 && roomInfo?.data?.room_info) {
      const roomInfoData = roomInfo.data
      // Replace short_id with original room_id
      realRoomIds = realRoomIds.map(room => {
        const parsedRoom = parseInt(room)
        if (parsedRoom === roomInfoData.room_info.short_id && roomInfoData.room_info.short_id !== 0) {
          return roomInfoData.room_info.room_id.toString()
        } else {
          return room
        }
      })

      roomSearchHistoryMerged = [
        ...roomSearchHistoryMerged,
        {
          value: roomInfoData.room_info.room_id.toString(),
          uid: roomInfoData.room_info.uid.toString(),
          label: roomInfoData.room_info.room_id.toString(),
          username: roomInfoData.anchor_info.base_info.uname,
        },
      ]
    }

    setSelectedRooms(rooms)
    setLocalStorageOptions({
      ...localStorageOptions,
      roomIds: realRoomIds,
      roomSearchHistory: addRoomSearchHistory(roomSearchHistory, roomSearchHistoryMerged),
    })
  }

  // Load saved rooms from localStorage on mount and when localStorage updates
  useEffect(() => {
    const roomsFromStorage = roomSearchHistory
      .filter(room => roomIds.includes(room.value))
      .sort((a, b) => {
        const indexA = roomIds.indexOf(a.value)
        const indexB = roomIds.indexOf(b.value)
        return indexA - indexB
      })

    setSelectedRooms(roomsFromStorage)
  }, [roomIds, roomSearchHistory])

  return (
    <div className='w-full'>
      <OptionLabel
        label={
          <div className='flex items-center gap-x-2'>
            <span className='cursor-default font-medium' onClick={() => setOpen(true)}>
              {t('Select Rooms')}
            </span>
            <a
              href='https://link.bilibili.com/p/center/index#/my-room/start-live'
              target='_blank'
              rel='noopener noreferrer'
            >
              <div className='flex items-center gap-x-1'>
                {t('Get Your Room ID')}
                <IconExternalLink size='1em' />
              </div>
            </a>
          </div>
        }
        desc={
          '输入目标直播间的房间号。靓号会被自动转成原始房间号。房间的历史记录只保存在浏览器本地，不会被记录或上传至服务器'
        }
      />
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div
            tabIndex={0}
            className='focus-ring data-[state=open]:border-ac mt-1 flex cursor-pointer flex-wrap gap-1 rounded-md border p-1 text-lg'
            onKeyDown={e => {
              // Handle Space and Enter keys
              if (e.key === 'Enter') {
                e.preventDefault() // Prevent page scroll on space
                e.currentTarget.click() // Trigger the popover
              }
            }}
          >
            {selectedRooms.length > 0 ? (
              selectedRooms.map(room => (
                <div key={room.value} className='bg-bg flex items-center gap-2 rounded-sm border p-2 py-1'>
                  <BiliibliUserWithRoom uid={room.uid} username={room.username || room.label} roomId={room.value} />
                  <Button
                    className='-mr-0.5 p-1 opacity-50'
                    variant='link'
                    onClick={e => {
                      e.preventDefault()
                      handleRoomsSelectChange(selectedRooms.filter(r => r.value !== room.value))
                    }}
                    aria-label='移除'
                  >
                    <IconX size='1.25rem' />
                  </Button>
                </div>
              ))
            ) : (
              <div className='text-fg/40 px-1.5 py-0.5'>{t('Please select rooms')}</div>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent className='max-h-[min(var(--radix-popover-content-available-height),350px)] w-[var(--radix-popover-trigger-width)] p-0'>
          <div className='shadow-border-b relative z-50 px-3'>
            <Input
              placeholder={t('Please enter room IDs here')}
              value={typingRoomId}
              onChange={handleSearchChange}
              className='border-none py-2 pl-6 text-base shadow-none inset-shadow-none focus-visible:ring-0'
              leftSection={<IconSearch className='size-4 shrink-0 opacity-50' />}
              leftSectionClassName='pointer-events-none'
            />
          </div>
          {/* https://github.com/radix-ui/primitives/issues/2307 */}
          <ScrollArea
            className='z-40'
            // 40px is the height of the input
            // 5px is the extra offset to avoid outer container overflow scroll
            viewportProps={{
              className: 'max-h-[calc(min(var(--radix-popover-content-available-height),350px)-40px-5px)]',
            }}
          >
            <div>
              {/* Room search result */}
              <RoomSearchResult />

              {/* Room history */}
              {filteredRoomHistory.length > 0 &&
                filteredRoomHistory.map(room => (
                  <div
                    tabIndex={0}
                    key={room.value}
                    className='focus-ring hover:bg-ac/10 flex cursor-pointer items-center gap-2 px-3 py-1'
                    aria-label={`${room.username}（${room.value}）`}
                    onClick={() => {
                      handleRoomsSelectChange([...selectedRooms, room])
                      setOpen(false)
                      setTypingRoomId('')
                    }}
                    onKeyDown={e => {
                      // Handle Space and Enter keys
                      if (e.key === 'Enter') {
                        e.preventDefault() // Prevent page scroll on space
                        e.currentTarget.click() // Trigger the popover
                      }
                    }}
                  >
                    <BiliibliUserWithRoom
                      uid={room.uid}
                      username={room.username || room.label}
                      roomId={room.value}
                      className='flex-auto'
                    />
                    <Button
                      onClick={e => {
                        e.stopPropagation()
                        setLocalStorageOptions({
                          ...localStorageOptions,
                          roomSearchHistory: roomSearchHistory.filter(item => item.value !== room.value),
                        })
                      }}
                      className='p-1.5'
                      size='sm'
                      tint='rose'
                      aria-label='移除'
                    >
                      <IconTrash className='size-4' />
                    </Button>
                  </div>
                ))}
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>
    </div>
  )
}
