import React, { memo, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'

import { TRANSLATABLE_LANGUAGES } from '@/lib/const'
import { db } from '@/lib/db'

import { nf } from '@/utils/numberFormat'
import { translateManager } from '@/utils/translateManager'

import { useLocalStorage } from '@/hooks/useLocalStorage'

import OptionLabel from '@/components/OptionLabel'
import { Alert } from '@/components/ui/alert'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SwitchWithLabel } from '@/components/ui/switch-with-label'

const TranslateControls = memo(function TranslateControls() {
  const [options, setOptions] = useState(translateManager.getOptions())
  const [translationCount, setTranslationCount] = useState(0)
  const [purging, setPurging] = useState(false)
  const { t } = useTranslation()
  const [localStorageOptions] = useLocalStorage()
  const [showTokenWarning, setShowTokenWarning] = useState(false)

  // Subscribe to options changes
  useEffect(() => {
    return translateManager.subscribe(newOptions => {
      setOptions(newOptions)
    })
  }, [])

  // Sync with global login token
  useEffect(() => {
    translateManager.refreshLoginSyncToken()
  }, [localStorageOptions.loginSyncToken])

  // Check if token is missing when translation is enabled
  useEffect(() => {
    if (options.translateEnabled && !localStorageOptions.loginSyncToken) {
      setShowTokenWarning(true)
    } else {
      setShowTokenWarning(false)
    }
  }, [options.translateEnabled, localStorageOptions.loginSyncToken])

  // Get translation count
  useEffect(() => {
    const fetchTranslationCount = async () => {
      try {
        // Count translations in the database
        const count = await db.translations.count()
        setTranslationCount(count)
      } catch (error) {
        console.error('Failed to get translation count:', error)
      }
    }

    fetchTranslationCount()

    // Set up an interval to refresh the count
    const interval = setInterval(fetchTranslationCount, 30000) // refresh every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const handleOptionsUpdate = (updates: Partial<typeof options>) => {
    const newOptions = { ...options, ...updates }
    translateManager.setOptions(newOptions)

    // Clear translation cache when settings change
    if (updates.translateEnabled !== undefined) {
      // If translation is being toggled, clear all cache
      translateManager.clearCache()
    } else if (updates.translateTo !== undefined && updates.translateTo !== options.translateTo) {
      // If language is changed, only clear cache for the specific language
      translateManager.clearLanguageCache(updates.translateTo)
    }
  }

  const handlePurgeTranslations = async () => {
    setPurging(true)

    try {
      // Clear in-memory cache
      translateManager.clearCache()

      // Clear database cache
      await db.translations.clear()

      toast.success('Translation cache cleared successfully')

      // Get updated count
      const count = await db.translations.count()
      setTranslationCount(count)
    } catch (error) {
      toast.error('Failed to clear translation cache')
      console.error('Failed to clear translation cache:', error)
    } finally {
      setPurging(false)
    }
  }

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex h-6 flex-wrap items-center justify-between gap-1'>
        <SwitchWithLabel
          label={
            <OptionLabel
              label={t('chatTranslation.label')}
              desc={t('chatTranslation.desc')}
              // refLink='https://subspace.institute/docs/laplace-chat/translation'
              refLinkLabel={t('chatTranslation.refLinkLabel')}
            />
          }
          checked={options.translateEnabled}
          onChange={e => handleOptionsUpdate({ translateEnabled: e.target.checked })}
        />

        <div className='flex items-center gap-2'>
          <div className='text-fg/60 inline-flex text-sm leading-none'>
            {t('chatTranslation.cached', { value: nf.format(translationCount) })}
          </div>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button tint='rose' variant='outline' size='sm' loading={purging} disabled={translationCount === 0}>
                {t('Purge…')}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Purge Translation Cache</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to clear all cached translations? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel asChild>
                  <Button variant='link'>{t('Cancel')}</Button>
                </AlertDialogCancel>
                <AlertDialogAction asChild onClick={handlePurgeTranslations}>
                  <Button tint='rose'>{t('Confirm')}</Button>
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {showTokenWarning && (
        <Alert tint='warning' label={t('loginSyncToken.required')}>
          {t('loginSyncToken.requiredDesc')}
        </Alert>
      )}

      <div className='grid grid-cols-1 items-start gap-2 @xs:grid-cols-2'>
        <div className='grid w-full content-start gap-y-2'>
          <Label htmlFor='chat-translate-to'>
            <OptionLabel label={t('Translate To…')} />
          </Label>

          <Select value={options.translateTo} onValueChange={value => handleOptionsUpdate({ translateTo: value })}>
            <SelectTrigger id='chat-translate-to'>
              <SelectValue placeholder='Select language' />
            </SelectTrigger>
            <SelectContent>
              {TRANSLATABLE_LANGUAGES.map(language => (
                <SelectItem key={language.value} value={language.value}>
                  {language.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
})

export default TranslateControls
