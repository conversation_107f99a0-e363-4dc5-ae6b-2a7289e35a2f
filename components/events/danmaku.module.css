@layer template {
  .event {
    --fallback-text: var(--event-danmaku-text, var(--event-message-text, var(--text-color)));
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.125) calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    border-radius: var(--event-border-radius);
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--fallback-text);
  }

  .meta {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--event-font-size) * 0.25);
    margin-right: calc(var(--event-font-size) * 0.5);
  }

  .read {
    opacity: 0.5 !important;
    filter: grayscale(0.8) !important;
  }

  /* 白字 */
  .guardLevel0 {
    color: var(--event-danmaku-text-0, var(--fallback-text));
    background-color: var(--event-danmaku-bg-0, transparent);
    --username-text: var(--text-color);

    .username {
      color: var(--event-username-text-0, var(--event-username-color-t0, var(--username-text)));
    }
  }

  /* 总督 */
  .guardLevel1 {
    color: var(--event-danmaku-text-1, var(--fallback-text));
    background-color: var(--event-danmaku-bg-1, transparent);
    /* background-color: var(--orange-20); */
    --username-text: var(--orange-dark);

    .username {
      color: var(--event-username-text-1, var(--event-username-color-t1, var(--username-text)));
    }

    &.dark {
      --username-text: var(--orange-light);
    }
  }

  /* 提督 */
  .guardLevel2 {
    color: var(--event-danmaku-text-2, var(--fallback-text));
    background-color: var(--event-danmaku-bg-2, transparent);
    /* background-color: var(--purple-20); */
    --username-text: var(--purple-dark);

    .username {
      color: var(--event-username-text-2, var(--event-username-color-t2, var(--username-text)));
    }

    &.dark {
      --username-text: var(--purple-light);
    }
  }

  /* 舰长 */
  .guardLevel3 {
    color: var(--event-danmaku-text-3, var(--fallback-text));
    background-color: var(--event-danmaku-bg-3, transparent);
    /* background-color: var(--blue-20); */
    --username-text: var(--blue-dark);

    .username {
      color: var(--event-username-text-3, var(--event-username-color-t3, var(--username-text)));
    }

    &.dark {
      --username-text: var(--blue-light);
    }
  }

  .username {
    font-weight: bold;
    display: flex;
    align-items: center;
    word-break: break-word;
  }

  .userLevel {
    display: none;
    border: var(--1px) solid;
    padding: 0 calc(var(--1px) * 3);
    border-radius: calc(var(--1px) * 2);
    line-height: 1.2;
    font-size: calc(var(--1px) * 14);
    white-space: nowrap;
    background: var(--white-40);
  }

  .message {
    display: inline;
    align-items: center;
    line-height: var(--event-line-height);
    vertical-align: top;
    word-break: break-word;
  }

  .emoteWrap {
    display: inline-flex;
    vertical-align: top;
  }

  .emote {
    max-width: calc(var(--event-line-height) * 4);
    max-height: calc(var(--event-line-height) * 1);
    text-indent: -9999px;
  }

  .reply {
    --avatar-size: 1.2em;
    margin-right: 0.25em;
    color: var(--event-reply-text, var(--pink-light));
  }

  .replyUsername {
    font-weight: bold;
  }

  /* 当前榜1、2、3 */
  .currentRank {
    --fallback-color: var(--event-danmaku-current-rank-text, #000);
    color: var(--fallback-color);
    background: var(--yellow);
    padding: calc(var(--1px) * 2) calc(var(--1px) * 4);
    border-radius: calc(var(--1px) * 4);
    font-size: calc(var(--1px) * 12);
    line-height: 1;
    white-space: nowrap;
    font-weight: bold;
  }

  .currentRank1 {
    color: var(--event-danmaku-current-rank-text-1, var(--fallback-color));
    background: var(--event-danmaku-current-rank-bg-1, var(--yellow));
  }

  .currentRank2 {
    color: var(--event-danmaku-current-rank-text-2, var(--fallback-color));
    background: var(--event-danmaku-current-rank-bg-2, #ddd);
  }

  .currentRank3 {
    color: var(--event-danmaku-current-rank-text-3, var(--fallback-color));
    background: var(--event-danmaku-current-rank-bg-3, rgb(253, 180, 146));
  }

  .modBadge {
    color: var(--event-danmaku-mod-text, var(--blue));
    width: var(--event-line-height);
    height: var(--event-line-height);
    flex-shrink: 0;
  }

  .streamerBadge {
    color: var(--event-danmaku-streamer-text, var(--orange));
    width: var(--event-line-height);
    height: var(--event-line-height);
    flex-shrink: 0;
  }

  .effectMessageIconWrap {
    vertical-align: top;

    img {
      max-width: var(--event-line-height);
      max-height: var(--event-line-height);
      text-indent: -9999px;
    }
  }

  .hidden {
    display: none;
  }
}
