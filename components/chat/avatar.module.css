@layer components {
  .wrap {
    position: relative;
    width: var(--avatar-size, var(--event-line-height));
    height: var(--avatar-size, var(--event-line-height));
    flex-shrink: 0;
  }

  /* avatar image */
  .avatar {
    /*
      强制让头像图片以 inline 显示，可以更好的让弹幕中的头像可以和文本对齐，并覆盖 tailwind 的 @base reset
      由于 laplace-chat 项目的特殊性，这里的 inline 已经在 global.css 中定义了
    */
    /* display: inline; */
    width: var(--avatar-size, var(--event-line-height));
    height: var(--avatar-size, var(--event-line-height));
    border-radius: 50%;
    background-color: var(--text-color-10);
    text-indent: -9999px;
    vertical-align: top;
  }

  .avatarFrame {
    --resolved-avatar: var(--avatar-size, var(--event-line-height));
    --resolved-size: calc(var(--resolved-avatar) * var(--frame-scale-factor, 1.2));
    --resolved-offset: calc((var(--resolved-size) - var(--resolved-avatar)) / -2);

    position: absolute;
    width: var(--resolved-size);
    height: var(--resolved-size);
    transform: translateX(var(--resolved-offset)) translateY(var(--resolved-offset));

    /* Old method, which does not work well with large avatar sizes */
    /* top: calc(var(--frame-offset, 0.4em) * -0.5);
    left: calc(var(--frame-offset, 0.4em) * -0.5);
    width: calc(var(--avatar-size, var(--event-line-height)) + calc(var(--frame-offset, 0.4em) * 1));
    height: calc(var(--avatar-size, var(--event-line-height)) + calc(var(--frame-offset, 0.4em) * 1)); */
    background-size: cover;

    /* avoid capping by global style */
    max-width: unset;
    max-height: unset;
    z-index: 4;
    pointer-events: none;
  }

  .avatarImgWrap {
    line-height: var(--avatar-size, var(--event-line-height));
  }

  .guardLevel0 {
    background-color: transparent;
  }

  /* 总督 */
  .guardLevel1 {
    /* background-color: var(--orange-20); */

    &,
    &.frameRank1,
    &.perkLevel1 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-1.png);
    }

    &.frameRank2,
    &.perkLevel2 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-1-1k.png);
    }

    &.frameRank3,
    &.perkLevel3 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-1-10k.png);
    }

    .avatar {
      box-shadow: 0 0 0 var(--1px) var(--event-username-text-1, var(--event-username-color-t1, var(--orange)));
    }

    .avatarFrame {
      background-image: var(--avatar-frame);
    }
  }

  /* 提督 */
  .guardLevel2 {
    /* background-color: var(--purple-20); */

    &,
    &.frameRank1,
    &.perkLevel1 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-2.png);
    }

    &.frameRank2,
    &.perkLevel2 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-2-1k.png);
    }

    &.frameRank3,
    &.perkLevel3 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-2-10k.png);
    }

    .avatar {
      box-shadow: 0 0 0 var(--1px) var(--event-username-text-2, var(--event-username-color-t2, var(--purple)));
    }

    .avatarFrame {
      background-image: var(--avatar-frame);
    }
  }

  /* 舰长 */
  .guardLevel3 {
    /* background-color: var(--blue-20); */

    &,
    &.frameRank1,
    &.perkLevel1 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-3.png);
    }

    &.frameRank2,
    &.perkLevel2 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-3-1k.png);
    }

    &.frameRank3,
    &.perkLevel3 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/frame-3-10k.png);
    }

    .avatar {
      box-shadow: 0 0 0 var(--1px) var(--event-username-text-3, var(--event-username-color-t3, var(--blue)));
    }

    .avatarFrame {
      background-image: var(--avatar-frame);
    }
  }

  .guardLeader1 {
    &,
    &.frameRank1,
    &.frameRank2,
    &.frameRank3,
    &.perkLevel1,
    &.perkLevel2,
    &.perkLevel3 {
      --avatar-frame: url(https://rsrc.laplace.cn/assets/guard-icons/commander.png);
    }

    .avatarFrame {
      background-image: var(--avatar-frame);
    }
  }

  .isLive {
    .avatar {
      border: var(--pink) 1px solid;
    }

    &::before {
      position: absolute;
      display: block;
      content: '';
      width: 100%;
      height: 100%;
      border-radius: 100%;
      background-color: var(--pink);
      opacity: 0;
      transform: scale(1);
      animation: liveRadar 2s ease infinite;
      z-index: -1;
    }
  }

  @keyframes liveRadar {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
      transform: scale(1.6);
    }
  }
}
