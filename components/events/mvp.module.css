@layer template {
  .event {
    --avatar-size: calc(var(--event-font-size) * 2);
    --gift-size: calc(var(--event-font-size) * 2);
    --fallback-text: var(--event-mvp-text, var(--event-message-text, #6e4831));
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--fallback-text);
  }

  .read {
    opacity: 0.5 !important;
    filter: grayscale(0.8) !important;
  }

  .eventSizeHighlight {
    --gift-size: calc(var(--event-font-size) * 3.75);

    font-size: calc(var(--event-font-size) * 1.125);
    border-radius: var(--event-border-radius);
    color: var(--event-mvp-text, #6e4831);

    @container (max-width: 320px) {
      --gift-size: calc(var(--event-font-size) * 2);
    }

    .content {
      position: relative;
      display: grid;
      padding-right: calc(var(--avatar-size) * 1.25);
      gap: calc(var(--event-font-size) * 0.25);
    }

    .giftDetailsPrice {
      display: none;
    }

    .top {
      gap: calc(var(--event-font-size) * 0.375);
    }

    .giftIconWrap {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .event.showAs_sticky {
    --avatar-size: var(--event-line-height);
    --gift-size: calc(var(--event-line-height) * 1.25);
    --event-border-radius: calc(var(--1px) * 100);

    &.eventSizeHighlight {
      font-size: var(--event-font-size);
      padding: calc(var(--event-font-size) * 0.25);
      padding-right: calc(var(--event-font-size) * 0.5);

      .giftIconWrap {
        top: calc(var(--1px) * -3);
      }
    }
  }

  .username {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    word-break: break-word;

    a {
      color: inherit;
    }
  }

  .message {
    line-height: var(--event-line-height);
    word-break: break-word;
  }

  .top {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--event-font-size) * 0.25);
    line-height: var(--event-line-height);
    font-weight: bold;
  }

  .giftDetailsSymbol {
    opacity: 0.6;
  }

  .giftDetailsFigure {
    opacity: 0.6;
  }

  .giftIconWrap {
    display: inline-block;
    /* This help avoid inline image height issues */
    max-height: var(--gift-size);
    pointer-events: none;
    flex-shrink: 0;

    img {
      width: var(--gift-size);
      height: var(--gift-size);
    }
  }

  .mvpRank1 {
    color: var(--event-mvp-text-1, var(--fallback-text));
    background: var(--event-mvp-bg-1, #e7d6a9);
  }
  .mvpRank2 {
    color: var(--event-mvp-text-2, var(--fallback-text));
    background: var(--event-mvp-bg-2, #e7d6a9);
  }
  .mvpRank3 {
    color: var(--event-mvp-text-3, var(--fallback-text));
    background: var(--event-mvp-bg-3, #e7d6a9);
  }
  .mvpRank4 {
    color: var(--event-mvp-text-4, var(--fallback-text));
    background: var(--event-mvp-bg-4, #e7d6a9);
  }
  .mvpRank5 {
    color: var(--event-mvp-text-5, var(--fallback-text));
    background: var(--event-mvp-bg-5, #e7d6a9);
  }
  .mvpRank6 {
    color: var(--event-mvp-text-6, var(--fallback-text));
    background: var(--event-mvp-bg-6, #e7d6a9);
  }

  .priceCurrency {
    opacity: 0.8;
    font-weight: normal;
  }

  .priceFigure {
    font-weight: bold;
  }

  .hidden {
    display: none;
  }
}
