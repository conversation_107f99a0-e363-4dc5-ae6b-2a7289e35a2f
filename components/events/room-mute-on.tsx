import React from 'react'
import clsx from 'clsx'

import type { RoomMuteOn } from '@/lib/event-parsers/types/room-mute-on'

import type { EventModeProps } from '@/components/event'

import styles from './room-mute-on.module.css'

export interface RoomMuteOnProps extends React.HTMLAttributes<HTMLDivElement> {
  data: RoomMuteOn
  mode: EventModeProps
}

export default function RoomMuteOnItem({ data, mode, ...rest }: RoomMuteOnProps) {
  return (
    <div
      className={clsx(
        styles.event,
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <div className='message'>{data.message}</div>
    </div>
  )
}
