import { memo } from 'react'

import type { LaplaceEvent } from '@/lib/event-parsers/types'

import DanmakuItem from './events/danmaku'
import EffectDanmakuItem from './events/effect-danmaku'
import GiftItem from './events/gift'
import InteractionItem from './events/interaction'
import LikeClickItem from './events/like-click'
import LiveCutoffItem from './events/live-cutoff'
import LiveEndItem from './events/live-end'
import LiveStartItem from './events/live-start'
import LiveWarningItem from './events/live-warning'
import LotteryResultItem from './events/lottery-result'
import LotteryStartItem from './events/lottery-start'
import MvpItem from './events/mvp'
import NoticeItem from './events/notice'
import RedEnvelopeResultItem from './events/red-envelope-result'
import RedEnvelopeStartItem from './events/red-envelope-start'
import RoomMuteOffItem from './events/room-mute-off'
import RoomMuteOnItem from './events/room-mute-on'
import RoomNameUpdateItem from './events/room-name-update'
import SuperChatItem from './events/superchat'
import SystemItem from './events/system'
import ToastItem from './events/toast'
import UserBlockItem from './events/user-block'

/**
 * `sticky`: sticky event used by OBS mode
 * `normal`: general event form
 */
export type AsComponentProps = 'sticky' | 'normal' | 'full'
/**
 * Define component mode, usally get from next/route
 */
export type EventModeProps = 'obs' | 'dashboard'

export interface EventProps extends React.HTMLAttributes<HTMLDivElement> {
  data: LaplaceEvent
  mode?: EventModeProps
  as?: AsComponentProps
}

function EventItemComponent({ data, mode = 'obs', as = 'normal', ...rest }: EventProps) {
  if (data.type === 'system') {
    return <SystemItem data={data} {...rest} />
  }

  if (data.type === 'interaction') {
    return <InteractionItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'message') {
    return <DanmakuItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'toast') {
    return <ToastItem data={data} mode={mode} as={as} {...rest} />
  }

  if (data.type === 'gift') {
    return <GiftItem data={data} mode={mode} as={as} {...rest} />
  }

  if (data.type === 'superchat') {
    return <SuperChatItem data={data} mode={mode} as={as} {...rest} />
  }

  if (data.type === 'user-block') {
    return <UserBlockItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'live-start') {
    return <LiveStartItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'live-end') {
    return <LiveEndItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'live-warning') {
    return <LiveWarningItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'live-cutoff') {
    return <LiveCutoffItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'mvp') {
    return <MvpItem data={data} mode={mode} as={as} {...rest} />
  }

  if (data.type === 'like-click') {
    return <LikeClickItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'red-envelope-start') {
    return <RedEnvelopeStartItem data={data} mode={mode} as={as} {...rest} />
  }

  if (data.type === 'red-envelope-result') {
    return <RedEnvelopeResultItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'lottery-start') {
    return <LotteryStartItem data={data} mode={mode} as={as} {...rest} />
  }

  if (data.type === 'lottery-result') {
    return <LotteryResultItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'notice') {
    return <NoticeItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'effect-message') {
    return <EffectDanmakuItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'room-name-update') {
    return <RoomNameUpdateItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'room-mute-on') {
    return <RoomMuteOnItem data={data} mode={mode} {...rest} />
  }

  if (data.type === 'room-mute-off') {
    return <RoomMuteOffItem data={data} mode={mode} {...rest} />
  }

  return null
}

// The memo can prevent the danmaku/message list from stucking when scroll quickly with the scrollbar thumb
const EventItem = memo(EventItemComponent)

export default EventItem
