import { useMemo } from 'react'
import clsx from 'clsx'
import { Flipped, Flipper } from 'react-flip-toolkit'
import { useTranslation } from 'react-i18next'

import type { DashboardRealtimeInfo } from '@/types'

import useGlobalStore from '@/lib/store'

import { getPerkLevel } from '@/utils/eventAssetsUtils'
import { nf } from '@/utils/numberFormat'
import { timeFromNow } from '@/utils/timeFromNow'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import { LiveDuration } from '@/components/DashboardLiveDuration'
import AnimatedNumber from '@/components/ui/animated-numbers'
import UserDropdown from '@/components/UserDropdown'

interface UserMetaInfoProps {
  data: DashboardRealtimeInfo
  extra?: boolean
  showAsGrid?: boolean
  dashboardShowMetrics?: boolean
  dashboardShowTopRankUsers?: boolean
}

export function UserMetaInfo({
  data,
  extra,
  showAsGrid,
  dashboardShowMetrics,
  dashboardShowTopRankUsers,
}: UserMetaInfoProps) {
  const { t } = useTranslation()
  const options = useOptions()
  const { uiLang } = options
  const dashboardLiveGuards = useGlobalStore(state => state.dashboardLiveGuards)

  // Memoize these values to prevent recalculation on every render
  const wrapStyles = useMemo(
    () =>
      showAsGrid
        ? 'grid items-center gap-x-1 gap-y-0.5 leading-none text-[18px] min-w-12'
        : 'grid items-center gap-x-1 leading-4 text-sm',
    [showAsGrid]
  )

  const labelStyles = useMemo(
    () => (showAsGrid ? 'text-[12px] text-fg/60' : 'text-[11px] text-fg/60 leading-none'),
    [showAsGrid]
  )
  const numberStyles = 'font-logo font-semibold tabular-nums'

  return (
    <div className={showAsGrid ? 'flex flex-col gap-y-2' : 'flex gap-x-2'}>
      <div
        className={
          showAsGrid ? 'mt-1 flex flex-wrap items-center gap-x-4 gap-y-2' : 'flex flex-wrap items-center gap-x-2'
        }
      >
        {dashboardShowMetrics && (
          <>
            <div className={wrapStyles}>
              <span className={labelStyles}>{t('guards')}</span>
              <AnimatedNumber value={data.guards || 0} className={numberStyles} />
            </div>

            {data.fansclub ? (
              <div className={wrapStyles}>
                <span className={labelStyles}>{data.medalName || t('fans')}</span>
                <AnimatedNumber value={data.fansclub} className={numberStyles} />
              </div>
            ) : null}

            {data.online && data.online > 0 ? (
              <div className={wrapStyles}>
                <span className={labelStyles}>{t('viewers')}</span>
                <AnimatedNumber value={data.online} className={numberStyles} />
              </div>
            ) : null}

            {extra && data.watched ? (
              <div className={wrapStyles}>
                <span className={labelStyles}>{t('watched')}</span>
                <AnimatedNumber value={data.watched} className={numberStyles} />
              </div>
            ) : null}

            {extra && data.likes ? (
              <div className={wrapStyles}>
                <span className={labelStyles}>{t('likes')}</span>
                <AnimatedNumber value={data.likes} className={numberStyles} />
              </div>
            ) : null}
          </>
        )}
        {data.liveStartTime && data.liveStartTime > 0 ? (
          <div className={wrapStyles}>
            <span className={labelStyles}>{t('duration')}</span>
            <LiveDuration startTime={data.liveStartTime} />
          </div>
        ) : null}
      </div>

      {dashboardShowMetrics && dashboardShowTopRankUsers !== false && data.onlineRank && data.onlineRank.length > 0 ? (
        <Flipper
          className={showAsGrid ? 'grid grid-cols-2 gap-x-4 gap-y-1.5' : 'hidden gap-x-1 md:flex xl:gap-x-3'}
          flipKey={showAsGrid ? data.onlineRank.slice(0, 6) : data.onlineRank.slice(0, 3)}
          spring={{ stiffness: 1000, damping: 100 }}
        >
          {(showAsGrid ? data.onlineRank.slice(0, 6) : data.onlineRank.slice(0, 3)).map(item => (
            <Flipped key={item.uid} flipId={item.uid}>
              <div className={'flex items-center gap-x-2'}>
                <UserDropdown
                  event={{
                    type: 'online-rank-update',
                    id: '',
                    origin: 0,
                    originIdx: 0,
                    uid: item.uid,
                    username: item.uname,
                    timestamp: 0,
                    timestampNormalized: item.uinfo.guard?.expired_str ? +new Date(item.uinfo.guard?.expired_str) : 0,
                    message: '',
                    list: [],
                    read: false,
                  }}
                >
                  <div className='flex'>
                    <Avatar
                      className='cursor-pointer [--avatar-size:26px]'
                      uid={item.uid}
                      guard={item.guard_level}
                      avatar={item.face}
                      perkLevel={getPerkLevel(dashboardLiveGuards, {
                        roomId: data.room,
                      })}
                      // 与其他大航海指挥官的判断不一样，这里直接从返回的 uinfo 中判断
                      guardLeader={item.uinfo.uhead_frame?.id === 1750}
                    />
                  </div>
                </UserDropdown>

                {/* <span>{item.rank}</span> */}

                <div className='flex flex-auto flex-col'>
                  <div className={showAsGrid ? 'flex flex-auto items-center gap-x-1' : 'grid items-center'}>
                    <span className={clsx('flex-auto', showAsGrid ? 'flex' : 'flex text-[11px] leading-none')}>
                      <span
                        className={
                          showAsGrid
                            ? 'max-w-[120px] truncate'
                            : 'text-fg/60 hidden max-w-[60px] truncate lg:inline xl:max-w-[90px]'
                        }
                      >
                        {item.uname}
                      </span>
                    </span>

                    <span
                      className={clsx(
                        showAsGrid ? 'font-logo' : 'font-logo hidden text-sm leading-4 font-semibold lg:inline'
                      )}
                    >
                      ¥{nf.format(Number(item.score) / 10)}
                    </span>
                  </div>

                  {showAsGrid && item.uinfo.guard?.expired_str ? (
                    <span className='text-fg/60 text-[11px] leading-none'>
                      {/* {item.uinfo.guard?.expired_str} */}
                      {t('Guard expires {{date}}', {
                        date: timeFromNow(+new Date(item.uinfo.guard?.expired_str), {
                          locale: uiLang === 'zh-Hans' ? 'zh-CN' : 'en-US',
                        }),
                      })}
                    </span>
                  ) : null}
                </div>
              </div>
            </Flipped>
          ))}
        </Flipper>
      ) : null}
    </div>
  )
}
