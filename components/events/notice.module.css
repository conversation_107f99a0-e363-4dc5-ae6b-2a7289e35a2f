@layer template {
  .event {
    --fallback-text: var(--event-notice-text, var(--event-message-text, var(--text-color)));
    --event-border-radius: calc(var(--1px) * 6);

    /* 此图标尺寸不能按照 gift 的尺寸来算，而更接近弹幕中 emote 的显示效果 */
    --icon-size: calc(var(--event-line-height) * 0.75);

    padding: calc(var(--event-font-size) * 0.125) calc(var(--event-font-size) * 0.5);
    margin: calc(var(--event-font-size) * 0.25) 0;
    border-radius: var(--event-border-radius);
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--fallback-text);
  }

  .noticeIconWrap {
    display: inline-block;
    /* This help avoid inline image height issues */
    max-height: var(--icon-size);
    pointer-events: none;
    flex-shrink: 0;
    vertical-align: top;

    img {
      width: var(--icon-size);
      height: var(--icon-size);
    }
  }
}
