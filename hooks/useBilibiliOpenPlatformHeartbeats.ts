import useSWR, { type Fetcher } from 'swr'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

// Updated fetcher function with TypeScript support for fetching multiple URLs
const fetcher = async (urls: string[]) => {
  const f: Fetcher<BilibiliInternal.HTTPS.OpenPlatform.Heartbeat> = (url: string) => fetch(url).then(r => r.json())

  return Promise.allSettled(urls.map(url => f(url))).then(results =>
    results
      .filter(
        (result): result is PromiseFulfilledResult<BilibiliInternal.HTTPS.OpenPlatform.Heartbeat> =>
          result.status === 'fulfilled'
      )
      .map(result => result.value)
  )
}

/**
 * Same as `useBilibiliOpenPlatformHeartbeat` but fetch more than one room at a time
 * @param gameIds
 * @returns
 */
function useBilibiliOpenPlatformHeartbeats(gameIds: string[]) {
  const sanitizedUrls = gameIds.map(gameId => `${Api.Heartbeater}/bilibili-open/heartbeat/${gameId}`)

  const { data, error, isLoading } = useSWR(gameIds && gameIds.length > 0 ? sanitizedUrls : null, fetcher, {
    refreshInterval: 5 * 1000, // 5 seconds
    refreshWhenHidden: true,
    revalidateIfStale: true,
    revalidateOnFocus: true,
    revalidateOnReconnect: true,
  })

  return {
    heartbeats: data,
    isHeartbeatsLoading: isLoading,
    isHeartbeatsError: error,
  }
}

export default useBilibiliOpenPlatformHeartbeats
