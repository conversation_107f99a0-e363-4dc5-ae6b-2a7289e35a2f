import { IconMoon, IconSun } from '@tabler/icons-react'

import { useLocalStorage } from '@/hooks/useLocalStorage'

import { SwitchWithLabel } from '@/components/ui/switch-with-label'
import type { SwitchWithLabelProps } from '@/components/ui/switch-with-label'

export default function ColorSchemeSwitcher({ ...props }: SwitchWithLabelProps) {
  const [localStorage, setLocalStorage] = useLocalStorage()
  const { colorScheme } = localStorage

  return (
    <SwitchWithLabel
      size='lg'
      checked={colorScheme === 'dark' || false}
      onChange={e => {
        setLocalStorage({
          ...localStorage,
          colorScheme: e.currentTarget.checked ? 'dark' : 'light',
        })
      }}
      onLabel={<IconSun className='text-bg' size='.85rem' stroke={1.5} />}
      offLabel={<IconMoon size='.85rem' stroke={1.5} />}
      aria-label='切换配色'
      {...props}
    />
  )
}
