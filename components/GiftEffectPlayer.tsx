'use client'

import { useEffect, useRef, useState } from 'react'
import { toast } from 'sonner'

import { cn } from '@/lib/cn'
import { playAnimation } from '@/lib/gift-effects/player'
import { useGiftEffectStore } from '@/lib/gift-effects/store'

function GiftEffectPlayer({
  roomId,
  mode = 'obs',
  showLayer = false,
}: {
  roomId: number | undefined
  mode?: 'obs' | 'dashboard'
  /** show layer for debugging */
  showLayer?: boolean
}) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const hasLoggedError = useRef(false)
  const [isVisible, setIsVisible] = useState(false)

  const { queue, isPlaying, setPlaying, removeFromQueue, fetchEffectConfig, effectConfig, error } = useGiftEffectStore()

  // Fetch effect config on component mount
  useEffect(() => {
    if (roomId && !effectConfig) {
      fetchEffectConfig(roomId).catch(err => {
        toast.error('Failed to fetch effect config', {
          description: err.message,
        })
      })
    }
  }, [effectConfig, fetchEffectConfig, roomId])

  // Process the queue whenever the queue or playing state changes
  useEffect(() => {
    if (queue.length > 0 && !isPlaying && canvasRef.current && videoRef.current && containerRef.current) {
      const currentEffect = queue[0]!
      const containerWidth = containerRef.current.clientWidth
      const containerHeight = containerRef.current.clientHeight

      const playNextAnimation = async () => {
        try {
          setIsVisible(true)
          setPlaying(true)

          await playAnimation(canvasRef.current!, videoRef.current!, currentEffect, containerWidth, containerHeight)

          // Update play count or remove from queue
          currentEffect.playbackCount++
          if (currentEffect.playbackCount >= currentEffect.amount) {
            removeFromQueue(0)
          }

          setPlaying(false)
        } catch (err) {
          console.warn('Failed to play gift effect', err)
          removeFromQueue(0)
          setPlaying(false)
          toast.warning('Failed to play gift effect', {
            description: err instanceof Error ? err.message : String(err),
            id: 'gift-effect-play-error',
          })
        } finally {
          setIsVisible(false)
        }
      }

      playNextAnimation()
    }
  }, [queue, isPlaying, removeFromQueue, setPlaying])

  if (error) {
    // Only log the error once to prevent console spamming
    if (!hasLoggedError.current) {
      console.warn('Gift effect warning:', error)
      hasLoggedError.current = true
    }
    return null
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        'gift-effect-wrap pointer-events-none z-40 flex max-w-[calc(100dvw-1rem)] items-center justify-center overflow-hidden rounded-br-sm transition-opacity duration-200',
        mode === 'obs' ? 'absolute inset-0' : 'fixed top-0 right-2 bottom-2 w-full md:w-1/3',
        isVisible ? 'opacity-100' : showLayer ? 'opacity-100' : 'opacity-0'
      )}
    >
      <div
        className={cn(
          'gift-effect-positioner relative flex h-full w-full items-center justify-center',
          showLayer && 'bg-red-400/00'
        )}
      >
        <video ref={videoRef} className='absolute size-0 opacity-0' playsInline />
        <canvas
          ref={canvasRef}
          className={cn('gift-effect-canvas max-h-full max-w-full', showLayer && 'bg-green-400/20')}
        />
      </div>
    </div>
  )
}

export { GiftEffectPlayer }
