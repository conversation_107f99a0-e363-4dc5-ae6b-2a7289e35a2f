import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { useTranslation } from 'react-i18next'
import useLocalStorageState from 'use-local-storage-state'
import { IconExternalLink, IconSearch, IconTrash, IconX } from '@tabler/icons-react'

import { arrayToString } from '@/utils/arrayToString'

import useBilibiliOpenPlatformAuth from '@/hooks/useBilibiliOpenPlatformAuth'
import { useDebouncedValue } from '@/hooks/useDebouncedValue'
import type { RoomHistoryItemOpenPlatform } from '@/hooks/useLocalStorage'

import { BiliibliUserWithRoom, BiliibliUserWithRoomSkeleton } from '@/components/biliibli-user-with-room'
import OptionLabel from '@/components/OptionLabel'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'

export function RoomSelectOpenPlatform() {
  const router = useRouter()
  const { query } = router
  // https://open-live.bilibili.com/document/ad4901b8-c13e-7a20-e07e-410ad182564a
  const {
    // Caller: openPlatformParamCaller, // bilibili
    Code: tempOpCode,
    // Mid: openPlatformParamMid, // 2763
    // Timestamp: openPlatformParamTimestamp,
    // RoomId: openPlatformParamRoom, // 456117
    // Sign: openPlatformParamSign,
    // CodeSign: openPlatformParamCodeSign,
    // plug_env: openPlatformParamViewMode,
  } = query

  const [typingRoomIdOpenPlatform, setTypingRoomIdOpenPlatform] = useState('')
  const [open, setOpen] = useState(false)
  const [selectedRooms, setSelectedRooms] = useState<RoomHistoryItemOpenPlatform[]>([])

  const [openPlatformParamIdCode, setOpenPlatformParamIdCode] = useState<string | undefined>(undefined)

  const { t } = useTranslation()

  const [debouncedTypingRoomIdOpenPlatform] = useDebouncedValue(typingRoomIdOpenPlatform, 400)

  const { roomInfoOpenPlatform, isRoomInfoLoadingOpenPlatform, isRoomInfoErrorOpenPlatform } =
    useBilibiliOpenPlatformAuth(debouncedTypingRoomIdOpenPlatform)

  const [roomIdsOpenPlatform, setRoomIdsOpenPlatform] = useLocalStorageState<string[]>(
    'laplaceChatRoomIdsOpenPlatforms',
    { defaultValue: [] }
  )

  const [roomSearchHistoryOpenPlatform, setRoomSearchHistoryOpenPlatform] = useLocalStorageState<
    RoomHistoryItemOpenPlatform[]
  >('laplaceChatRoomSearchHistoryOpenPlatform', { defaultValue: [] })

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTypingRoomIdOpenPlatform(e.target.value)
  }

  function addRoomSearchHistory(array: RoomHistoryItemOpenPlatform[] = [], newItems: RoomHistoryItemOpenPlatform[]) {
    // Check if `newItems` exist in `RoomHistoryItem`, add if not
    newItems.forEach(newItem => {
      const valueExists = array.some(item => item.value.toString() === newItem.value)

      if (!valueExists) {
        array.push(newItem)
      }
    })

    // dedupe
    const dedupedArr = array
      .map(item => ({
        ...item,
        value: item.value.toString(),
        uid: item.uid.toString(),
      }))
      .filter((item, index, self) => index === self.findIndex(uniqueItem => uniqueItem.value === item.value))

    return dedupedArr
  }

  // Filter out selected rooms and current room if it's in the list
  const filteredRoomHistory = roomSearchHistoryOpenPlatform.filter(
    room =>
      !selectedRooms.some(selected => selected.value === room.value) &&
      (!roomInfoOpenPlatform?.data?.anchor_info || room.value !== roomInfoOpenPlatform.id_code)
  )

  function RoomSearchResult() {
    if (!typingRoomIdOpenPlatform) return null

    if (isRoomInfoErrorOpenPlatform) return <div className='p-2 text-sm text-rose-500'>载入失败</div>
    if (!roomInfoOpenPlatform && isRoomInfoLoadingOpenPlatform)
      return (
        <div className='text-fg/60 px-3 py-1 text-sm'>
          <BiliibliUserWithRoomSkeleton />
        </div>
      )
    if (roomInfoOpenPlatform?.code !== 0)
      return <div className='p-2 text-sm text-rose-500'>错误：{roomInfoOpenPlatform?.message || '未知错误'}</div>

    const anchorInfo = roomInfoOpenPlatform?.data?.anchor_info
    if (!anchorInfo) {
      return <div className='p-2 text-sm text-rose-500'>请输入身份码</div>
    }

    // Check if room is already selected
    const isRoomSelected = selectedRooms.some(room => room.value === roomInfoOpenPlatform.id_code)

    if (isRoomSelected) {
      return <div className='p-2 text-sm text-amber-500/60'>身份码已添加</div>
    }

    const newRoom = {
      value: roomInfoOpenPlatform.id_code,
      uid: anchorInfo.uid.toString(),
      label: roomInfoOpenPlatform.id_code,
      room: anchorInfo.room_id.toString(),
      username: anchorInfo.uname,
    }

    return (
      <div
        className='hover:bg-ac/10 flex cursor-pointer items-center gap-2 px-3 py-1'
        onClick={() => {
          handleRoomsSelectChange([...selectedRooms, newRoom])
          setOpen(false)
          setTypingRoomIdOpenPlatform('')
        }}
      >
        <BiliibliUserWithRoom
          uid={anchorInfo.uid}
          username={anchorInfo.uname}
          roomId={`${anchorInfo.room_id}（${roomInfoOpenPlatform.id_code}）`}
        />
      </div>
    )
  }

  function handleRoomsSelectChange(rooms: RoomHistoryItemOpenPlatform[]) {
    const realRoomIds = rooms.map(room => room.value)
    let roomSearchHistoryMerged = roomSearchHistoryOpenPlatform || []

    if (
      roomInfoOpenPlatform &&
      !isRoomInfoErrorOpenPlatform &&
      !isRoomInfoLoadingOpenPlatform &&
      roomInfoOpenPlatform?.code === 0
    ) {
      roomSearchHistoryMerged = [
        ...roomSearchHistoryMerged,
        {
          value: roomInfoOpenPlatform.id_code,
          uid: roomInfoOpenPlatform.data.anchor_info.uid.toString(),
          label: roomInfoOpenPlatform.id_code,
          room: roomInfoOpenPlatform.data.anchor_info.room_id.toString(),
          username: roomInfoOpenPlatform.data.anchor_info.uname,
        },
      ]
    }

    setSelectedRooms(rooms)
    setRoomIdsOpenPlatform(realRoomIds)
    setRoomSearchHistoryOpenPlatform(addRoomSearchHistory(roomSearchHistoryOpenPlatform, roomSearchHistoryMerged))
  }

  // Load saved rooms from localStorage on mount and when localStorage updates
  useEffect(() => {
    const roomsFromStorage = roomSearchHistoryOpenPlatform
      .filter(room => roomIdsOpenPlatform.includes(room.value))
      .sort((a, b) => {
        const indexA = roomIdsOpenPlatform.indexOf(a.value)
        const indexB = roomIdsOpenPlatform.indexOf(b.value)
        return indexA - indexB
      })

    setSelectedRooms(roomsFromStorage)
  }, [roomIdsOpenPlatform, roomSearchHistoryOpenPlatform])

  useEffect(() => {
    if (tempOpCode) {
      const code = arrayToString(tempOpCode)
      setOpenPlatformParamIdCode(code)
    }
  }, [tempOpCode])

  return (
    <div className='w-full'>
      <OptionLabel
        label={
          <div className='flex items-center gap-x-2'>
            <span className='cursor-default font-medium' onClick={() => setOpen(true)}>
              {t('Select ID Codes')}
            </span>
            <a href='https://play-live.bilibili.com/' target='_blank' rel='noopener noreferrer'>
              <div className='flex items-center gap-x-1'>
                {t('Get Your ID Code')}
                <IconExternalLink size='1em' />
              </div>
            </a>
          </div>
        }
        desc={
          '输入您的身份码，可访问 play-live.bilibili.com 然后在屏幕右侧的「身份码」处获取。请勿将身份码透露给陌生人'
        }
      />

      {openPlatformParamIdCode && (
        <Button
          className='my-2'
          onClick={() => {
            handleSearchChange({ target: { value: openPlatformParamIdCode } } as React.ChangeEvent<HTMLInputElement>)
          }}
        >
          点击填写当前身份码：{openPlatformParamIdCode}
        </Button>
      )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div
            tabIndex={0}
            className='focus-ring data-[state=open]:border-ac mt-1 flex cursor-pointer flex-wrap gap-1 rounded-md border p-1 text-lg'
            onKeyDown={e => {
              // Handle Space and Enter keys
              if (e.key === ' ' || e.key === 'Enter') {
                e.preventDefault() // Prevent page scroll on space
                e.currentTarget.click() // Trigger the popover
              }
            }}
          >
            {selectedRooms.length > 0 ? (
              selectedRooms.map(room => (
                <div key={room.value} className='bg-bg flex items-center gap-2 rounded-sm border p-2 py-1'>
                  <BiliibliUserWithRoom
                    uid={room.uid}
                    username={room.username || room.label}
                    roomId={`${room.room}（${room.value}）`}
                  />
                  <Button
                    className='-mr-0.5 p-1 opacity-50'
                    variant='link'
                    onClick={e => {
                      e.preventDefault()
                      handleRoomsSelectChange(selectedRooms.filter(r => r.value !== room.value))
                    }}
                    aria-label='移除'
                  >
                    <IconX size='1.25rem' />
                  </Button>
                </div>
              ))
            ) : (
              <div className='text-fg/40 px-1.5 py-0.5'>{t('Please select ID Codes')}</div>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent className='max-h-[min(var(--radix-popover-content-available-height),350px)] w-[var(--radix-popover-trigger-width)] p-0'>
          <div className='shadow-border-b relative z-50 px-3'>
            <Input
              placeholder={t('Please enter ID Codes here')}
              value={typingRoomIdOpenPlatform}
              onChange={handleSearchChange}
              className='border-none py-2 pl-6 text-base shadow-none inset-shadow-none focus-visible:ring-0'
              leftSection={<IconSearch className='size-4 shrink-0 opacity-50' />}
              leftSectionClassName='pointer-events-none'
            />
          </div>
          {/* https://github.com/radix-ui/primitives/issues/2307 */}
          <ScrollArea
            className='z-40'
            // 40px is the height of the input
            // 5px is the extra offset to avoid outer container overflow scroll
            viewportProps={{
              className: 'max-h-[calc(min(var(--radix-popover-content-available-height),350px)-40px-5px)]',
            }}
          >
            <div>
              {/* Room search result */}
              <RoomSearchResult />

              {/* Room history */}
              {filteredRoomHistory.length > 0 &&
                filteredRoomHistory.map(room => (
                  <div
                    tabIndex={0}
                    key={room.value}
                    className='focus-ring hover:bg-ac/10 flex cursor-pointer items-center gap-2 px-3 py-1'
                    aria-label={`${room.username}（${room.value}）`}
                    onClick={() => {
                      handleRoomsSelectChange([...selectedRooms, room])
                      setOpen(false)
                      setTypingRoomIdOpenPlatform('')
                    }}
                    onKeyDown={e => {
                      // Handle Space and Enter keys
                      if (e.key === 'Enter') {
                        e.preventDefault() // Prevent page scroll on space
                        e.currentTarget.click() // Trigger the popover
                      }
                    }}
                  >
                    <BiliibliUserWithRoom
                      uid={room.uid}
                      username={room.username || room.label}
                      roomId={`${room.room}（${room.value}）`}
                      className='flex-auto'
                    />
                    <Button
                      onClick={e => {
                        e.stopPropagation()
                        setRoomSearchHistoryOpenPlatform(
                          roomSearchHistoryOpenPlatform.filter(item => item.value !== room.value)
                        )
                      }}
                      className='p-1.5'
                      size='sm'
                      tint='rose'
                      aria-label='移除'
                    >
                      <IconTrash className='size-4' />
                    </Button>
                  </div>
                ))}
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>
    </div>
  )
}
