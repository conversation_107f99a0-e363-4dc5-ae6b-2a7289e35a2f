const lang = Object.freeze(JSON.parse("{\"displayName\":\"dotEnv\",\"name\":\"dotenv\",\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#line-comment\"}]}},\"match\":\"^\\\\s?(#.*)$\\\\n\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#key\"}]},\"2\":{\"name\":\"keyword.operator.assignment.dotenv\"},\"3\":{\"name\":\"property.value.dotenv\",\"patterns\":[{\"include\":\"#line-comment\"},{\"include\":\"#double-quoted-string\"},{\"include\":\"#single-quoted-string\"},{\"include\":\"#interpolation\"}]}},\"match\":\"^\\\\s?(.*?)\\\\s?(=)(.*)$\"}],\"repository\":{\"double-quoted-string\":{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#interpolation\"},{\"include\":\"#escape-characters\"}]}},\"match\":\"\\\"(.*)\\\"\",\"name\":\"string.quoted.double.dotenv\"},\"escape-characters\":{\"match\":\"\\\\\\\\(?:[\\\"'\\\\\\\\bfnrt]|u[0-9A-F]{4})\",\"name\":\"constant.character.escape.dotenv\"},\"interpolation\":{\"captures\":{\"1\":{\"name\":\"keyword.interpolation.begin.dotenv\"},\"2\":{\"name\":\"variable.interpolation.dotenv\"},\"3\":{\"name\":\"keyword.interpolation.end.dotenv\"}},\"match\":\"(\\\\$\\\\{)(.*)(})\"},\"key\":{\"captures\":{\"1\":{\"name\":\"keyword.key.export.dotenv\"},\"2\":{\"name\":\"variable.key.dotenv\",\"patterns\":[{\"include\":\"#variable\"}]}},\"match\":\"(export\\\\s)?(.*)\"},\"line-comment\":{\"match\":\"#.*$\",\"name\":\"comment.line.dotenv\"},\"single-quoted-string\":{\"match\":\"'(.*)'\",\"name\":\"string.quoted.single.dotenv\"},\"variable\":{\"match\":\"[A-Z_a-z]+[0-9A-Z_a-z]*\"}},\"scopeName\":\"source.dotenv\"}"))

export default [
lang
]
