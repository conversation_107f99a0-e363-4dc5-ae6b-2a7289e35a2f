import Link from 'next/link'

import Avatar from '@/components/chat/avatar'

export default function BilibiliUser({ uid, name }: { uid: number; name?: string }) {
  return (
    <Link href={`https://space.bilibili.com/${uid}`} target='_blank' rel='noopener noreferrer'>
      <div className='flex items-center gap-2'>
        <Avatar uid={uid} />
        {name ? <span>{name}</span> : null}
      </div>
    </Link>
  )
}
