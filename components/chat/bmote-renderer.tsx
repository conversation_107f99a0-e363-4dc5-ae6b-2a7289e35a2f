import React from 'react'
import type { SimpleEmotesList } from '@laplace.live/internal'

import styles from './bmote-renderer.module.css'

interface BmoteProps {
  text: string
  bmotes: SimpleEmotesList
}

export default function BmoteRenderer({ text, bmotes }: BmoteProps) {
  const regex = /(\[[^\]]+\])|([^\[\]]+)/g

  const replaceEmotes = (inputText: string) => {
    const matches = inputText.match(regex)

    if (matches) {
      const parts = matches.map((part, idx) => {
        if (bmotes.hasOwnProperty(part)) {
          return (
            <picture key={idx} className={`${styles.bmoteWrap} bmote-wrap inline-flex`}>
              <img
                className={`${styles.bmote} bmote`}
                src={bmotes[part]?.url}
                alt={part}
                // NOTE: b豆目前返回的尺寸基本都是 20 x 20 固定的
                // width={bmotes[part].width || 24}
                // height={bmotes[part].height || 24}
                referrerPolicy='no-referrer'
                loading='lazy'
              />
            </picture>
          )
        }
        return part
      })
      return parts
    } else {
      return []
    }
  }

  return <>{replaceEmotes(text)}</>
}
