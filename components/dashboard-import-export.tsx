import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import { IconFileTypeCsv, IconFileTypeXls, IconFileZip } from '@tabler/icons-react'

import { db } from '@/lib/db'
import type { LaplaceEvent } from '@/lib/event-parsers/types'

import { interactionTypeMap } from '@/utils/map'
import { nf } from '@/utils/numberFormat'
import { validateLaplaceEvents } from '@/utils/validateLaplaceEvents'

import { useCompressionWorker } from '@/hooks/useCompressionWorker'

import { Alert } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { CheckboxWithLabel } from '@/components/ui/checkbox-with-label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuIcon,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown'
import { Dropzone } from '@/components/ui/dropzone'
import { Label } from '@/components/ui/label'
import { Loading } from '@/components/ui/loading'
import { SwitchWithLabel } from '@/components/ui/switch-with-label'

interface EventExportData {
  name: string
  data: LaplaceEvent[]
}

interface ExportEventsProps {
  events: EventExportData[]
  meta: {
    uname: string
    roomId: number
  }
}

interface ImportEventsProps {
  onImportComplete?: () => void
}

interface EventTypeCounts {
  messages: number
  superchats: number
  gifts: number
  toasts: number
  interactions: number
  systemEvents: number
  redEnvelopes: number
  lotteries: number
  notices: number
  userBlock: number
}

function processExportFilename(str: string) {
  return str.replaceAll(':', '_').replaceAll(' ', '_')
}

export function ExportEvents({ events, meta }: ExportEventsProps) {
  const [isExporting, setIsExporting] = useState(false)
  const { t } = useTranslation()
  const { compressData, compressing } = useCompressionWorker()
  const exportDate = new Date().toLocaleString()
  const exportFilenamePrefix = `${meta.roomId}_${meta.uname}`

  // Process events for export formats
  const processEvents = (events: LaplaceEvent[], eventType: string) => {
    const arrFiltered =
      eventType === 'gifts'
        ? events.filter(event =>
            ['gift', 'toast', 'mvp', 'red-envelope-start', 'red-envelope-result'].includes(event.type)
          )
        : eventType === 'superchats'
          ? events.filter(event => ['superchat'].includes(event.type))
          : events

    if (!arrFiltered?.length) return []

    return arrFiltered.map(event => {
      let message = ''

      if ('message' in event) {
        message = event.message
      }

      if (event.type === 'toast') {
        const msgMetaRegex = /.*(开通|续费).*的第(\d+)天/
        const msgMeta = event.message.match(msgMetaRegex)
        const totalDays = msgMeta ? Number(msgMeta[2]) : 0
        const toastActionType = msgMeta ? msgMeta[1] : '开通'
        message = `${toastActionType}${event.toastName}${totalDays > 0 ? `，已陪伴主播 ${totalDays} 天` : ''}`
      }

      if (event.type === 'interaction') {
        message = `${interactionTypeMap[event.action]?.name ?? '??'}直播间`
      }

      if (event.type === 'user-block') {
        message = `被${event.operator === 1 ? '房管' : '主播'}禁言${event.vaildPeriod ? `，有效期${event.vaildPeriod}` : ''}`
      }

      return {
        origin: event.origin,
        uid: event.uid,
        username: 'username' in event ? event.username : '',
        type: event.type,
        price:
          event.type === 'red-envelope-start'
            ? event.priceForStreamer
            : 'priceNormalized' in event
              ? event.priceNormalized
              : 0,
        message,
        date: new Date(event.timestampNormalized).toISOString(),
        timestamp: event.timestampNormalized,
      }
    })
  }

  // Export to LAPLACE Chat Archive
  const exportToLCA = async () => {
    setIsExporting(true)

    // Show compression progress toast
    const toastId = 'export-progress'
    toast.loading(t('Exporting, please wait…'), { id: toastId })

    try {
      // Compress data using worker
      const compressedData = await compressData(events)
      if (!compressedData) {
        throw new Error('Compression failed')
      }

      // Create and download file
      const blob = new Blob([compressedData], { type: 'application/lca' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = processExportFilename(`${exportFilenamePrefix}_${exportDate}.lca`)
      a.click()
      URL.revokeObjectURL(url)

      toast.success(t('Export successful'), {
        id: toastId,
        description: t('You can use Import Events to view it again'),
      })
    } catch (error) {
      console.error('Export error:', error)
      toast.error(t('Export failed'), {
        id: toastId,
        description: error instanceof Error ? error.message : 'Unknown error',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Export to raw JSON
  const exportToRawJson = () => {
    try {
      const blob = new Blob([JSON.stringify(events, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = processExportFilename(`${exportFilenamePrefix}_${exportDate}.json`)
      a.click()
      URL.revokeObjectURL(url)
      toast.success(t('Export successful'))
    } catch (error) {
      console.error('Export error:', error)
      toast.error(t('Export failed'))
    }
  }

  // Export to Excel helper
  const exportToExcel = async () => {
    setIsExporting(true)
    try {
      const XLSX = await import('@/utils/xlsxLoader')
      const wb = XLSX.utils.book_new()

      events.forEach(({ name, data }) => {
        const processedData = processEvents(data, name)
        if (processedData.length) {
          const ws = XLSX.utils.json_to_sheet(processedData)
          XLSX.utils.book_append_sheet(wb, ws, name)
        }
      })

      XLSX.writeFileXLSX(wb, processExportFilename(`${exportFilenamePrefix}_${exportDate}.xlsx`))
      toast.success(t('Export successful'))
    } catch (error) {
      console.error('Failed to export Excel:', error)
      toast.error(t('Export failed'))
    } finally {
      setIsExporting(false)
    }
  }

  // Export to CSV helper
  const exportToCSV = (eventType: string = '') => {
    const eventToExport = events.find(e => e.name === eventType)
    if (!eventToExport) return

    const processedData = processEvents(eventToExport.data, eventType)
    if (!processedData.length) return

    const header = Object.keys(processedData[0]!).join(',') + '\r\n'
    const rows = processedData
      .map(row =>
        Object.values(row)
          .map(value => (typeof value === 'string' ? `"${value.replaceAll('"', '""')}"` : value))
          .join(',')
      )
      .join('\r\n')

    const universalBOM = '\uFEFF'
    const blob = new Blob([universalBOM + header + rows], { type: 'text/csv; charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = processExportFilename(`${exportFilenamePrefix}_${eventType}_${exportDate}.csv`)
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button disabled={isExporting || !events.length}>
          {isExporting ? (
            <>
              <Loading />
              {compressing ? t('Compressing…') : t('Preparing…')}
            </>
          ) : (
            t('exportEvents.label')
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-56'>
        <DropdownMenuLabel>{t('Export All')}</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* All Data Exports */}
        <DropdownMenuGroup>
          <DropdownMenuItem disabled={isExporting} onClick={exportToLCA}>
            <DropdownMenuIcon>
              <IconFileZip />
            </DropdownMenuIcon>
            <span>LAPLACE Chat {t('Archive')}</span>
          </DropdownMenuItem>

          <DropdownMenuItem disabled={isExporting} onClick={exportToExcel}>
            <DropdownMenuIcon>
              <IconFileTypeXls />
            </DropdownMenuIcon>
            <span>{t('Excel Spreadsheet')}</span>
          </DropdownMenuItem>

          {/* <DropdownMenuItem disabled={isExporting} onClick={exportToRawJson}>
            <DropdownMenuIcon>
              <IconJson />
            </DropdownMenuIcon>
            <span>Raw JSON</span>
          </DropdownMenuItem> */}
        </DropdownMenuGroup>

        <DropdownMenuSeparator />
        <DropdownMenuLabel>{t('Export by Types')}</DropdownMenuLabel>

        {events.map(({ name, data }) => (
          <DropdownMenuGroup key={name}>
            <DropdownMenuItem disabled={isExporting || !data.length} onClick={() => exportToCSV(name)}>
              <DropdownMenuIcon>
                <IconFileTypeCsv />
              </DropdownMenuIcon>
              <span>{name}.csv</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export function ImportEvents({ onImportComplete }: ImportEventsProps) {
  const { t } = useTranslation()
  const [isImporting, setIsImporting] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [eventCount, setEventCount] = useState(0)

  // Add new state for tracking selected event types
  const [selectedEventTypes, setSelectedEventTypes] = useState({
    messages: true,
    superchats: true,
    gifts: true,
    toasts: true,
    interactions: true,
    systemEvents: true,
    redEnvelopes: true,
    lotteries: true,
    notices: true,
    userBlock: true,
  })

  const [eventTypeCounts, setEventTypeCounts] = useState<EventTypeCounts>({
    messages: 0,
    superchats: 0,
    gifts: 0,
    toasts: 0,
    interactions: 0,
    systemEvents: 0,
    redEnvelopes: 0,
    lotteries: 0,
    notices: 0,
    userBlock: 0,
  })

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])

    if (files.length === 0) {
      setSelectedFiles([])
      setEventCount(0)
      setEventTypeCounts({
        messages: 0,
        superchats: 0,
        gifts: 0,
        toasts: 0,
        interactions: 0,
        systemEvents: 0,
        redEnvelopes: 0,
        lotteries: 0,
        notices: 0,
        userBlock: 0,
      })
      return
    }

    // Track validation results
    const validFiles: File[] = []
    const validEventsCount = { total: 0 }
    const typeCounts: EventTypeCounts = {
      messages: 0,
      superchats: 0,
      gifts: 0,
      toasts: 0,
      interactions: 0,
      systemEvents: 0,
      redEnvelopes: 0,
      lotteries: 0,
      notices: 0,
      userBlock: 0,
    }
    let hasError = false

    // Validate each file
    for (const file of files) {
      if (!file.name.endsWith('.lca')) {
        toast.error(t('Invalid file format'), {
          description: `${file.name}: ${t('Please select a .lca file')}`,
        })
        hasError = true
        continue
      }

      try {
        const LZString = await import('lz-string')
        const text = await file.text()
        const decompressedText = LZString.default.decompressFromUTF16(text)
        if (!decompressedText) {
          throw new Error('Failed to decompress file')
        }

        const data = JSON.parse(decompressedText)
        if (!validateLaplaceEvents(data)) {
          throw new Error('Invalid event data format')
        }

        // Count events by type
        data.forEach(sheet => {
          sheet.data.forEach(event => {
            validEventsCount.total++

            if (event.type === 'message' || event.type === 'effect-message') typeCounts.messages++
            else if (event.type === 'superchat') typeCounts.superchats++
            else if (['gift', 'mvp'].includes(event.type)) typeCounts.gifts++
            else if (event.type === 'toast') typeCounts.toasts++
            else if (['interaction', 'like-click'].includes(event.type)) typeCounts.interactions++
            else if (
              [
                'live',
                'preparing',
                'live-start',
                'live-end',
                'live-warning',
                'live-cutoff',
                'room-name-update',
                'room-mute-on',
                'room-mute-off',
              ].includes(event.type)
            )
              typeCounts.systemEvents++
            else if (['red-envelope-start', 'red-envelope-result'].includes(event.type)) typeCounts.redEnvelopes++
            else if (['lottery-start', 'lottery-result'].includes(event.type)) typeCounts.lotteries++
            else if (event.type === 'notice') typeCounts.notices++
            else if (event.type === 'user-block') typeCounts.userBlock++
          })
        })

        validFiles.push(file)
      } catch (error) {
        console.error(`File validation error for ${file.name}:`, error)
        toast.error(`${file.name}: ${t('Invalid file format')}`, {
          description: error instanceof Error ? error.message : 'Unknown error',
        })
        hasError = true
      }
    }

    // Update state based on validation results
    if (validFiles.length === 0) {
      e.target.value = ''
      setSelectedFiles([])
      setEventCount(0)
      setEventTypeCounts({
        messages: 0,
        superchats: 0,
        gifts: 0,
        toasts: 0,
        interactions: 0,
        systemEvents: 0,
        redEnvelopes: 0,
        lotteries: 0,
        notices: 0,
        userBlock: 0,
      })
    } else {
      setSelectedFiles(validFiles)
      setEventCount(validEventsCount.total)
      setEventTypeCounts(typeCounts)

      // Uncheck type selections for empty counts
      setSelectedEventTypes(prev => {
        const newState = { ...prev }
        Object.keys(typeCounts).forEach(key => {
          if (typeCounts[key as keyof EventTypeCounts] === 0) {
            newState[key as keyof EventTypeCounts] = false
          }
        })
        return newState
      })

      // If some files were valid but others had errors
      if (hasError) {
        toast.success(t('Some files were processed successfully'), {
          description: t('{{count}} valid files containing {{events}} events', {
            count: validFiles.length,
            events: validEventsCount.total,
          }),
        })
      }
    }
  }

  const handleImport = async () => {
    if (selectedFiles.length === 0) return

    try {
      setIsImporting(true)
      const LZString = await import('lz-string')
      let totalImportedEvents = 0

      // Process each file sequentially
      for (const file of selectedFiles) {
        try {
          const text = await file.text()
          const decompressedText = LZString.default.decompressFromUTF16(text)
          if (!decompressedText) {
            throw new Error(`Failed to decompress file: ${file.name}`)
          }

          const data = JSON.parse(decompressedText)
          if (!validateLaplaceEvents(data)) {
            throw new Error(`Invalid event data format in file: ${file.name}`)
          }

          // Filter events based on selected types
          const allEvents = data.flatMap(sheet =>
            sheet.data.filter(event => {
              if (['message', 'effect-message'].includes(event.type) && !selectedEventTypes.messages) return false
              if (event.type === 'superchat' && !selectedEventTypes.superchats) return false
              if (['gift', 'mvp'].includes(event.type) && !selectedEventTypes.gifts) return false
              if (event.type === 'toast' && !selectedEventTypes.toasts) return false
              if (['interaction', 'like-click'].includes(event.type) && !selectedEventTypes.interactions) return false
              if (
                [
                  'live',
                  'preparing',
                  'live-start',
                  'live-end',
                  'live-warning',
                  'live-cutoff',
                  'room-name-update',
                  'room-mute-on',
                  'room-mute-off',
                ].includes(event.type) &&
                !selectedEventTypes.systemEvents
              )
                return false
              if (
                ['red-envelope-start', 'red-envelope-result'].includes(event.type) &&
                !selectedEventTypes.redEnvelopes
              )
                return false
              if (['lottery-start', 'lottery-result'].includes(event.type) && !selectedEventTypes.lotteries)
                return false
              if (event.type === 'notice' && !selectedEventTypes.notices) return false
              if (event.type === 'user-block' && !selectedEventTypes.userBlock) return false
              return true
            })
          )

          // Bulk put all events into IndexedDB
          await db.eventItems.bulkPut(allEvents)
          totalImportedEvents += allEvents.length
        } catch (error) {
          console.error(`Import error for file ${file.name}:`, error)
          toast.error(`Failed to import ${file.name}`, {
            description: error instanceof Error ? error.message : 'Unknown error',
          })
        }
      }

      if (totalImportedEvents > 0) {
        toast.success(t('Import successful'), {
          description: `${nf.format(totalImportedEvents)} ${t('events imported')}`,
        })
        onImportComplete?.()
        setIsOpen(false)
      } else {
        toast.error(t('No events were imported'))
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error(t('Import failed'), {
        description: error instanceof Error ? error.message : 'Unknown error',
      })
    } finally {
      setIsImporting(false)
      setSelectedFiles([])
      setEventCount(0)
    }
  }

  // Reset states when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedFiles([])
      setEventCount(0)
    }
  }, [isOpen])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>{t('Import Events…')}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogDescription hidden>{t('Import Events')}</DialogDescription>
        <DialogHeader>
          <DialogTitle>{t('Import Events')}</DialogTitle>
        </DialogHeader>
        <div className='space-y-2'>
          <Label htmlFor='lcaFileInput'>{t('Select a LAPLACE Chat Archive (.lca) to import events.')}</Label>
          <Dropzone
            id='lcaFileInput'
            accept='.lca'
            onChange={handleFileSelect}
            disabled={isImporting}
            className='w-full'
            multiple={true}
            maxFiles={5}
          />
          {selectedFiles.length > 0 && (
            <div>
              <div className='text-fg/60 mt-1 text-sm'>
                {t('Ready to import')} {nf.format(eventCount)} {t('events')}
              </div>

              <div className='mt-4 space-y-3'>
                <SwitchWithLabel
                  label={t('Select All')}
                  checked={Object.values(selectedEventTypes).every(v => v)}
                  onChange={e => {
                    const newValue = !Object.values(selectedEventTypes).every(v => v)
                    setSelectedEventTypes(prev =>
                      Object.keys(prev).reduce(
                        (acc, key) => ({
                          ...acc,
                          [key]: newValue,
                        }),
                        prev
                      )
                    )
                  }}
                />

                <div className='grid grid-cols-2 gap-3'>
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('Messages')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.messages)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.messages}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, messages: e.target.checked }))}
                    disabled={eventTypeCounts.messages === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex flex-auto items-center gap-2'>
                        <span className='flex-1'>{t('SuperChats')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.superchats)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.superchats}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, superchats: e.target.checked }))}
                    disabled={eventTypeCounts.superchats === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('Gifts')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.gifts)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.gifts}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, gifts: e.target.checked }))}
                    disabled={eventTypeCounts.gifts === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('Guards')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.toasts)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.toasts}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, toasts: e.target.checked }))}
                    disabled={eventTypeCounts.toasts === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('Interactions')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.interactions)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.interactions}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, interactions: e.target.checked }))}
                    disabled={eventTypeCounts.interactions === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('System Events')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.systemEvents)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.systemEvents}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, systemEvents: e.target.checked }))}
                    disabled={eventTypeCounts.systemEvents === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('Red Envelopes')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.redEnvelopes)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.redEnvelopes}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, redEnvelopes: e.target.checked }))}
                    disabled={eventTypeCounts.redEnvelopes === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('Lotteries')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.lotteries)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.lotteries}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, lotteries: e.target.checked }))}
                    disabled={eventTypeCounts.lotteries === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('Notices')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.notices)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.notices}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, notices: e.target.checked }))}
                    disabled={eventTypeCounts.notices === 0}
                  />
                  <CheckboxWithLabel
                    label={
                      <div className='flex items-center gap-1'>
                        <span>{t('User Blocks')}</span>
                        <span className='text-fg/60 font-normal'>{nf.format(eventTypeCounts.userBlock)}</span>
                      </div>
                    }
                    checked={selectedEventTypes.userBlock}
                    onChange={e => setSelectedEventTypes(prev => ({ ...prev, userBlock: e.target.checked }))}
                    disabled={eventTypeCounts.userBlock === 0}
                  />
                </div>
              </div>
              <Alert className='mt-4' tint='info'>
                当您点击「导入」时，您的归档将会在浏览器本地解压缩并保存至浏览器内置的数据库，没有任何数据会被上传或记录至互联网。重复的事件会被覆盖
              </Alert>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant='link' disabled={isImporting} onClick={() => setIsOpen(false)}>
            {t('Cancel')}
          </Button>
          <Button
            tint={'accent'}
            onClick={handleImport}
            loading={isImporting}
            disabled={isImporting || !(selectedFiles.length > 0) || !Object.values(selectedEventTypes).some(v => v)}
          >
            {isImporting ? t('Importing…') : t('Import')}{' '}
            {selectedFiles.length > 0 ? (
              <div>
                {nf.format(
                  Object.entries(eventTypeCounts).reduce(
                    (sum, [key, count]) => (selectedEventTypes[key as keyof EventTypeCounts] ? sum + count : sum),
                    0
                  )
                )}{' '}
                {t('events')}
              </div>
            ) : null}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
