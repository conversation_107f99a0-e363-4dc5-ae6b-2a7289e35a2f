@layer template {
  .event {
    --event-border-radius: calc(var(--1px) * 6);

    padding: calc(var(--event-font-size) * 0.25) calc(var(--event-font-size) * 0.5);
    font-family: var(--event-font-family);
    font-size: var(--event-font-size);
    line-height: var(--event-line-height-base);
    color: var(--event-message-text, var(--text-color));
    background: var(--event-interaction-bg, transparent);
  }

  .meta {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--event-font-size) * 0.25);
    margin-right: calc(var(--event-font-size) * 0.5);
  }

  .message {
    line-height: var(--event-line-height);
    vertical-align: top;
  }

  .guardLevel0 {
    background-color: transparent;
    --fallback-text: var(--text-color);

    .username {
      color: var(--event-username-text-0, var(--event-username-color-t0, var(--fallback-text)));
    }
  }

  .guardLevel1 {
    /* background-color: var(--orange-20); */
    --fallback-text: var(--orange-dark);

    .username {
      color: var(--event-username-text-1, var(--event-username-color-t1, var(--fallback-text)));
    }

    &.dark {
      --fallback-text: var(--orange-light);
    }
  }

  .guardLevel2 {
    /* background-color: var(--purple-20); */
    --fallback-text: var(--purple-dark);

    .username {
      color: var(--event-username-text-1, var(--event-username-color-t2, var(--fallback-text)));
    }

    &.dark {
      --fallback-text: var(--purple-light);
    }
  }

  .guardLevel3 {
    /* background-color: var(--blue-20); */
    --fallback-text: var(--blue-dark);

    .username {
      color: var(--event-username-text-1, var(--event-username-color-t3, var(--fallback-text)));
    }

    &.dark {
      --fallback-text: var(--blue-light);
    }
  }

  .username {
    font-weight: bold;
    word-break: break-word;
  }

  .action_enter {
    .message {
      color: var(--event-interaction-text-enter, var(--text-color-50));
    }
  }

  .action_follow {
    .message {
      color: var(--event-interaction-text-follow, var(--orange));
    }
  }

  .action_share {
    .message {
      color: var(--event-interaction-text-share, var(--blue));
    }
  }

  .action_follow-special {
    .message {
      color: var(--event-interaction-text-follow-special, var(--orange-light));
    }
  }

  .action_follow-mutual {
    .message {
      color: var(--event-interaction-text-follow-mutual, var(--pink));
    }
  }

  .relationWrap {
    vertical-align: top;
    display: inline-flex;
  }

  .relationIconWrap {
    .relationIcon {
      max-height: var(--event-line-height);
      text-indent: -9999px;
      border-radius: var(--event-line-height);

      /* b站的关系图标因为时给官方直播姬用的，只有深色模式，因此完全没考虑浅色模式下的效果，此处强制增加一个深色背景 */
      [data-theme='light'] & {
        background-color: rgba(0, 0, 0, 0.8);
      }
    }
  }
}
