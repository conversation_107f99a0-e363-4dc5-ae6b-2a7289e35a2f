<style>
  /* layer in global.css does not work in storybook, idk why. Redefine it here with Tailwind built-in layers */
  @layer theme, base, components, utilities, kladenets, kladewind, template, remote-css, custom-css;

  /* Fixed variables for visual testing */
  @layer template {
    :root {
      --event-font-size: 20px;

      /* copied from globals.css */
      --1px: calc(var(--event-font-size, 16px) * 0.0625);
      --event-line-height: calc(var(--event-font-size, 16px) * 1.5);
      --event-line-height-base: 1.15;
    }
  }
</style>
