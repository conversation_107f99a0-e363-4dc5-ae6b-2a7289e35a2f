import React, { startTransition, useEffect, useMemo, useRef, useState } from 'react'
import clsx from 'clsx'
import { Flipped, Flipper } from 'react-flip-toolkit'
import { toast } from 'sonner'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'
import type { LaplaceEvent } from '@/lib/event-parsers/types'
import { useGiftEffectStore } from '@/lib/gift-effects/store'
import useGlobalStore from '@/lib/store'

import { loadCloudTemplate } from '@/utils/loadCloudTemplate'
import { loadRemoteTemplate } from '@/utils/loadRemoteTemplate'
import { shouldShowEventObs } from '@/utils/processEvents'

import { useOptions } from '@/hooks/useOptions'
import useRemoteEmotes from '@/hooks/useRemoteEmotes'
import useRoomsInfo from '@/hooks/useRoomsInfo'

import EventItem from '@/components/event'
import { GiftEffectPlayer } from '@/components/GiftEffectPlayer'
import { ScrollArea } from '@/components/ui/scroll-area'

import styles from './EventListObs.module.css'

export interface EventListProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Optional pre-defined events
   *
   * When this option is set, the global events store will be ignored
   */
  predefinedEvents?: LaplaceEvent[]

  /**
   * Whether the list is scrollale, should be false for OBS mode
   *
   * 具体使用场景：
   * - 当在首页并且是 mock data demo 的时候，设置为 true，让容器可以自由滚动，此时 overflow = auto
   * - 当在首页并且是 live data demo 的时候，设置为 false，并在下方的 useEffect 中进行判断，开启自动触底滚动
   */
  scrollable?: boolean

  /**
   * Rooms
   */
  rooms: number[]

  /**
   * Whether to show the gift effect layer for debugging
   */
  showGiftEffectLayer?: boolean
}

export default function EventListObs({
  rooms,
  className,
  predefinedEvents,
  scrollable = false,
  showGiftEffectLayer = false,
  ...rest
}: EventListProps) {
  // Memoize rooms array to prevent unnecessary re-renders
  // Only updates when room IDs change
  const roomsKey = useMemo(() => rooms.join(','), [rooms])
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedRooms = useMemo(() => rooms, [roomsKey])

  const viewportRef = useRef<HTMLDivElement>(null)
  const eventCountRef = useRef<number>(0)

  const [processedObsEvents, setProcessedObsEvents] = useState<LaplaceEvent[]>([])
  const [processedEventsStickyGift, setProcessedEventsStickyGift] = useState<LaplaceEvent[]>([])
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true)
  const [, setProcessedRoomsInfo] = useState<BilibiliInternal.HTTPS.Prod.GetInfoByRoom[]>([])
  const [eventRate, setEventRate] = useState<number>(0)

  const events = useGlobalStore(state => state.events)
  const queue = useGlobalStore(state => state.queue)
  const dashboardLiveGuards = useGlobalStore(state => state.dashboardLiveGuards)
  const addEvent = useGlobalStore(state => state.addEvent)
  const delQueue = useGlobalStore(state => state.delQueue)
  const updateRemoteEmotes = useGlobalStore(state => state.updateRemoteEmotes)
  const updateDashboardLiveGuards = useGlobalStore(state => state.updateDashboardLiveGuards)
  const updateCloudThemeCss = useGlobalStore(state => state.updateCloudThemeCss)
  const updateRemoteThemeCss = useGlobalStore(state => state.updateRemoteThemeCss)

  const options = useOptions()
  const {
    colorScheme,
    sceneName,
    connectionMode,
    showStickyBar,
    showGiftStickyAbove,
    limitEventAmount,
    limitStickyAmount,
    showGiftEffect,
    showGiftEffectAbove,
    cloudTheme,
    remoteTheme,
    enableCloudTheme,
  } = options

  const { addGiftEffectQueue } = useGiftEffectStore()
  const { roomsInfo } = useRoomsInfo(memoizedRooms && memoizedRooms.length > 0 ? memoizedRooms : [])
  const { remoteEmotes } = useRemoteEmotes(memoizedRooms)

  useEffect(() => {
    if (roomsInfo && roomsInfo.length > 0) {
      startTransition(() => {
        setProcessedRoomsInfo(roomsInfo)

        roomsInfo.map(roomInfo => {
          if (roomInfo.code === 0 && roomInfo.data) {
            updateDashboardLiveGuards(roomInfo.data.room_info.uid, {
              roomId: roomInfo.data.room_info.room_id,
              guardCount: roomInfo.data.guard_info?.count || 0,
            })
          }
        })
      })
    }
  }, [roomsInfo, updateDashboardLiveGuards])

  // Load the template if cloudTheme is provided in the URL
  useEffect(() => {
    const fetchCloudTheme = async () => {
      // 为了让配置器中的编辑器可以正常工作，即：
      // 用户切换模版时，如果没有激活云端载入，删除编辑器中的 CSS 可以正常清除样式
      // 此处需要增加 enableCloudTheme 的判断
      // 虽然这个选项在 OBS 模式下似乎是没什么用途的，但为了实现上述效果我们还是需要它
      // enableCloudTheme 默认设置为 !== false 是因为，在 OBS 模式下
      // 这个参数不会通过 URI 传入，因此它总会是 undefined，为了让条件正确判断，因此要设置为 !== false
      if (enableCloudTheme !== false) {
        const css = await loadCloudTemplate(cloudTheme)
        updateCloudThemeCss(css)
      } else {
        updateCloudThemeCss('')
      }
    }
    fetchCloudTheme()
  }, [enableCloudTheme, cloudTheme, updateCloudThemeCss])

  // Load CSS from remoteTheme URL if provided
  useEffect(() => {
    if (remoteTheme && typeof remoteTheme === 'string') {
      const loadRemoteTheme = async () => {
        try {
          const css = await loadRemoteTemplate(remoteTheme)
          if (css.success) {
            updateRemoteThemeCss(css.css)
          } else {
            console.warn(`Failed to load remote template: ${remoteTheme}`, css.error)
            toast.warning('Error parsing remote template', {
              id: 'remote-template-error',
            })
          }
        } catch (error) {
          console.warn(`Failed to load remote template: ${remoteTheme}`, error)
          toast.warning('Error parsing remote template', {
            id: 'remote-template-error',
          })
        }
      }
      loadRemoteTheme()
    } else {
      updateRemoteThemeCss('')
    }
  }, [remoteTheme, updateRemoteThemeCss])

  // Get remote emotes (only works for the first room at the moment)
  useEffect(() => {
    if (remoteEmotes && Object.keys(remoteEmotes).length > 0) {
      updateRemoteEmotes(remoteEmotes)
    }
  }, [remoteEmotes, updateRemoteEmotes])

  // Process events and push them to effects (OBS widget)
  useEffect(() => {
    const isPredefined = predefinedEvents && predefinedEvents.length > 0
    const data = isPredefined ? predefinedEvents : events

    if (data && data.length > 0) {
      // Process global events
      // Only perserve recent events for OBS for better performance
      setProcessedObsEvents(isPredefined ? data : data.slice((limitEventAmount || 50) * -1))

      // Processs sticky bar events
      const preparedEventsStickyGift = data
        .filter(event => {
          // 天选比较特殊，不需要其拥有合法 priceNormalized，因此单独判断
          return (
            ((((event.type === 'gift' && event.coinType === 'gold') ||
              event.type === 'superchat' ||
              event.type === 'toast' ||
              event.type === 'mvp' ||
              event.type === 'red-envelope-start') &&
              event.priceNormalized > showGiftStickyAbove) ||
              event.type === 'lottery-start') &&
            // filter expired events
            (scrollable || event.timestampNormalized + event.duration * 1000 >= Date.now())
          )
        })
        .slice((limitStickyAmount || 20) * -1)

      setProcessedEventsStickyGift(
        // NOTE: uncomment the following to see how it can be sorted by price
        // preparedEventsStickyGift.sort((a, b) => {
        //   // @ts-ignore
        //   return b.priceNormalized - a.priceNormalized
        // })
        preparedEventsStickyGift.reverse()
      )
    }
  }, [predefinedEvents, events, showGiftStickyAbove, limitEventAmount, limitStickyAmount, scrollable])

  /**
   * Detect if new items should be added to DOM
   */
  useEffect(() => {
    if (queue.length > 0) {
      // Dynamic timeout, more events in queue, faster timeout for animation
      const eventQueueLength = queue.length
      // console.log(`eventQueueLength`, [eventQueueLength, eventRate])

      // websocket 正常是使用此选项
      // const timeoutFactor = eventQueueLength > 3 ? 0 : 90 / eventQueueLength

      // 以下选项为b站开始对ws限流时所用的选项，在限流时，弹幕事件将 1 以秒一个周期的形式出现
      const timeoutFactor =
        eventQueueLength > 30 || eventQueueLength < 2 || eventRate > 30 || eventRate < 2 ? 0 : 900 / eventQueueLength

      // const timeoutFactor = eventQueueLength < 100 ? 1000 / eventQueueLength : 0
      // console.log(`timeoutFactor`, timeoutFactor);

      const timer = setTimeout(() => {
        const currentEvent = queue[0]!

        // Process special events first
        // if (currentEvent.type === 'entry-effect') {
        //   console.log(`entry-effect`, currentEvent);
        // }

        if (currentEvent.type === 'toast') {
          if (currentEvent.origin in dashboardLiveGuards) {
            updateDashboardLiveGuards(currentEvent.origin, {
              ...dashboardLiveGuards[currentEvent.origin]!,
              guardCount: dashboardLiveGuards[currentEvent.origin]!.guardCount + 1,
            })
          }
        }

        // Pause pushing events when scrolled
        if (shouldScrollToBottom) {
          const showEvent = shouldShowEventObs(currentEvent, options)
          if (showEvent) {
            addEvent(currentEvent)

            // trigger gift effects automatically
            if (showGiftEffect) {
              // 新选项可能是 undefined，所以需要设置默认值 99 for OBS
              if (currentEvent.type === 'gift' && currentEvent.priceNormalized > (showGiftEffectAbove || 99)) {
                addGiftEffectQueue({
                  giftId: currentEvent.giftId,
                  amount: currentEvent.giftAmount,
                })
              }

              if (
                currentEvent.type === 'toast' &&
                currentEvent.effectId &&
                currentEvent.priceNormalized > (showGiftEffectAbove || 99)
              ) {
                addGiftEffectQueue({
                  effectId: currentEvent.effectId,
                  amount: currentEvent.toastAmount,
                })
              }

              if (
                currentEvent.type === 'mvp' &&
                currentEvent.effectId &&
                currentEvent.priceNormalized > (showGiftEffectAbove || 99)
              ) {
                addGiftEffectQueue({
                  effectId: currentEvent.effectId,
                  amount: currentEvent.mvpAmount,
                })
              }

              // Debug mode copyied from dashboard mode
              if (currentEvent.type === 'message' && (currentEvent.uid === 2763 || currentEvent.userType === 100)) {
                const resolvedGiftId = currentEvent.message.match(/g:(\d+)/)
                if (resolvedGiftId) {
                  addGiftEffectQueue({
                    giftId: Number(resolvedGiftId[1]),
                    amount: 1,
                  })
                }

                const resolvedEffectId = currentEvent.message.match(/e:(\d+)/)
                if (resolvedEffectId) {
                  addGiftEffectQueue({
                    effectId: Number(resolvedEffectId[1]),
                    amount: 1,
                  })
                }
              }
            }
          }
          delQueue(1)
        }
      }, timeoutFactor)

      return () => clearTimeout(timer)
    }
  }, [
    queue,
    addEvent,
    delQueue,
    shouldScrollToBottom,
    options,
    eventRate,
    dashboardLiveGuards,
    updateDashboardLiveGuards,
    showGiftEffect,
    showGiftEffectAbove,
    addGiftEffectQueue,
  ])

  useEffect(() => {
    setTimeout(() => {
      eventCountRef.current = events.length
    }, 1000)
  }, [events])

  useEffect(() => {
    if (events && events.length > 0) {
      setEventRate(events.length - eventCountRef.current)
    }
  }, [eventCountRef, events])

  // Auto scroll event for OBS widget
  useEffect(() => {
    if (processedObsEvents && processedObsEvents.length > 0) {
      // console.log(`eventRate`, eventRate)

      if (!scrollable && viewportRef.current) {
        const { writingMode } = window.getComputedStyle(viewportRef.current)

        // https://people.igalia.com/fwang/scrollable-elements-in-non-default-writing-modes/
        const scrollLeft =
          writingMode === 'vertical-lr'
            ? viewportRef.current.scrollWidth
            : writingMode === 'vertical-rl'
              ? -viewportRef.current.scrollWidth
              : undefined

        viewportRef.current.scrollTo({
          top: viewportRef.current.scrollHeight,
          left: scrollLeft,
          // 3 为正常情况下的平滑滚动参数
          behavior: eventRate > 6 ? 'auto' : 'smooth',

          // 15 为 Aug 3, 2023 之后b站开始对弹幕便秘后的参数
          // behavior: eventRate > 15 ? 'auto' : 'smooth'
        })
      }
    }
  }, [scrollable, processedObsEvents, eventRate])

  const handleScroll = () => {
    if (viewportRef.current) {
      // TODO: does not work
      // const lastChild = viewportRef.current.querySelector('[data-radix-scroll-area-viewport] > div *:last-child');
      // const lastChildHeight = (lastChild as HTMLElement)?.offsetHeight || 100;

      const { scrollTop, scrollHeight, offsetHeight } = viewportRef.current
      const isAtBottom = scrollTop + offsetHeight >= scrollHeight - 250
      setShouldScrollToBottom(isAtBottom)
    }
  }

  return (
    <>
      {processedObsEvents && processedObsEvents.length > 0 && (
        <div
          className={clsx(
            styles.eventListWrap,
            'events-list-wrap',
            'in-obs',
            colorScheme && `scheme-${colorScheme}`,
            sceneName && `scene-${sceneName}`,
            className
          )}
          {...rest}
        >
          {/* Gift effect player, safely fallback to 5440 if roomsInfo is not available */}
          {showGiftEffect && roomsInfo && roomsInfo[0]?.data && (
            <GiftEffectPlayer roomId={roomsInfo[0].data?.room_info.room_id || 5440} showLayer={showGiftEffectLayer} />
          )}

          {/* Event rate debugger */}
          {/* {eventRate} {'events/s'} */}

          {/* Sticky gift bar */}
          {showStickyBar && processedEventsStickyGift && processedEventsStickyGift.length > 0 && (
            <Flipper
              className={clsx(styles.giftStickyBar, scrollable && styles.scrollable, 'gift-sticky-bar slim-scrollbar')}
              flipKey={processedEventsStickyGift}
              spring={{ stiffness: 1000, damping: 100 }}
            >
              {processedEventsStickyGift.map(event => (
                <Flipped key={event.id} flipId={event.id}>
                  <EventItem key={event.id} data={event} as={'sticky'} />
                </Flipped>
              ))}
            </Flipper>
          )}

          {/* All event list */}
          <ScrollArea
            viewportRef={viewportRef}
            className={clsx(styles.eventList, 'event-list', `connection-mode--${connectionMode}`)}
            // onScroll={handleScroll}
            onScrollPositionChange={scrollable ? handleScroll : undefined}
            scrollbarSize={0}
          >
            {processedObsEvents.map(event => (
              <EventItem key={event.id} data={event} />
            ))}
          </ScrollArea>
        </div>
      )}
    </>
  )
}
