import { useEffect, useState } from 'react'
import type { SimpleEmotesList } from '@laplace.live/internal'

import { fetchRemoteEmotes, remoteEmotesDomainWhitelist } from '@/utils/fetchRemoteEmotes'

import useLocation from '@/hooks/useLocation'

function useRemoteEmotes(rooms: number[]) {
  const [remoteEmotes, setRemoteEmotes] = useState<SimpleEmotesList | undefined>()
  const { hostname } = useLocation()

  useEffect(() => {
    if (hostname && rooms && rooms.length > 0) {
      rooms.map(async (room, idx) => {
        if (idx === 0) {
          const resp = remoteEmotesDomainWhitelist.includes(hostname) && (await fetchRemoteEmotes(room))

          if (resp) {
            setRemoteEmotes(resp)
          }
        }
      })
    }
  }, [rooms, hostname, setRemoteEmotes])

  return {
    remoteEmotes,
  }
}

export default useRemoteEmotes
