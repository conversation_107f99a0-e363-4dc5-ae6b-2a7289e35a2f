import React, { useEffect, useRef, useState } from 'react'
import clsx from 'clsx'

import { nf } from '@/utils/numberFormat'

const AnimatedNumber = ({ value, className = '' }: { value: number; className?: string }) => {
  const [isAnimating, setIsAnimating] = useState(false)
  const [isIncreasing, setIsIncreasing] = useState(false)
  const prevValueRef = useRef(value)

  useEffect(() => {
    if (value !== prevValueRef.current) {
      setIsAnimating(true)
      setIsIncreasing(value > prevValueRef.current)

      const timer = setTimeout(() => {
        setIsAnimating(false)
      }, 1000)

      prevValueRef.current = value
      return () => clearTimeout(timer)
    }
  }, [value])

  return (
    <span
      className={clsx(
        className,
        isAnimating && (isIncreasing ? 'text-emerald-500 transition-none' : 'text-rose-500 transition-none'),
        'transition-colors duration-1000'
      )}
    >
      {nf.format(value)}
    </span>
  )
}

export default AnimatedNumber
