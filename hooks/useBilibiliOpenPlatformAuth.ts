import useSWR, { type Fetcher } from 'swr'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<BilibiliInternal.HTTPS.OpenPlatform.AppStart> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while requesting the auth data')
  }
  return response.json()
}

// 给首页配置器用的
function useBilibiliOpenPlatformAuth(roomId: string) {
  const sanitizedQuery = roomId.replace(/ /g, '')

  const { data, error, isLoading } = useSWR(
    sanitizedQuery ? `${Api.Heartbeater}/bilibili-open/auth/${sanitizedQuery}` : null,
    fetcher,
    {
      refreshInterval: 0,
      revalidateOnFocus: false,
    }
  )

  return {
    roomInfoOpenPlatform: data,
    isRoomInfoLoadingOpenPlatform: isLoading,
    isRoomInfoErrorOpenPlatform: error,
  }
}

export default useBilibiliOpenPlatformAuth
