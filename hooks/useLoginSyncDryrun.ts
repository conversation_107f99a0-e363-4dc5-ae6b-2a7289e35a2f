import useSWR, { type Fetcher } from 'swr'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

import { filterEmptyParams } from '@/utils/filterEmptyParams'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<BilibiliInternal.HTTPS.Prod.Nav> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching login sync data')
  }
  return response.json()
}

function useLoginSyncDryrun(syncToken: string, syncServer: string) {
  const token = syncToken?.trim()
  const params = {
    loginSyncServer: syncServer || '',
  }

  const urlParams = filterEmptyParams(params)

  const tokenUrl = `${Api.Workers}/bilibili/validate-login-sync/${token}?${urlParams}`

  const { data, error, isLoading, mutate, isValidating } = useSWR(token ? `${tokenUrl}` : null, fetcher, {
    refreshInterval: 0,
    revalidateOnFocus: false,
  })

  return {
    dataFromLoginSyncDryrun: data,
    isLoginSyncDryrunLoading: isLoading,
    isLoginSyncDryrunError: error,
    isLoginSyncDryrunValidating: isValidating,
    loginSyncDryrunMutate: mutate,
  }
}

export default useLoginSyncDryrun
